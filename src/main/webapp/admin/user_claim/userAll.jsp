<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%--
    Document   : user_all
    Product    : Intranet - UA Intranet & Common Auth. System.
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Created on : Nov 22, 2010, 2:32:34 PM
    Author     : <PERSON><PERSON>
--%>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@page import="com.misyn.mcms.dbconfig.DbRecordCommonFunction" %>
<%@page import="java.util.List" %>
<jsp:useBean id="UserManagerBean" class="com.misyn.mcms.admin.UserManager" scope="application"/>
<%
    String butClass = "class=\"button\" onmouseover=\"className='button_dwn';\" onmouseout=\"className='button';\"";
    boolean isSearchPage = false;

    String ADD_RIGHT = "disabled='disabled'";
    String DELETE_RIGHT = "disabled='disabled'";
    if (((String) session.getAttribute("RIGHT_D")).equals("checked")) {
        DELETE_RIGHT = "";
    }


    try {
        isSearchPage = Boolean.parseBoolean(request.getParameter("P_ISSEARCH_PAGE"));
    } catch (Exception e) {
        isSearchPage = false;
    }

    session.removeAttribute("ISSEARCH_PAGE");
    session.setAttribute("ISSEARCH_PAGE", isSearchPage);


    long timeURL = System.currentTimeMillis();
    String URL = "userAllResult.jsp?" + timeURL;

    int n_comid = -1;

    try {
        n_comid = Integer.parseInt(request.getParameter("P_N_COMID"));
    } catch (Exception e) {
    }

    int n_usrcode = -1;
    String str_n_comid_popList = "";
    String str_n_accessusrtype_popList = "";
    String str_n_team_id_popList = "";
    String str_v_district_code_popList = "";
    String strAssessorReportingList = "";
    String strRteLvl2 = "";
    String strRteLvl3 = "";
    String strRteLvl4 = "";

    String str_branch_code_and_name_popList = "";
    String str_authority_limit_popList = "";

    int size = 0;
    List<User> list = null;
    User find_user = null;
    String searchKey = "";

    boolean isNewRecord = true;
    try {
        isNewRecord = Boolean.parseBoolean(request.getParameter("P_ISNEWRECORD"));
    } catch (Exception e) {
    }

    try {
        n_usrcode = Integer.parseInt(request.getParameter("P_N_USRCODE"));
    } catch (Exception e) {
    }

    str_n_team_id_popList = DbRecordCommonFunction.getInstance().
            getPopupList("claim_channel_team ", "N_TEAM_ID", "CONCAT(V_TEAM_NAME,' - ',V_CHANNEL_DESC)", "", "");

    str_v_district_code_popList = DbRecordCommonFunction.getInstance().
            getPopupList("claim_district ", "V_DISTRICT_CODE", "V_DISTRICT_NAME", "", "");

    strAssessorReportingList = DbRecordCommonFunction.getInstance().
            getPopupList("usr_mst ", "v_usrid", "v_usrid", "n_accessusrtype IN(21,22,23,24)", "");

    str_branch_code_and_name_popList = DbRecordCommonFunction.getInstance().
            getPopupList("branch_mst ", "branch_code", "CONCAT(branch_code,' - ',branch_name)", "record_status IN('A','E')", "");

    str_authority_limit_popList = DbRecordCommonFunction.getInstance().
            getPopupList("user_authority_limit", "level_id", "CONCAT(level_name,' - ',' From(Rs.) ',from_limit,' To(Rs.) ',to_limit)", "WHERE department_id=4");

    strRteLvl2 = DbRecordCommonFunction.getInstance().
            getPopupList("usr_mst ", "v_usrid", "v_usrid", "n_accessusrtype IN(22,23,24) AND n_auth_level=2", "");

    strRteLvl3 = DbRecordCommonFunction.getInstance().
            getPopupList("usr_mst ", "v_usrid", "v_usrid", "n_accessusrtype IN(22,23,24) AND n_auth_level=3", "");

    strRteLvl4 = DbRecordCommonFunction.getInstance().
            getPopupList("usr_mst ", "v_usrid", "v_usrid", "n_accessusrtype IN(22,23,24) AND n_auth_level=4", "");

    if (isNewRecord) {

        session.removeAttribute("IS_NEW_RECORD");
        session.setAttribute("IS_NEW_RECORD", isNewRecord);

        if (((String) session.getAttribute("RIGHT_I")).equals("checked")) {
            ADD_RIGHT = "";
        }

        find_user = new User();

        if (user.getN_accessusrtype() == 1) {
            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "", "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "", "");


        } else if (user.getN_accessusrtype() == 2) {
            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "", "");
            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype NOT IN(1,2)", "");


        } else if (user.getN_accessusrtype() == 3) {
            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid =" + user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(6)", "");


        } else if (user.getN_accessusrtype() == 4) {
            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=" + user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(7)", "");


        } else if (user.getN_accessusrtype() == 5) {
            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=" + user.getN_comid(), "");
            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(0)", "");


        } else {
            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=-1", "");
            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(-1)", "");

        }

    } else {
        session.removeAttribute("IS_NEW_RECORD");
        session.setAttribute("IS_NEW_RECORD", isNewRecord);

        if (((String) session.getAttribute("RIGHT_M")).equals("checked")) {
            ADD_RIGHT = "";
        }

        //readOnlyClass = " readonly class=\"textReadOnly\" ";
        if (user.getN_accessusrtype() == 1) {
            searchKey = " AND n_usrcode=" + n_usrcode;
            list = UserManagerBean.getUserList(searchKey);
            find_user = list.get(0);

            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid =" + find_user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "", "");


        } else if (user.getN_accessusrtype() == 2) {
            searchKey = "AND n_usrcode=" + n_usrcode + " AND n_accessusrtype NOT IN(1,2)";
            list = UserManagerBean.getUserList(searchKey);
            find_user = list.get(0);

            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid =" + find_user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype NOT IN(1,2)", "");


        } else if (user.getN_accessusrtype() == 3) {
            searchKey = "AND u.n_usrcode=" + n_usrcode + " AND u.n_accessusrtype IN(6) AND u.n_comid=" + user.getN_comid();
            list = UserManagerBean.getUserList(searchKey);
            find_user = list.get(0);


            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid =" + find_user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(6)", "");

        } else if (user.getN_accessusrtype() == 4) {
            searchKey = "AND u.n_usrcode=" + n_usrcode + " AND u.n_accessusrtype IN(7) AND u.n_comid=" + user.getN_comid();
            list = UserManagerBean.getUserList(searchKey);
            find_user = list.get(0);

            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=" + find_user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(7)", "");


        } else if (user.getN_accessusrtype() == 5) {
            searchKey = "AND u.n_usrcode=" + n_usrcode + " AND u.n_accessusrtype IN(8) AND u.n_comid=" + user.getN_comid();
            list = UserManagerBean.getUserList(searchKey);
            find_user = list.get(0);

            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=" + find_user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(0)", "");

        }
    }

    /*
     * if (list != null) { if (list.size() != 0) { find_user = list.get(0); }
            }
     */
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/common/ListBox.js"></script>
    <script type="text/javascript" src="jsAjaxMultPopup2Key.js"></script>
    <script type="text/javascript" src="jsAjaxPopup2Key.js"></script>
    <link href="${pageContext.request.contextPath}/css/common/fb_form/fb_form_col.css" rel="stylesheet"
          type="text/css"/>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/css/common/fb_form/custom-form-elements.js"></script>
    <link href="${pageContext.request.contextPath}/css/jquery_css/theme_orange/jquery-ui-1.8.2.custom.css"
          rel="stylesheet" type="text/css"/>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery-1.4.2.min.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery-ui-1.8.2.custom.min.js"></script>
    <script type="text/javascript">
        var timeUrl = new Date().getDate();
        var ajaxMultiSelectPopup1 = new AjaxMultiSelectPopup('txtSearchV_usrtypes', 'hidV_usrtypes', 'lstV_usrtypes', "popupJSP/ajaxV_USRTYPES.jsp");
        var ajaxMultiSelectPopup2 = new AjaxMultiSelectPopup('txtSearchV_group_ids', 'hidV_group_ids', 'lstV_group_ids', "popupJSP/ajaxV_GROUP_IDs.jsp");

        var ajaxPopup = new AjaxPopup('txtV_asscode', 'hideV_asscode', 'lstV_asscode', "popupJSP/ajaxV_assessor_code.jsp");

        //--> For Dialog Box
        function setConfirmbox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    buttons: {
                        "No": function () {
                            $(this).dialog("close");
                        },
                        "Yes": function () {
                            $(this).dialog("close");
                            document.frmCampaign.action = "CampaignResult.jsp?" + timeUrl;
                            document.frmCampaign.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    return false;
                });

            });
        }

        function setDialogBox() {
            $(document).ready(function () {
                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    buttons: {
                        "Ok": function () {
                            $(this).dialog("close");
                            //$(this).dialog({show: 'explode'});

                            if ($("select#txtN_comid").val() == "-1") {
                                $("select#txtN_comid").focus();
                                return;
                            }
                            if ($("input#txtV_usrid").val() == "") {
                                $("input#txtV_usrid").focus();
                                return;
                            }
                            if ($("select#txtN_accessusrtype").val() == "-1") {
                                $("select#txtN_accessusrtype").focus();
                                return;
                            }
                            if ($("input#txtV_usrtypes").val() == "") {
                                $("input#txtV_usrtypes").focus();
                                return;
                            }
                            if ($("input#txtV_password").val() == "") {
                                $("input#txtV_password").focus();
                                return;
                            }
                            if ($("input#txtV_confirm_password").val() == "") {
                                $("input#txtV_confirm_password").focus();
                                return;
                            }
                            //if($("select#txtV_title").val()==""){$("select#txtV_title").focus();return;}
                            if ($("input#txtV_firstname").val() == "") {
                                $("input#txtV_firstname").focus();
                                return;
                            }
                            if ($("input#txtV_lastname").val() == "") {
                                $("input#txtV_lastname").focus();
                                return;
                            }
                            if ($("input#txtV_email").val() == "") {
                                $("input#txtV_email").focus();
                                return;
                            }
                            if (document.getElementById("txtN_accessusrtype").value == 20) {
                                if (document.getElementById("hideV_asscode").value == "") {
                                    if ($("input#txtV_asscode").val() == "") {
                                        $("input#txtV_asscode").focus();
                                        return;
                                    }

                                }
                                if (document.getElementById("txtV_asscode").value == "Invalid") {
                                    if ($("input#txtV_asscode").val() == "Invalid") {
                                        $("input#txtV_asscode").focus();
                                        return;
                                    }

                                }
                            } else {
                                if ($("input#txtV_emp_no").val() == "") {
                                    $("input#txtV_emp_no").focus();
                                    return;
                                }
                            }
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    return false;
                });
            });
        }

        function showConfirmbox(str) {
            setConfirmbox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        function showDialogbox(str) {
            setDialogBox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        //--> End Dialog Box
        function init() {
            var isNew =<%=isNewRecord%>;
            if (isNew) {
                ajaxMultiSelectPopup2.ViewParameterList('viewUserParameterList.jsp', 0);
            } else {
                ajaxMultiSelectPopup2.ViewParameterList('viewUserParameterList.jsp', "<%= find_user.getV_group_ids()%>");
            }
            hideDiv_EmpCode_OR_AssCode();
            hideDIV_V_channel_code_DIV_V_report_to();

        }

        function refresh_n_comid() {
            var p_n_comid = document.getElementById("txtN_comid").value;
            var isNew =<%=isNewRecord%>;
            if (isNew) document.frmForm.action = "userAll.jsp?" + timeUrl + "&P_N_COMID=" + p_n_comid + "&P_ISNEWRECORD=true";
            else document.frmForm.action = "userAll.jsp?" + timeUrl + "&P_N_COMID=" + p_n_comid;

            document.frmForm.submit();
        }

        function displayDialogBox(type,str) {
            return new Promise((resolve) => {
                $("p#dialog-email").html(str);
                $('#dialog').dialog({
                    autoOpen: true,
                    resizable: false,
                    height: "auto",
                    width: 400,
                    modal: true,
                    buttons: {
                        "Yes": function() {
                            $(this).dialog("close");
                            resolve(true); // Resolving the promise with true if user selects 'Yes'
                        },
                        "No": function() {
                            $(this).dialog("close");
                            resolve(false); // Resolving the promise with false if user selects 'No'
                        }
                    }
                });
            });
        }

        function pageSubmit(type) {

            if (type == 'Save') {
                if (document.getElementById("txtN_comid").value == "-1") {
                    showDialogbox("Please Select Company Code");
                    return;
                } else if (Trim(document.getElementById("txtV_usrid").value) == "") {
                    showDialogbox("Please Enter User Id");
                    return;
                } else if (document.getElementById("txtN_accessusrtype").value == "-1") {
                    showDialogbox("Please Select Access User Level");
                    return;
                } else if (Trim(document.getElementById("txtV_usrtypes").value) == "") {
                    showDialogbox("Please Select User Role");
                    return;
                } else if (Trim(document.getElementById("txtV_password").value) == "") {
                    showDialogbox("Please Enter Password");
                    return;
                } else if (Trim(document.getElementById("txtV_confirm_password").value) == "") {
                    showDialogbox("Please Enter Confirm Password");
                    return;
                } else if (Trim(document.getElementById("txtV_confirm_password").value) != Trim(document.getElementById("txtV_password").value)) {
                    document.getElementById("txtV_confirm_password").value = "";
                    document.getElementById("txtV_password").value = "";
                    showDialogbox("The Password You Typed Do Not Match.Please Retype the New Password in Both Boxes.");
                    return;
                }

                /* else if(document.getElementById("txtV_title").value=="")
                {
                    showDialogbox("Please Select Title");
                    return;
                }*/

                else if (document.getElementById("txtV_firstname").value == "") {
                    showDialogbox("Please Enter First Name");
                    return;
                } else if (document.getElementById("txtV_lastname").value == "") {
                    showDialogbox("Please Enter Last Name");
                    return;
                } else if (document.getElementById("txtV_email").value == "") {
                    showDialogbox("Please Enter Email Address");
                    return;
                } else if (!isValidEmail(document.getElementById("txtV_email").value)) {
                    showDialogbox("Invalid Email Address");
                    return;
                } else if (document.getElementById("txtN_accessusrtype").value == "20" &&
                    document.getElementById("txtVAssessorReportTo").value == "") {
                    showDialogbox("Please Select Reporting User");
                    return;
                } else if (!isValidLandPhoneNumber(document.getElementById("txtV_land_phone").value)) {
                    showDialogbox("Invalid Land Phone Number");
                    return;
                } else if (!isValidMobileNumber(document.getElementById("txtV_mobile").value)) {
                    showDialogbox("Invalid Mobile Number ");
                    return;
                }

                if (document.getElementById("txtN_accessusrtype").value == 20) {
                    if (document.getElementById("hideV_asscode").value == "") {
                        showDialogbox("Please Enter Valid Assessor Code");
                        return;
                    }
                    if (document.getElementById("assessor_type").value == "") {
                        showDialogbox("Please Select Assessor Type");
                        return;
                    }
                } else if (document.getElementById("txtN_accessusrtype").value == 41) {
                    if (document.getElementById("txtN_TEAM_ID").value == 0) {
                        showDialogbox("Please Select a Team");
                        return;
                    }
                } else if ((document.getElementById("txtN_accessusrtype").value == 100)) {
                    if (document.getElementById("txtV_branchCode").value == "") {
                        showDialogbox("Please Select a Branch");
                        return;
                    }
                } else if (document.getElementById("txtN_accessusrtype").value == 22 || document.getElementById("txtN_accessusrtype").value == 23 || document.getElementById("txtN_accessusrtype").value == 24) {
                    if (document.getElementById("sel_ReserveLimit").value == 0) {
                        showDialogbox("Please Select Reserve Limits");
                        return;
                    } else {
                        if (document.getElementById("sel_ReserveLimit").value == 1) {
                            if (document.getElementById("sel_RteLevel_2").value == 0 || document.getElementById("sel_RteLevel_3").value == 0 || document.getElementById("sel_RteLevel_4").value == 0) {
                                showDialogbox("Please Assign Users for Corresponding Levels");
                                return;
                            }
                        }
                        if (document.getElementById("sel_ReserveLimit").value == 2) {
                            if (document.getElementById("sel_RteLevel_3").value == 0 || document.getElementById("sel_RteLevel_4").value == 0) {
                                showDialogbox("Please Assign Users for Corresponding Levels");
                                return;
                            }
                        }
                        if (document.getElementById("sel_ReserveLimit").value == 3) {
                            if (document.getElementById("sel_RteLevel_4").value == 0) {
                                showDialogbox("Please Assign Users for Corresponding Levels");
                                return;
                            }
                        }
                    }
                } else {
                    if (document.getElementById("txtV_emp_no").value == "") {
                        showDialogbox("Please Enter Employee No");
                        return;
                    }
                }
                if (txtV_usrstatus.value === 'C') {
                    displayDialogBox('Save', 'Please check and reassign all assigned task for this user ! ,Are you sure you want to continue?').then((confirmed) => {
                        if (confirmed) {
                            parent.document.getElementById("loading").style.display = "block";
                            parent.document.getElementById("cell1").style.display = "block";
                            document.getElementById("cmdSave").style.cursor = 'wait';
                            document.frmForm.cmdSave.disabled = true;
                            document.frmForm.cmdClose.disabled = true;
                            document.frmForm.txtN_TEAM_ID.disabled = false;
                            document.frmForm.action = "<%=URL%>";
                            document.frmForm.submit();
                        }
                    });
                } else {
                    parent.document.getElementById("loading").style.display = "block";
                    parent.document.getElementById("cell1").style.display = "block";
                    document.getElementById("cmdSave").style.cursor = 'wait';
                    document.frmForm.cmdSave.disabled = true;
                    document.frmForm.cmdClose.disabled = true;
                    document.frmForm.txtN_TEAM_ID.disabled = false;
                    document.frmForm.action = "<%=URL%>";
                    document.frmForm.submit();
                }

            } else if (type == 'Delete') {
                displayDialogBox('Delete', 'Please check and reassign all assigned task for this user ! ,Are you sure you want to continue?')
                    .then((confirm) => {
                        if (!confirm) return;
                        document.frmForm.action = "userDelete.jsp?<%=timeURL%>&P_TYPE=1";
                        document.frmForm.submit();
                    });

            } else if (type == 'Close') {
                var isSearchPage =<%=isSearchPage%>;
                if (isSearchPage == true) window.location = "searchUser.jsp?<%=timeURL%>";
                else window.location = "userAllList.jsp?<%=timeURL%>";

            }

        }

        // Validations
        function isValidEmail(email) {
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function isValidMobileNumber(mobileNumber) {
            if (mobileNumber.trim() == "") {
                return true;
            } else {
                var mobileNumberRegex = /^(07\d{8})$/;
                return mobileNumberRegex.test(mobileNumber);
            }

        }

        function isValidLandPhoneNumber(phoneNumber) {
            if (phoneNumber.trim() == "") {
                return true;
            } else {
                var phoneRegex = /^(0[1-9])\d{8}$/;
                return phoneRegex.test(phoneNumber);
            }
        }


        function Trim(str) {
            while (str.substring(0, 1) == ' ') // check for white spaces from beginning
            {
                str = str.substring(1, str.length);
            }
            while (str.substring(str.length - 1, str.length) == ' ') // check white space from end
            {
                str = str.substring(0, str.length - 1);
            }

            return str;
        }

        function loadChannels() {
            let contextPath = "${pageContext.request.contextPath}";
            const teamId = $("#txtN_TEAM_ID").val();
            if (teamId != 0) {
                $.ajax({
                    url: contextPath + "/UserManagementController/getChannelsForTeam?team=" + teamId,
                    type: 'post',
                    success: function (data) {
                        let resp = JSON.parse(data);
                        console.log(resp);
                    }
                })
            }
        }

        function hideDiv_EmpCode_OR_AssCode() {
            if (document.getElementById("txtN_accessusrtype").value == 20) {
                document.getElementById("DivEmpCode").className = "row divHide";
                document.getElementById("DivAssCode").className = "row";
                document.getElementById("firstNameLabel").innerHTML = "Calling Name";

            } else {
                document.getElementById("DivAssCode").className = "row divHide";
                document.getElementById("DivEmpCode").className = "row";
                document.getElementById("firstNameLabel").innerHTML = "First Name";
            }

            if (document.getElementById("txtN_accessusrtype").value != 22 && document.getElementById("txtN_accessusrtype").value != 23 && document.getElementById("txtN_accessusrtype").value != 24) {
                document.getElementById("divRteLvl2").className = "row divHide";
                document.getElementById("divRteLvl3").className = "row divHide";
                document.getElementById("divRteLvl4").className = "row divHide";
            }

            hideDIV_V_channel_code_DIV_V_report_to();

            hideDiv_branch_code();

            hideDiv_needToSendEmail();
        }

        function hideDiv_branch_code() {
            if ((document.getElementById("txtN_accessusrtype").value == 100)) {
                document.getElementById("DivBranchCode").className = "row";
            } else {
                document.getElementById("DivBranchCode").className = "row divHide";
            }
        }

        function hideDiv_needToSendEmail() {
            if ((document.getElementById("txtN_accessusrtype").value == 42) || (document.getElementById("txtN_accessusrtype").value == 62)) {
                document.getElementById("needToSendEmailDiv").className = "row";
            } else {
                document.getElementById("needToSendEmailDiv").className = "row divHide";
            }
        }

        function hideDIV_V_channel_code_DIV_V_report_to() {
            //alert(document.getElementById("txtN_accessusrtype").value);
            if ((document.getElementById("txtN_accessusrtype").value == 42)
                || (document.getElementById("txtN_accessusrtype").value == 43)
                || (document.getElementById("txtN_accessusrtype").value == 44)
                || (document.getElementById("txtN_accessusrtype").value == 41)
                || (document.getElementById("txtN_accessusrtype").value == 62)
                || (document.getElementById("txtN_accessusrtype").value == 63)) {
                document.getElementById("DIV_V_channel_code").className = "row";
                document.getElementById("DIV_V_report_to").className = "row";
                document.getElementById("DIV_N_liablity_limit").className = "row";
                document.getElementById("DIV_N_payment_limit1").className = "row";
                document.getElementById("DIV_N_payment_limit2").className = "row";
                document.getElementById("DIV_N_payment_limit3").className = "row";
                document.getElementById("DIV_N_payment_limit4").className = "row divHide";

            } else if ((document.getElementById("txtN_accessusrtype").value == 22)
                || (document.getElementById("txtN_accessusrtype").value == 23)
                || (document.getElementById("txtN_accessusrtype").value == 24)) {
                document.getElementById("DIV_V_channel_code").className = "row divHide";
                document.getElementById("DIV_V_report_to").className = "row divHide";
                document.getElementById("DIV_N_liablity_limit").className = "row divHide";
                document.getElementById("DIV_N_payment_limit1").className = "row divHide";
                document.getElementById("DIV_N_payment_limit2").className = "row divHide";

                document.getElementById("DIV_N_payment_limit3").className = "row divHide";
                document.getElementById("DIV_N_payment_limit4").className = "row";
            } else {
                document.getElementById("DIV_V_channel_code").className = "row divHide";
                document.getElementById("DIV_V_report_to").className = "row divHide";
                document.getElementById("DIV_N_liablity_limit").className = "row divHide";
                document.getElementById("DIV_N_payment_limit1").className = "row divHide";
                document.getElementById("DIV_N_payment_limit2").className = "row divHide";
                document.getElementById("DIV_N_payment_limit3").className = "row divHide";
                document.getElementById("DIV_N_payment_limit4").className = "row divHide";
            }
        }

        function selectAuthorityLimit() {
            var authLevel = $('#sel_ReserveLimit').val();
            if (authLevel == 1) {
                document.getElementById("divRteLvl2").className = "row";
                document.getElementById("divRteLvl3").className = "row";
                document.getElementById("divRteLvl4").className = "row";
            } else if (authLevel == 2) {
                $("#sel_RteLevel_2").val('');
                document.getElementById("divRteLvl2").className = "row divHide";
                document.getElementById("divRteLvl3").className = "row";
                document.getElementById("divRteLvl4").className = "row";
            } else if (authLevel == 3) {
                $("#sel_RteLevel_2").val('');
                $("#sel_RteLevel_3").val('');
                document.getElementById("divRteLvl2").className = "row divHide";
                document.getElementById("divRteLvl3").className = "row divHide";
                document.getElementById("divRteLvl4").className = "row";
            } else {
                $("#sel_RteLevel_2").val('');
                $("#sel_RteLevel_3").val('');
                $("#sel_RteLevel_4").val('');
                document.getElementById("divRteLvl2").className = "row divHide";
                document.getElementById("divRteLvl3").className = "row divHide";
                document.getElementById("divRteLvl4").className = "row divHide";
            }
        }
    </script>
    <style>
        *, ::after, ::before {
            box-sizing: border-box;
        }

        .container {
            max-width: 1024px;
            margin: auto
        }

        .col_half {
            width: 50%;
            float: left;
            padding: 0;
        }

        .col_half fieldset {
            border: 0;
            margin: 0;
        }

        .row {
            padding: 5px 0 !important;
            width: 100%;
            position: relative;
        }

        .label {
            padding-top: 5px;
            margin-bottom: 0;
            font-size: inherit;
            line-height: 1.5;
            width: 30% !important;
            float: left;
        }

        .send-email {
            padding-top: 5px;
            margin-bottom: 0;
            line-height: 1.5;
            text-align: right;
            font-family: Arial;
            font-size: 11px;
            font-weight: bold;
            color: #666666;
        }

        .txt_cont {
            width: 70%;
            float: left;
            padding-left: 10px;
        }

        .txt_cont .form-control {
            display: block;
            width: 100%;
            font-size: 12px;
            line-height: 1.5;
            color: rgb(73, 80, 87);
            background-color: rgb(255, 255, 255);
            background-clip: padding-box;
            padding: 0.375rem 0.75rem;
            border-width: 1px;
            border-style: solid;
            border-color: rgb(206, 212, 218);
            border-image: initial;
            border-radius: 0.25rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .multiDivTag > div {
            width: 100% !important;
        }

        .but_container {
            text-align: right;
        }

        .txt_cont .drop_down_btn, .txt_cont .drop_down_btn_up {
            position: absolute;
            top: 6px;
            right: 2px;
            width: auto;
            border-radius: 0 3px 3px 0;
            border: 0;
            font-size: 8px;
            color: #495056;
            padding: 10px 5px;
            cursor: pointer;
        }

        .col_half .button {
            background-image: none;
            padding: 3px 10px;
            box-shadow: none !important;
            background-color: #005AAA;
            border-color: #015AAA;
            display: inline-block;
            font-weight: 400;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            color: #ffffff;
            font-size: 12px;
            line-height: 1.5;
            border-radius: .25rem;
            transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
            height: auto;
        }

        .col_half .button:hover {
            box-shadow: 0 0 5px 0 #555 !important;
        }

        .fbSelectBox {
            border: 1px solid #0C0;
            padding: 3px;
        }

        .selectbox {
            margin: 0 5px 10px 0;
            padding-left: 2px;
            font-family: Verdana, Geneva, Arial, Helvetica, sans-serif;
            font-size: 1em; /* Resize Font*/
            width: 190px; /* Resize Width */
            display: block;
            text-align: left;
            background: url('bg_select.png') right;
            cursor: pointer;
            border: 1px solid #D1E4F6;
            color: #333;
        }

        .divHide {
            display: none;
        }
    </style>
</head>
<body onload="init();">
<div class="form_header">User Details</div>
<div class="container">
    <div id="Note" class="noteDivClass">Note : Fields marked with <span style="color:#FF0000">*</span> are mandatory.
    </div>
    <form name="frmForm" action="" method="post">
        <input name="txtN_usrcode" id="txtN_usrcode" value="<%=find_user.getN_usrcode()%>" type="hidden"/>
        <div class="col_half">
            <fieldset>
                <div class="row">
                    <span class="label">Company <span style="color:#D50000">*</span> :</span>
                    <div class="txt_cont">
                        <select name="txtN_comid" id="txtN_comid" class="styled1 form-control"
                                onchange="refresh_n_comid();">
                            <%out.print(str_n_comid_popList);%>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <span class="label">Login Name <span style="color:#D50000">*</span> :</span>
                    <div class="txt_cont">
                        <%
                            if (!"".equalsIgnoreCase(find_user.getV_usrid())) {
                        %>
                        <input name="txtV_usrid" id="txtV_usrid" maxlength="20" readonly title="User Id" type="text"
                               class="form-control"
                               value="<%=find_user.getV_usrid()%>"/>
                        <%
                        } else {%>
                        <input name="txtV_usrid" id="txtV_usrid" maxlength="20" title="User Id" type="text"
                               class="form-control"
                               value="<%=find_user.getV_usrid()%>"/>
                        <%
                            }%>

                    </div>
                </div>
                <div class="row">
                    <span class="label">Department <span style="color:#D50000">*</span>: </span>
                    <div class="txt_cont">
                        <select name="txtN_accessusrtype" id="txtN_accessusrtype" class="form-control"
                                onchange="hideDiv_EmpCode_OR_AssCode();">
                            <%out.print(str_n_accessusrtype_popList);%>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <span class="label">User Role <span style="color:#D50000">*</span> :</span>
                    <div class="txt_cont">
                        <input class="dropdown form-control" size="25" name="txtV_usrtypes" readonly="readonly"
                               value="<%= UserManagerBean.getTextValue(find_user.getV_usrtype_desc())%>"
                               id="txtV_usrtypes" type="text"/>
                        <input type="button" class="drop_down_btn" name="cmdV_usrtypes" id="cmdV_usrtypes"
                               value="&#x25BC;" onclick="ajaxMultiSelectPopup1.onClick_ToggleButton(event,true)"/>
                        <div class="multiDivTag" name="lstV_usrtypes" id="lstV_usrtypes">
                            <input class="listtxt form-control" name="txtSearchV_usrtypes" id="txtSearchV_usrtypes"
                                   style="display:none;" type="text"
                                   onkeyup="ajaxMultiSelectPopup1.showPopupListMenu(event,false)"/>
                        </div>
                        <input name="hidV_usrtypes" id="hidV_usrtypes" type="hidden"
                               value="<%= find_user.getV_usrtypes()%>"/>
                    </div>
                </div>
                <div class="row" style="display:none;">
                    <span class="label">User Group :</span>
                    <div class="txt_cont">
                        <input class="dropdown form-control" size="25" name="txtV_group_ids" readonly="readonly"
                               value="<%= UserManagerBean.getTextValue(find_user.getV_group_ids_desc())%>"
                               id="txtV_group_ids" type="text"/>
                        <input type="button" class="drop_down_btn" name="cmdV_group_ids" id="cmdV_group_ids"
                               value="&#x25BC;" onclick="ajaxMultiSelectPopup2.onClick_ToggleButton(event,true)"/>
                        <div class="multiDivTag" name="lstV_group_ids" id="lstV_group_ids"
                             style="z-index:1000;">
                            <input class="listtxt" name="txtSearchV_group_ids" id="txtSearchV_group_ids"
                                   style="display:none;" type="text"
                                   onkeyup="ajaxMultiSelectPopup2.showPopupListMenu(event,false)"/>
                        </div>
                        <input name="hidV_group_ids" id="hidV_group_ids" type="hidden"
                               value="<%= find_user.getV_group_ids()%>"/>
                    </div>
                </div>
                <div class="row">
                    <span class="label">Password <span style="color:#D50000">*</span> :</span>
                    <div class="txt_cont">
                        <input name="txtV_password" id="txtV_password" maxlength="15" type="password"
                               class="form-control"
                               value="<%=find_user.getV_real_password()%>"/>
                    </div>
                </div>
                <div class="row">
                    <span class="label">Confirm Password <span style="color:#D50000">*</span> :</span>
                    <div class="txt_cont">
                        <input name="txtV_confirm_password" id="txtV_confirm_password" maxlength="15"
                               class="form-control"
                               title="test" type="password" value="<%=find_user.getV_real_password()%>"/>
                    </div>
                </div>
                <div class="row">
                    <span class="label">User Status <span style="color:#D50000">*</span> :</span>
                    <div class="txt_cont">
                        <%if (isNewRecord) {%>
                        <select name="txtV_usrstatus" id="txtV_usrstatus" class="styled1 form-control">
                            <option value="X">Active</option>
                            <option value="D">Disabled</option>
                            <option value="L">Locked</option>
                            <option value="C">Delete</option>
                        </select>
                        <%
                        } else {

                            if (find_user.getV_usrstatus().equalsIgnoreCase("A")) {
                        %>
                        <select name="txtV_usrstatus" id="txtV_usrstatus" class="styled1 form-control">
                            <option value="A">Active</option>
                            <option value="D">Disabled</option>
                            <option value="L">Locked</option>
                            <option value="C">Delete</option>
                        </select>
                        <% } else {%>
                        <select name="txtV_usrstatus" id="txtV_usrstatus" class="styled1 form-control">
                            <option value="X">Active</option>
                            <option value="D">Disabled</option>
                            <option value="L">Locked</option>
                            <option value="C">Delete</option>
                        </select>
                        <% }
                        }%>
                    </div>
                </div>
                <div class="row" id="divRteLvl2">
                    <span class="label">User (RTE Level 2) <span style="color:#D50000">*</span> :</span>
                    <div class="txt_cont">
                        <select name="sel_RteLevel_2" id="sel_RteLevel_2" class="styled1 form-control">
                            <option value="0">Please Select</option>
                            <%out.print(strRteLvl2);%>
                        </select>
                    </div>
                </div>
                <div class="row" id="divRteLvl3">
                    <span class="label">User (RTE Level 3) <span style="color:#D50000">*</span> :</span>
                    <div class="txt_cont">
                        <select name="sel_RteLevel_3" id="sel_RteLevel_3" class="styled1 form-control">
                            <option value="0">Please Select</option>
                            <%out.print(strRteLvl3);%>
                        </select>
                    </div>
                </div>
                <div class="row" id="divRteLvl4">
                    <span class="label">User (RTE Level 4) <span style="color:#D50000">*</span> :</span>
                    <div class="txt_cont">
                        <select name="sel_RteLevel_4" id="sel_RteLevel_4" class="styled1 form-control">
                            <option value="0">Please Select</option>
                            <%out.print(strRteLvl4);%>
                        </select>
                    </div>
                </div>
            </fieldset>
            <fieldset>
                <div id="paraListView"> Parameter List</div>
            </fieldset>
        </div>
        <div class="col_half">
            <fieldset>
                <div class="row">
                    <span class="label">Title :</span>
                    <div class="txt_cont">
                        <select name="txtV_title" id="txtV_title" class="styled1 form-control">
                            <option value="">Please select one</option>
                            <option value="Rev">Rev</option>
                            <option value="Dr">Dr</option>
                            <option value="Mr">Mr</option>
                            <option value="Mrs">Mrs</option>
                            <option value="Ms">Ms</option>
                            <option value="M/S">M/S</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <span class="label" id="firstNameLabel">First Name <span style="color:#D50000">*</span> :</span>
                    <div class="txt_cont">
                        <input name="txtV_firstname" id="txtV_firstname" maxlength="40" title="test" type="text"
                               class="form-control"
                               value="<%=find_user.getV_firstname()%>"/>
                    </div>
                </div>
                <div class="row">
                    <span class="label">Last Name <span style="color:#D50000">*</span> :</span>
                    <div class="txt_cont">
                        <input name="txtV_lastname" id="txtV_lastname" maxlength="20" title="test" type="text"
                               class="form-control"
                               value="<%=find_user.getV_lastname()%>"/>
                    </div>
                </div>
                <div id="DivBranchCode" class="row">
                    <span class="label">Branch Code <span style="color:#D50000">*</span> :</span>
                    <div class="txt_cont">
                        <select name="branch_code" id="txtV_branchCode" class="form-control">
                            <option value="">Please select one</option>
                            <%out.print(str_branch_code_and_name_popList);%>
                        </select>
                    </div>
                </div>
                <div id="DivEmpCode" class="row">
                    <span class="label">Emp. Code <span style="color:#D50000">*</span> :</span>
                    <div class="txt_cont">
                        <input name="txtV_emp_no" id="txtV_emp_no" maxlength="20" title="test" type="text"
                               class="form-control"
                               value="<%=find_user.getV_emp_no()%>"/>
                    </div>
                </div>
                <div id="DivAssCode" class="row">
                    <span class="label">Assessor Code <span style="color:#D50000">*</span> :</span>
                    <div class="txt_cont">
                        <input name="txtV_asscode" id="txtV_asscode" style="width:240px; float: left;"
                               title="Assessor Code" class="form-control"
                               type="text" value="<%=find_user.getV_ass_name()%>"
                               onkeyup="ajaxPopup.showPopupListMenu(event,false);"/>
                        <input type="button" class=" drop_down_btn" name="cmdV_asscode"
                               style="    right: auto;left: 378px;"
                               id="cmdV_asscode" tabindex="1000" value="&#x25BC;"
                               onclick="ajaxPopup.showPopupListMenu(event,true)"/>
                        <label id="lblV_asscode"
                               style="font-size:12px; font-weight:bold; color:#0C0; float: left; padding: 8px 2px;"><%=find_user.getV_emp_no().equals("") ? "Invalid Assessor Code" : find_user.getV_emp_no()%>
                        </label>
                        <input name="hideV_asscode" id="hideV_asscode" type="hidden"
                               value="<%=find_user.getV_emp_no()%>"/>
                        <div>
                            <select name="lstV_asscode" class="lstbox" style="display:none;" size="10"
                                    id="lstV_asscode"
                                    ondblclick="ajaxPopup.selectLstBox();" onblur="ajaxPopup.onhide();"
                                    onkeyup="ajaxPopup.EnterKeyLst(event)">
                            </select>
                        </div>
                    </div>

                    <%--add dropdown to select ass type--%>

                    <div class="row">
                        <span class="label">Assessor Type  <span style="color:#D50000">*</span>: </span>
                        <div class="txt_cont">
                            <select name="assessor_type" id="assessor_type" class="form-control">
                                <option value="">Please select one</option>
                                <option value="FREELANCE">Freelance</option>
                                <option value="PERMANENT">Permanent</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <span class="label">Assign District  <span style="color:#D50000">*</span>: </span>
                        <div class="txt_cont">
                            <select name="txtV_district_code" id="txtV_district_code" class="form-control"
                                    onchange="hideDiv_EmpCode_OR_AssCode();">
                                <%out.print(str_v_district_code_popList);%>
                            </select>
                        </div>
                        <script type="text/javascript">
                            document.getElementById("txtV_district_code").value = "<%=find_user.getV_district_code()%>";
                        </script>
                    </div>
<%--                    <div class="row" id="DIVAssessorReportTo">--%>
<%--                        <span class="label" style="color: #1c7430;">Reporting To :</span>--%>
<%--                        <div class="txt_cont">--%>
<%--                            <select name="txtVAssessorReportTo" id="txtVAssessorReportTo" class="form-control">--%>
<%--                                <option value="">Please select one</option>--%>
<%--                                <%out.print(strAssessorReportingList);%>--%>
<%--                            </select>--%>
<%--                        </div>--%>
<%--                        <script type="text/javascript">--%>
<%--                            document.getElementById("txtVAssessorReportTo").value = "<%=find_user.getReportingTo()%>";--%>
<%--                        </script>--%>
<%--                    </div>--%>
                </div>
                <div class="row" id="DIV_V_channel_code">
                    <span class="label" style="color:#D50000">Team ID :</span>
                    <div class="txt_cont">
                        <%--                        <select name="txtN_TEAM_ID" id="txtN_TEAM_ID" class="form-control" onchange="loadChannels()">--%>
                        <select name="txtN_TEAM_ID" id="txtN_TEAM_ID" class="form-control">
                            <%out.print(str_n_team_id_popList);%>
                        </select>
                    </div>
                </div>
                <div class="row" id="DIV_N_liablity_limit">
                    <span class="label" style="color: #D50000;">Liability Limit :</span>
                    <div class="txt_cont">
                        <input name="txtN_liablity_limit" id="txtN_liablity_limit" type="text" class="form-control"
                               value="<%=find_user.getN_liablity_limit()%>"/>
                    </div>
                </div>
                <div class="row" id="DIV_N_payment_limit1">
                    <span class="label" style="color: #D50000;">Payment Limit :</span>
                    <div class="txt_cont">
                        <input name="txtN_payment_limit" id="txtN_payment_limit" type="text" class="form-control"
                               value="<%=find_user.getN_payment_limit()%>"/>
                    </div>
                </div>
                <div class="row" id="DIV_N_payment_limit2">
                    <span class="label" style="color: #D50000;">Payment Auth Limit :</span>
                    <div class="txt_cont">
                        <input name="txtN_payment_auth_limit" id="txtN_payment_auth_limit" type="text"
                               class="form-control"
                               value="<%=find_user.getN_payment_auth_limit()%>"/>
                    </div>
                </div>
                <div class="row" id="DIV_N_payment_limit3">
                    <span class="label" style="color: #D50000;">Reserve Limit :</span>
                    <div class="txt_cont">
                        <input name="txtN_reserve_limit" id="txtN_reserve_limit" type="text" class="form-control"
                               value="<%=find_user.getN_reserve_limit()%>"/>
                    </div>
                </div>
                <div class="row" id="DIV_N_payment_limit4">
                    <span class="label" style="color: #D50000;">Reserve Limit :</span>
                    <div class="txt_cont">
                        <select name="sel_ReserveLimit" id="sel_ReserveLimit" class="styled1 form-control"
                                onchange="selectAuthorityLimit()">
                            <option value="0">Please Select</option>
                            <%out.print(str_authority_limit_popList);%>
                        </select>
                    </div>
                </div>
<%--                <div class="row" id="DIV_V_report_to">--%>
<%--                    <span class="label" style="color: #D50000;">Reporting To :</span>--%>
<%--                    <div class="txt_cont">--%>
<%--                        <input name="txtV_report_to" id="txtV_report_to" type="text" class="form-control"--%>
<%--                               value="<%=find_user.getReportingTo()%>"/>--%>
<%--                    </div>--%>
<%--                </div>--%>
                <div class="row">
                    <span class="label">Email <span style="color:#D50000">*</span> :</span>
                    <div class="txt_cont">
                        <input name="txtV_email" id="txtV_email" type="text" class="form-control"
                               value="<%=find_user.getV_email()%>"/>
                    </div>
                </div>
                <div class="row" id="needToSendEmailDiv">
                    <input type="hidden" id="txtNeedToSendEmail" value="<%=find_user.getNeedToSendEmail()%>"/>
                    <span class="label"></span>
                    <label class="label" style="padding-left:10px">
                        <input name="needToSendEmail" id="needToSendEmail" type="checkbox"
                               class="align-middle checkbox_check"
                               value="Y"/>
                        <span class="custom-control-indicator"></span>
                        <span class="send-email">Need to send email</span>
                    </label>
                    <script type="text/javascript">
                        if ('Y' == $("#txtNeedToSendEmail").val()) {
                            $("#needToSendEmail").attr('checked', true)
                        } else {
                            $("#needToSendEmail").attr('checked', false)
                        }
                    </script>
                </div>
                <div class="row">
                    <span class="label">Address  :</span>
                    <div class="txt_cont">
                        <input name="txtV_address1" id="txtV_address1" type="text" class="form-control"
                               value="<%=find_user.getV_address1()%>"/>
                        <div style="height:5px;"></div>
                        <input name="txtV_address2" id="txtV_address2" type="text" class="form-control"
                               value="<%=find_user.getV_address2()%>"/>
                    </div>
                </div>
            </fieldset>
            <fieldset>
                <div class="row">
                    <span class="label">Land Phone:</span>
                    <div class="txt_cont">
                        <input name="txtV_land_phone" id="txtV_land_phone" title="test" type="text" class="form-control"
                               value="<%=find_user.getV_land_phone()%>"/>
                    </div>
                </div>
                <div class="row">
                    <span class="label">Mobile Phone:</span>
                    <div class="txt_cont">
                        <input name="txtV_mobile" id="txtV_mobile" maxlength="20" title="test" type="text"
                               class="form-control"
                               value="<%=find_user.getV_mobile()%>"/>
                    </div>
                </div>
                <div class="row">
                    <span class="label">NIC/Passport:</span>
                    <div class="txt_cont">
                        <input name="txtV_nic" id="txtV_nic" maxlength="20" title="test" type="text"
                               class="form-control"
                               value="<%=find_user.getV_nic()%>"/>
                    </div>
                </div>
            </fieldset>
            <div class="but_container">
                <input type="button" name="cmdSave" id="cmdSave" value="Save Changes"
                       onclick="pageSubmit('Save')" <%=ADD_RIGHT%> class="button"/>
                <input type="button" name="cmdDelete" id="cmdDelete" value="Delete"
                       onclick="pageSubmit('Delete')" <%=DELETE_RIGHT%> class="button"/>
                <input type="button" name="cmdClose" id="cmdClose" value="Close" onclick="pageSubmit('Close')"
                       style="background: #888; border: none; color: #fff;"
                       class="button"/>
            </div>
        </div>
        <% if (!isNewRecord) {
        %>
        <script type="text/javascript">
            document.getElementById("txtN_accessusrtype").value =<%=find_user.getN_accessusrtype()%>;
            document.getElementById("txtV_title").value = "<%=find_user.getV_title()%>";
            document.getElementById("txtV_usrstatus").value = "<%=find_user.getV_usrstatus()%>";
            document.getElementById("txtN_TEAM_ID").value = "<%=find_user.getN_team_id()%>";
            document.getElementById("assessor_type").value = "<%=find_user.getAssessorType()%>";
            document.getElementById("txtV_branchCode").value = "<%=find_user.getBranchCode()%>";

            if (document.getElementById("txtN_accessusrtype").value == 22 || document.getElementById("txtN_accessusrtype").value == 23 || document.getElementById("txtN_accessusrtype").value == 24) {
                document.getElementById("sel_ReserveLimit").value = "<%=find_user.getRteReserveLimitLevel()%>";
                document.getElementById("divRteLvl2").className = "row";
                document.getElementById("divRteLvl3").className = "row";
                document.getElementById("divRteLvl4").className = "row";
                document.getElementById("sel_RteLevel_2").value = "<%=find_user.getRteLevel2()%>";
                document.getElementById("sel_RteLevel_3").value = "<%=find_user.getRteLevel3()%>";
                document.getElementById("sel_RteLevel_4").value = "<%=find_user.getRteLevel4()%>";

                if (null == $("#sel_RteLevel_2").val() || undefined == $("#sel_RteLevel_2").val()) {
                    $("#sel_RteLevel_2").val(0);
                }
                if (null == $("#sel_RteLevel_3").val() || undefined == $("#sel_RteLevel_3").val()) {
                    $("#sel_RteLevel_3").val(0);
                }
                if (null == $("#sel_RteLevel_4").val() || undefined == $("#sel_RteLevel_4").val()) {
                    $("#sel_RteLevel_4").val(0);
                }
                selectAuthorityLimit();
            }

            if (<%=find_user.getN_team_id() != 0%>) {
                document.frmForm.txtN_TEAM_ID.disabled = true;
            } else {
                document.frmForm.txtN_TEAM_ID.disabled = false;
            }
        </script>
        <%} else {%>
        <script type="text/javascript">
            document.getElementById("txtN_comid").value = "<%=n_comid%>";
        </script>
        <%}%>
        <div id="dialog" style="display:none; font-size: 12px;" title="${CompanyTitle}">
            <p><span class="ui-icon ui-icon-info" style="float:left; margin:0 7px 0 0;"></span></p>
            <p id="dialog-email" class="textGrey"></p>
        </div>
    </form>
    <div class="spacer"> &nbsp;</div>
</div>
</body>
</html>
