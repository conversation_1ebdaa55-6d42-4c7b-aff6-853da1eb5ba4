<%@ page import="com.misyn.mcms.dbconfig.DbRecordCommonFunction" %>
<%@ page import="com.misyn.mcms.admin.admin.dto.ProductDetailListDto" %><%--
    Document   : policy
    Created on : Feb 11, 2011, 12:01:25 PM
    Product    : Aviva Claim System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 1.0
--%>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<%@ taglib prefix="fn" uri="jakarta.tags.functions" %>
<jsp:useBean id="DbRecordCommonFunctionBean" class="com.misyn.mcms.dbconfig.DbRecordCommonFunction"
             scope="session"/>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%
    String spCoodList = DbRecordCommonFunction.getInstance().
            getPopupList("usr_mst", "userId", "userId", "accessUserType=27", "");

    String scrList = DbRecordCommonFunction.getInstance().
            getPopupList("usr_mst", "userId", "userId", "accessUserType=28", "");
%>

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link href="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.css" rel="stylesheet">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>

    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/callcenter/policypage.js?v8"></script>
    <%-- Generic page styles --%>
    <%-- The jQuery UI widget factory, can be omitted if jQuery UI is already included --%>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/vendor/jquery.ui.widget.js"></script>
    <%-- The Iframe Transport is required for browsers without support for XHR file uploads --%>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery.popupWindow.js"></script>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.iframe-transport.js"></script>
    <%-- The basic File Upload plugin --%>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/jquery-scrollto.js"></script>
    <c:set var="claimsDto" value="${claimHandlerDto.claimsDto}" scope="request"/>
    <jsp:include page="/WEB-INF/jsp/claim/common/policyDetailsModal.jsp"></jsp:include>
    <c:set var="previousClaim" value="${IS_PREVIOUS_CLAIM}" scope="request"/>
    <c:set var="rejectionDocTypeId" value="80"/>
    <style>
        .bootbox.modal {
            z-index: 9999 !important;
        }

        @media all and (device-width: 768px) and (device-height: 1024px) and (orientation: portrait) {
            .responsive_tab {
                height: calc(100vh - 600px);
            }
        }

        @media all and (device-width: 768px) and (device-height: 1024px) and (orientation: landscape) {
            .responsive_tab {
                height: calc(100vh - 600px);
            }
        }

        .responsive_tab {
            height: calc(100vh - 210px);
        }
    </style>
    <script type="text/javascript">
        var documentUploadIds = [];
        var rejectionDocumentUploadId = [];
        var isOther = "false";
        $(document).ready(function () {
            loadDocumentUploadView();
            loadDefineDocumentView();
            loadGenerateReminderView();
            loadInvestigationView();
            loadDriverDetailView();
            loadSuppyOrderView();
            rejectedDocumentView();
            claimSummaryView();
            previousClaimsView();
            loadEngineeringDocuments();
            loadPanelDecision();

            $("#CLAIM_HANDLER_MESSAGE_TYPE").val("");
            $("#CLAIM_HANDLER_MESSAGE").val("");

        });

        //loadInvestigationView();

        let broadcast = new BroadcastChannel('tab_reload');
        broadcast.onmessage = function (e) {
            if (e.data == 'reload') {
                loadDefineDocumentView();
                loadDocumentUploadView();
            }
        }

        function loadDocumentUploadView() {

            showLoader();
            isOther = "false";
            $("#documentUploadContainer").load("${pageContext.request.contextPath}/ClaimHandlerController/documentUpload?P_N_CLIM_NO=${claimsDto.claimNo}");
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
            hideLoader();
            loadInvestigationView();
            refreshClaimView();
        }

        function loadDefineDocumentView() {
            var searchDocumentName = "";
            try {
                searchDocumentName = document.getElementById("searchDocumentName").value;
            } catch (e) {

            }
            $("#claimDefineDocumentContainer").load("${pageContext.request.contextPath}/ClaimHandlerController/defineDocument", {
                "DOC_NAME": searchDocumentName,
                "P_N_CLIM_NO": "${claimsDto.claimNo}"
            });

            refreshClaimView();
        }

        function loadEngineeringDocuments() {
            $("#engDocUploadContainer").load("${pageContext.request.contextPath}/ClaimHandlerController/viewEngDocUpload?PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}&P_N_CLIM_NO=${claimsDto.claimNo}");
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
        }

        function loadGenerateReminderView() {
            $("#claimGenerateReminderContainer").load("${pageContext.request.contextPath}/ClaimHandlerController/viewGenerateReminder?P_N_CLIM_NO=${claimsDto.claimNo}");
        }

        function loadInvestigationView() {
            $("#claimInvestigationContainer").load("${pageContext.request.contextPath}/ClaimHandlerController/viewInvestigation?P_N_CLIM_NO=${claimsDto.claimNo}");
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
        }

        function loadDriverDetailView() {
            $("#driverDetailContainer").load("${pageContext.request.contextPath}/ClaimHandlerController/viewDriverDetails?P_N_CLIM_NO=${claimsDto.claimNo}");
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
        }

        function loadSuppyOrderView() {
            var refNo = ${DO_REF_NO};
            if (0 == refNo) {
                $("#claimSupplyOrderContainer").load("${pageContext.request.contextPath}/ClaimHandlerController/viewSupplyOrder?P_N_CLIM_NO=${claimsDto.claimNo}&NOTIFY=true");
            } else {
                $("#claimSupplyOrderContainer").load("${pageContext.request.contextPath}/ClaimHandlerController/viewSupplyOrder?P_N_CLIM_NO=${claimsDto.claimNo}&N_REF_NO=" + refNo + "&NOTIFY=true");
            }
        }


        function rejectedDocumentView() {
            $("#rejDocumentViewContainer").load("${pageContext.request.contextPath}/TestController/rejDocument");
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
        }

        function previousClaimsView() {
            $("#previousClaimsContainer").load("${pageContext.request.contextPath}/ClaimHandlerController/viewPreviousClaims?VEHICLE_NO=${claimsDto.vehicleNo}&P_N_CLIM_NO=${claimsDto.claimNo}");
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
        }

        function claimSummaryView() {
            $("#claimSummaryContainer").load("${pageContext.request.contextPath}/ClaimHandlerController/viewClaimSummary?P_N_CLIM_NO=${claimsDto.claimNo}&POL_STATUS=${claimsDto.policyDto.polStatus}");
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
        }

        function loadPanelDecision() {
            $("#panelDecisionContainer").load("${pageContext.request.contextPath}/ClaimHandlerController/viewPanelDecision?N_CLAIM_NO=${claimsDto.claimNo}");
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
        }
    </script>
    <script type="text/javascript">
        showLoader();

        function resizeIframe(obj) {
            obj.style.height = obj.contentWindow.document.body.scrollHeight + 'px';
        }
    </script>
</head>
<body class="scroll" onload="hideLoader()">
<div class="container-fluid">
    <input id="policyCoverNoteNo"
           name="policyCoverNoteNo"
           type="hidden"
           value="${claimHandlerDto.claimsDto.policyDto.policyNumber}"/>
    <div class="row header-bg bg-dark">
        <div class="col-sm-12 py-2" id="status" >
            <h6 class="float-left text-dark hide-for-small"> Claim Details -
                <c:if test="${claimHandlerDto.assignUserId!=null && claimHandlerDto.assignUserId!=''}">
                    <small>
                            ${UtilityBean.getCustomDateFormat(claimHandlerDto.assignDateTime,"yyyy-MM-dd HH:mm:ss","d MMM yyyy 'at' hh:mm a")}
                    </small>
                    <small>
                        by ${claimHandlerDto.assignUserId}
                    </small>
                </c:if>
                <c:if test="${ (claimHandlerDto.initLiabilityAssignUserId!=null
                    && claimHandlerDto.initLiabilityAssignUserId!='' && claimHandlerDto.assignUserId==null) }">
                    <small>
                            ${UtilityBean.getCustomDateFormat(claimHandlerDto.initLiabilityAssignDateTime,"yyyy-MM-dd HH:mm:ss","d MMM yyyy 'at' hh:mm a")}
                    </small>
                    <small>
                        by ${claimHandlerDto.initLiabilityAssignUserId}
                    </small>
                </c:if>
            </h6>
            <c:if test="${null != claimsDto.claimNo && claimsDto.claimNo!=0  }">
                <c:if test="${claimsDto.policyDto.vehicleNumber != ''}">
                    <h6 class="text-dark float-right">Vehicle No : ${claimsDto.policyDto.vehicleNumber} </h6>
                    <h6 class="text-dark float-right" style="margin-right: 10px">Claim No : ${claimsDto.claimNo}</h6>
                    <p class="text-danger float-right mr-3">ISF Claim No
                        : ${not empty claimsDto.isfClaimNo  ? claimsDto.isfClaimNo:'PENDING'} |</p>
                </c:if>
                <c:if test="${claimsDto.policyDto.vehicleNumber == ''}">
                    <h6 class="text-dark float-right">Cover Note No : ${claimsDto.coverNoteNo} </h6>
                    <h6 class="text-dark float-right" style="margin-right: 10px">Claim No : ${claimsDto.claimNo}</h6>
                    <p class="text-danger float-right mr-3">ISF Claim No
                        : ${not empty claimsDto.isfClaimNo  ? claimsDto.isfClaimNo:'PENDING'} |</p>
                </c:if>
            </c:if>
            <div class="clearfix"></div>
        </div>
    </div>
    <div>
        <div>
            <div class="ErrorNote"></div>
        </div>
        <form name="frmForm" id="frmForm" method="post">
            <input name="P_N_CLIM_NO" id="P_N_CLIM_NO" type="hidden"/>
            <input name="N_TXN_NO" id="N_TXN_NO" type="hidden"/>
            <input name="CLAIM_HANDLER_MESSAGE" id="CLAIM_HANDLER_MESSAGE" type="hidden"/>
            <input name="CLAIM_HANDLER_MESSAGE_TYPE" id="CLAIM_HANDLER_MESSAGE_TYPE" type="hidden"/>
            <input type="hidden" value="${FORM_TYPE}" name="FORM_TYPE"/>
        </form>
        <div class="row">
            <div class="col-sm-12">
                <div id="claimStampContainer" class="stamp-container">
                    <c:if test="${claimsDto.policyDto.categoryDescription eq 'VIP'}">
                        <img src="${pageContext.request.contextPath}/resources/stamps/vip.png"
                             class="stamp-container-vip"
                        >
                    </c:if>
                </div>
                <div class="f1-steps">
                    <div class="f1-progress">
                        <div class="f1-progress-line" data-now-value="10" data-number-of-steps="5"
                             style="width: 90%;"></div>
                    </div>
                    <div class="f1-step active">
                        <div class="f1-step-icon ${claimsDto.claimStatus>=1?"active":""}">1</div>
                        <p>Call Center</p>
                    </div>
                    <div class="f1-step ${claimsDto.claimStatus>=3?"active":""}">
                        <div class="f1-step-icon">2</div>
                        <p>Assessor Coordinator</p>
                    </div>
                    <div class="f1-step active">
                        <div class="f1-step-icon ${claimsDto.claimStatus>=1?"active":""}">3</div>
                        <p>Assessor</p>
                    </div>
                    <div class="f1-step active">
                        <div class="f1-step-icon active">4</div>
                        <p>Motor Engineer</p>
                    </div>
                    <div class="f1-step active">
                        <div class="f1-step-icon">5</div>
                        <p>Claim Handler</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col">
            <div id="Note" class="noteDivClass text-right text-danger">Note : <span
                    class="text-muted font-weight-bold">Fields marked with  <span
                    class="text-danger font-weight-bold"> *</span> are mandatory.</span>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-5 " style="max-height: 500px; overflow-y: auto">
                <div class="card mt-2">
                    <c:if test="${!sessionClaimUserTypeDto.initLiabilityUser && !sessionClaimUserTypeDto.offerTeamInitLiabilityUser}">
                        <fieldset class="border p-2 ">
                            <h6>Claim Details</h6>
                            <hr class="my-2">
                            <ul class="list-group">
                                <c:if test="${isTheftClaim}">
                                    <li class="list-group-item">
                                        <div class="form-group row" id="divTheftAndFound"
                                             style="background-color: deepskyblue">
                                            <label class="col-sm-4 col-form-label"></label>
                                            <div class="col-sm-8">
                                                <div class="row">
                                                    <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 col-sm-12 pl-3 col-form-label check-container">
                                                        <input name="theftAndFound" title="Theft and Found"
                                                               class="align-middle"
                                                               type="checkbox"
                                                               id="theftAndFound"
                                                               onclick="markTheftAndFound(this.checked)"
                                                               value="Y"/>
                                                        <span class="checkmark"></span>
                                                        <span class="custom-control-description">Theft and Found </span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                </c:if>
                                <li class="list-group-item">
                                    <span class="float-left">Vehicle No : </span>
                                    <span class="label_Value float-right">${claimsDto.policyDto.vehicleNumber}</span>
                                </li>
                                <li class="list-group-item">
                                    <span class="float-left">Insured Name : </span>
                                    <span class="label_Value  float-right">${claimsDto.policyDto.custName}</span>
                                </li>
                                <li class="list-group-item">
                                    <span class="float-left">Claim No : </span>
                                    <span class="label_Value  float-right">${claimsDto.claimNo}</span>
                                </li>
                                <li class="list-group-item">
                                    <span>Date Of Loss : </span>
                                    <span class="label_Value float-right">${claimsDto.accidDate}</span>
                                </li>
                            </ul>
                            <div class="form-group row" style="display: none">
                                <label class="col-sm-4 col-form-label"></label>
                                <div class="col-sm-8">
                                    <div class="row">
                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 col-sm-12 pl-3 col-form-label check-container">
                                            <input name="" title="Is Police Report Required"
                                                   class="align-middle checkbox_check"
                                                   type="checkbox"
                                                   value="Y"/>
                                            <span class="checkmark"></span>
                                            <span class="custom-control-description">Is Police Report Required </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                    </c:if>
                    <form name="frmLiabilityCheck" id="frmLiabilityCheck">
                        <fieldset class="border p-2  mt-1">
                            <input type="hidden" name="claimNo" value="${claimsDto.claimNo}">
                            <h6>Liability Check List </h6>
                            <hr class="my-2">
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label"> Name Of insured & Policy No :</label>
                                <div class="col-sm-8">
                                    <div class="row">
                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                            <input name="isLcChk1" type="radio"
                                                   value="Y"
                                                   class="align-middle" ${claimHandlerDto.isLcChk1 eq 'Y' ? 'checked': ''}/>
                                            <span class="radiomark"></span>
                                            <span class="custom-control-description">Yes</span>
                                        </label>
                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                            <input name="isLcChk1" type="radio"
                                                   value="N"
                                                   class="align-middle" ${claimHandlerDto.isLcChk1 eq 'N' ? 'checked': ''}/>
                                            <span class="radiomark"></span>
                                            <span class="custom-control-description">No</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label"> Period Of Cover :</label>
                                <div class="col-sm-8">
                                    <div class="row">
                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                            <input name="isLcChk2" type="radio"
                                                   value="Y"
                                                   class="align-middle" ${claimHandlerDto.isLcChk2 eq 'Y' ? 'checked': ''}/>
                                            <span class="radiomark"></span>
                                            <span class="custom-control-description">Yes</span>
                                        </label>
                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                            <input name="isLcChk2" type="radio"
                                                   value="N"
                                                   class="align-middle" ${claimHandlerDto.isLcChk2 eq 'N' ? 'checked': ''}/>
                                            <span class="radiomark"></span>
                                            <span class="custom-control-description">No</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label"> Premium :</label>
                                <div class="col-sm-8">
                                    <div class="row">
                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                            <input name="isLcChk3" type="radio"
                                                   value="Y"
                                                   class="align-middle"  ${claimHandlerDto.isLcChk3 eq 'Y' ? 'checked': ''}/>
                                            <span class="radiomark"></span>
                                            <span class="custom-control-description">Yes</span>
                                        </label>
                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                            <input name="isLcChk3" type="radio"
                                                   value="N"
                                                   class="align-middle" ${claimHandlerDto.isLcChk3 eq 'N' ? 'checked': ''}/>
                                            <span class="radiomark"></span>
                                            <span class="custom-control-description">No</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label"> Insurable Interest :</label>
                                <div class="col-sm-8">
                                    <div class="row">
                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                            <input name="isLcChk4" type="radio"
                                                   value="Y"
                                                   class="align-middle" ${claimHandlerDto.isLcChk4 eq 'Y' ? 'checked': ''} />
                                            <span class="radiomark"></span>
                                            <span class="custom-control-description">Yes</span>
                                        </label>
                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                            <input name="isLcChk4" type="radio"
                                                   value="N"
                                                   class="align-middle" ${claimHandlerDto.isLcChk4 eq 'N' ? 'checked': ''} />
                                            <span class="radiomark"></span>
                                            <span class="custom-control-description">No</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label"> 3rd party user (Only for
                                    LOLC/CLC/Brac:</label>
                                <div class="col-sm-8">
                                    <div class="row">
                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                            <input name="isLcChk5" type="radio"
                                                   value="Y"
                                                   class="align-middle" ${claimHandlerDto.isLcChk5 eq 'Y' ? 'checked': ''} />
                                            <span class="radiomark"></span>
                                            <span class="custom-control-description">Yes</span>
                                        </label>
                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                            <input name="isLcChk5" type="radio"
                                                   value="N"
                                                   class="align-middle" ${claimHandlerDto.isLcChk5 eq 'N' ? 'checked': ''} />
                                            <span class="radiomark"></span>
                                            <span class="custom-control-description">No</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label"> Driving Licence :</label>
                                <div class="col-sm-8">
                                    <div class="row">
                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                            <input name="isLcChk6" type="radio"
                                                   value="Y"
                                                   class="align-middle" ${claimHandlerDto.isLcChk6 eq 'Y' ? 'checked': ''} />
                                            <span class="radiomark"></span>
                                            <span class="custom-control-description">Yes</span>
                                        </label>
                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                            <input name="isLcChk6" type="radio"
                                                   value="N"
                                                   class="align-middle" ${claimHandlerDto.isLcChk6 eq 'N' ? 'checked': ''}/>
                                            <span class="radiomark"></span>
                                            <span class="custom-control-description">No</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label"> Peril & Nature Of Loss :</label>
                                <div class="col-sm-8">
                                    <div class="row">
                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                            <input name="isLcChk7" type="radio"
                                                   value="Y"
                                                   class="align-middle" ${claimHandlerDto.isLcChk7 eq 'Y' ? 'checked': ''} />
                                            <span class="radiomark"></span>
                                            <span class="custom-control-description">Yes</span>
                                        </label>
                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                            <input name="isLcChk7" type="radio"
                                                   value="N"
                                                   class="align-middle" ${claimHandlerDto.isLcChk7 eq 'N' ? 'checked': ''}/>
                                            <span class="radiomark"></span>
                                            <span class="custom-control-description">No</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label"> Usage :</label>
                                <div class="col-sm-8">
                                    <div class="row">
                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-2 col-form-label check-container">
                                            <input name="isLcChk8" type="radio"
                                                   value="H"
                                                   class="align-middle" ${claimHandlerDto.isLcChk8 eq 'H' ? 'checked': ''} />
                                            <span class="radiomark"></span>
                                            <span class="custom-control-description">Hiring</span>
                                        </label>
                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-2 col-form-label check-container">
                                            <input name="isLcChk8" type="radio"
                                                   value="R"
                                                   class="align-middle" ${claimHandlerDto.isLcChk8 eq 'R' ? 'checked': ''} />
                                            <span class="radiomark"></span>
                                            <span class="custom-control-description">Rent</span>
                                        </label>
                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-2 col-form-label check-container">
                                            <input name="isLcChk8" type="radio"
                                                   value="P"
                                                   class="align-middle" ${claimHandlerDto.isLcChk8 eq 'P' ? 'checked': ''}/>
                                            <span class="radiomark"></span>
                                            <span class="custom-control-description">Private</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group ">
                                <div class="row d-none">
                                    <div class="col-sm-6">
                                        <span>PAV Amount : </span>
                                        <span class="label_Value"></span>
                                    </div>
                                </div>
                            </div>
                            <c:if test="${null eq claimHandlerDto.lcChkUser or claimHandlerDto.lcChkUser eq '' }">
                                <div class="mt-2 text-right mb-3">
                                    <button type="submit" id="checkBtn" name=""
                                            class="btn btn-success ">Checked
                                    </button>
                                    <button type="button" name="" style="display: none"
                                            class="btn btn-success ml-2">Apply Penalty
                                    </button>
                                </div>
                            </c:if>
                        </fieldset>
                    </form>
                    <fieldset class="border p-2 mt-1">
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label"> Repudiated Reason :</label>
                            <div class="col-sm-8">
                                <select class=" form-control form-control-sm" id="repudiatedReason"
                                        name="repudiatedReason" onclick="hideSelctotrs()">
                                    ${DbRecordCommonFunctionBean.getPopupList("claim_repudiated_reason", "N_REF_NO", "V_REPUDIATED_REASON_DESC")}
                                </select>
                                <script type="text/javascript">
                                    $('#repudiatedReason').val('${claimHandlerDto.repudiatedType}');
                                </script>
                            </div>
                        </div>
                    </fieldset>
                    <form name="frmAri" id="frmAri">
                        <c:if test="${IS_TECHNICAL_COORDINATOR || IS_TECHNICAL_COORDINATOR_ARI_ONLY}">
                            <fieldset class="border p-2 mt-1  bg-warning">
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label"> Request ARI :</label>
                                    <div class="col-sm-8">
                                        <div class="row">
                                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                <input name="isRequested" type="radio"
                                                       value="Y" id="chkIsRequested"
                                                       class="align-middle"  ${isRequested eq 'Y' ? 'checked': ''}
                                                />
                                                <span class="radiomark"></span>
                                                <span class="custom-control-description">Yes</span>
                                            </label>
                                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                <input name="isRequested" type="radio"
                                                       value="N"
                                                       class="align-middle"
                                                       id="chkIsNotRequested"
                                                />
                                                <span class="radiomark"></span>
                                                <span class="custom-control-description">No</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-2 text-right">
                                    <c:if test="${isRequested != 'Y'}">
                                        <button type="button" name="" onclick="requestedAri()" id="btnAriRequest"
                                                class="btn btn-success ">Request
                                        </button>
                                    </c:if>
                                </div>
                            </fieldset>
                        </c:if>
                    </form>
                    <form name="frmRemark" id="frmRemark">
                        <fieldset class="border p-2 mt-1">
                            <input type="hidden" name="claimNo" value="${claimsDto.claimNo}">
                            <input type="hidden" id="OUTSTANDING_PREMIUM" name="OUTSTANDING_PREMIUM"
                                   value=""/>
                            <input type="hidden" id="IS_CANCELLED_POLICY" name="IS_CANCELLED_POLICY"
                                   value=""/>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Remark :</label>
                                <div class="col-sm-8">
                                        <textarea name="remark" id="remark"
                                                  class="form-control form-control-sm"></textarea>
                                </div>
                            </div>
                            <div class="mt-2 text-right mb-3">
                                <button type="button" name="" value="SR" onclick="remarkSubmitForm(this)"
                                        class="btn btn-success ml-2">Special Remark
                                </button>
                                <c:if test="${claimHandlerDto.claimStatus==57 && G_USER.userId==claimHandlerDto.specialApprovalUserId}">
                                    <button type="button" name="" value="SC" onclick="remarkSubmitForm(this)"
                                            class="btn btn-warning ml-2">Special Approval Comment
                                    </button>
                                </c:if>
                                <%--<c:if test="${sessionClaimUserTypeDto.branchUser}">--%>
                                <button type="button" name="" value="BR" onclick="remarkSubmitForm(this)"
                                        class="btn btn-info ml-2">Add Branch Remark
                                </button>
                                <%--</c:if>--%>
                            </div>
                        </fieldset>
                        <fieldset class="border p-2 mt-1">
                            <div class="form-group" style="overflow: auto ; ">
                                <p class="m-0"><b>Previous Inspection</b></p>
                                <div class="row">
                                    <div class="col ">
                                        <table width="100%" cellpadding="0" cellspacing="1"
                                               class="table table-hover table-xs dataTable no-footer dtr-inline">
                                            <thead>
                                            <tr>
                                                <th class="tbl_row_header"></th>
                                                <th class="tbl_row_header">Job No</th>
                                                <th class="tbl_row_header">Inspection Type</th>
                                                <th class="tbl_row_header">Assign Assessor</th>
                                                <th class="tbl_row_header">Assign RTE</th>
                                                <th class="tbl_row_header">Forwarded RTE</th>
                                                <th class="tbl_row_header">Approve Date Time</th>
                                                <th class="tbl_row_header">Date Of Accident</th>
                                                <th class="tbl_row_header">Status</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <c:forEach var="claim" items="${previousInspectionList}">
                                                <c:forEach var="jobs" items="${claim.list}" varStatus="loop">
                                                    <tr>
                                                        <td>
                                                            <c:if test="${jobs.recordStatus!=29}">

                                                                <div>
                                                                    <a href="${pageContext.request.contextPath}/MotorEngineerController/viewEditClaimPrevious?P_N_CLIM_NO=${claim.claimNo}&P_N_JOB_NO=${jobs.jobRefNo}&P_POL_N_REF_NO=${jobs.policyRefNo}"
                                                                       class="jobView${loop.index}">
                                                                        <button type="button"
                                                                                name="cmdPrevInspec"
                                                                                class="btn btn-primary">
                                                                            <i class="fa fa-eye"></i>
                                                                        </button>
                                                                    </a>
                                                                </div>
                                                                <script type="text/javascript">
                                                                    var x = $('.jobView${loop.index}').popupWindow({
                                                                        height: screen.height,
                                                                        width: screen.width,
                                                                        resizable: 1,
                                                                        status: 1,
                                                                        centerScreen: 1,
                                                                        scrollbars: 1,
                                                                        top: 1,
                                                                        windowName: 'swip${jobs.jobNo}'
                                                                    });
                                                                </script>

                                                            </c:if>
                                                        </td>
                                                        <td>${jobs.jobNo}</td>
                                                        <td>${jobs.inspectionType}</td>
                                                        <td>${jobs.assignAssessor}</td>
                                                        <td>${jobs.assignRte}</td>
                                                        <td>${jobs.approveAssignRte}</td>
                                                        <td>${jobs.approveDateTime}</td>
                                                        <td>${jobs.dateOfAccident}</td>
                                                        <td>${jobs.statusDesc}</td>
                                                    </tr>
                                                </c:forEach>
                                            </c:forEach>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <p class="m-0"><b>Call Center Details</b></p>
                                <div class="row">
                                    <div class="col">
                                        <table width="100%" cellpadding="0" cellspacing="1"
                                               class="table table-hover table-xs dataTable no-footer dtr-inline">
                                            <thead>
                                            <tr>
                                                <th class="tbl_row_header">Claim No</th>
                                                <th class="tbl_row_header">Vehicle No</th>
                                                <th class="tbl_row_header">Policy No</th>
                                                <th class="tbl_row_header">Date of Accident</th>
                                                <th class="tbl_row_header"></th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr>
                                                <td>${claimsDto.claimNo}</td>
                                                <td>${claimsDto.policyDto.vehicleNumber}</td>
                                                <td>${claimsDto.policyDto.policyNumber}</td>
                                                <td>${claimsDto.accidDate}</td>
                                                <td>
                                                    <div>
                                                        <a href="${pageContext.request.contextPath}/ClaimHandlerController/viewClaimHistory?P_N_CLIM_NO=${claimsDto.claimNo}"
                                                           class="previousView1">
                                                            <button type="button"
                                                                    name="cmdViewAccident"
                                                                    class="btn btn-primary">
                                                                <i class="fa fa-eye"></i>
                                                            </button>
                                                        </a>
                                                    </div>
                                                    <script type="text/javascript">
                                                        $('.previousView1').popupWindow({
                                                            height: screen.height,
                                                            width: screen.width,
                                                            resizable: 1,
                                                            centerScreen: 1,
                                                            scrollbars: 1,
                                                            windowName: '.previousView1'
                                                        });
                                                    </script>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                        <div class="form-group row p-2 mt-1">
                            <label class="col-sm-4 col-form-label">Remark :</label>
                            <div class="col-sm-8">
                            <textarea name="panleRemark" id="panleRemark"
                                      class="form-control form-control-sm"></textarea>
                            </div>
                        </div>
                    </form>


                    <div class="mt-2 text-right mb-3">
                        <form name="frmSuperDashboard" id="frmSuperDashboard" method="post">
                            <input type="hidden" name="P_N_CLIM_NO" id="txtSuperDashboardClaimNo"/>

                            <c:if test="${G_USER.accessUserType eq 1 || G_USER.accessUserType eq 2 || G_USER.accessUserType ge 40}">
                                <div class="float-left ml-1">
                                    <a onclick="viewSuperDashboard(${claimsDto.claimNo})" id="viewSuperDashBoardBtn"
                                       class="btn btn-info ml-2"><b>Super Dashboard</b>
                                    </a>
                                </div>
                            </c:if>
                        </form>
                        <div class="float-right ml-1">
                            <button onclick="goBack()" class="btn btn-link float-right" type="button"><b>Back</b>
                            </button>
                        </div>
                        <div class="float-right ml-1" id="claimHandlerBtnPanel">
                        </div>
                        <c:if test="${IS_SPECIAL_TEAM or IS_OFFER_TEAM_SPECIAL_TEAM or IS_MOFA_TEAM or IS_OFFER_TEAM_MOFA_TEAM}">
                            <div class="float-right ml-1">
                                <button onclick="claimClose();" id="closeBtn" class="btn btn-danger" type="button">Claim
                                    Close
                                </button>

                                <button onclick="claimReopen()" id="reOpenBtn" class="btn btn-warning" type="button">
                                    Claim
                                    Reopen
                                </button>
                            </div>
                        </c:if>


                    </div>
                </div>
            </div>
            <div class="col-md-12 col-lg-7 scroll">
                <div id="accordion2">
                    <div class="card mt-2">
                        <div class="card-header p-0" id="underwritingDetails">
                            <h5 class="mb-0">
                                <a class="btn btn-link" data-toggle="collapse" data-target="#underwriting_Details"
                                   aria-expanded="false" aria-controls="underwriting_Details">
                                    Underwriting Details
                                </a>
                            </h5>
                        </div>
                        <div id="underwriting_Details" class="collapse" aria-labelledby="underwriting_Details"
                             data-parent="#accordion2">
                            <div class="card-body p-0">




                                <fieldset class="border p-2 mt-1">
                                    <h6>Customer Details </h6>
                                    <hr class="my-2">
                                    <div class="form-group ">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span class="float-left">Customer Name : </span>
                                                <span class="label_Value ml-2"
                                                      id="custName">${claimsDto.policyDto.custName}</span>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>Customer NIC Number : </span>
                                                <span class="label_Value"
                                                      id="customerNicNo">${claimsDto.policyDto.custNic} </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group ">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span class="float-left">Contact Address : </span>
                                                <span class="label_Value "
                                                      id="contactAddress">${claimsDto.policyDto.custAddressLine1} </span>
                                                <span class="label_Value"
                                                      id="contactAddress2">${claimsDto.policyDto.custAddressLine2} </span>
                                                <span class="label_Value"
                                                      id="contactAddress3">${claimsDto.policyDto.custAddressLine3} </span>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>Customer Contact Number : </span>
                                                <span class="label_Value "
                                                      id="contactNumber">${claimsDto.policyDto.custMobileNo} </span>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>


                                <fieldset class="border p-2 mt-1">
                                    <%--                                    <div class="form-group" >--%>
                                    <div class="row">
                                        <div class="col-sm-2"
                                             style="display: flex; justify-content: flex-start; align-items: center;">
                                            <span>Product Name : </span>
                                        </div>
                                        <div class="col-sm-8"
                                             style="display: flex; justify-content: center; align-items: center;">
                                            <h6 class="font-weight-bold m-0 p-2 border rounded"
                                                style="background: rgba(135, 206, 250, 0.3); text-align: center;">
                                                ${(claimsDto.policyDto.product eq "") || (null eq claimsDto.policyDto.product) ? "N/A" : claimsDto.policyDto.product }</h6>
                                        </div>
                                        <div class="col-sm-2"
                                             style="display: flex; justify-content: flex-end; align-items: center;">
                                            <button type="button" class="btn btn-primary" name="btnCHPolicyDetails"
                                                    id="btnCHPolicyDetails" data-toggle="modal"
                                                    data-target="#policyDetailsModal">
                                                Policy
                                                Details
                                            </button>
                                        </div>
                                    </div>
                                    <%--                                    </div>--%>
                                </fieldset>

                                <fieldset class="border p-2 mt-1">
                                    <h6> Vehicle Details</h6>
                                    <hr class="my-2">
                                    <div class="form-group ">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>Endorsement Count : </span>
                                                <span class="label_Value"
                                                      id="endorsementCount">${claimsDto.policyDto.endCount}</span>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>Renewal Count :</span>
                                                <span class="label_Value"
                                                      id="renewalCount">${claimsDto.policyDto.renCount}</span>
                                            </div>
                                        </div>
                                        <hr class="m-0 mt-2">
                                    </div>
                                    <div class="form-group ">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>Vehicle No : </span>
                                                <span class="label_Value"
                                                      id="vehicleNo">${claimsDto.policyDto.vehicleNumber}</span>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>Policy Number :</span>
                                                <span class="label_Value"
                                                      id="policyNo">${claimsDto.policyDto.policyNumber}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group ">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>Model Code :</span>
                                                <span class="label_Value"
                                                      id="modelCode">${claimsDto.policyDto.vehicleModel}</span>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>Make Code :</span>
                                                <span class="label_Value"
                                                      id="makeCode">${claimsDto.policyDto.vehicleMake}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>Vehicle Color :</span>
                                                <span class="label_Value">${claimsDto.policyDto.vehicleColor}</span>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>Engine Number :</span>
                                                <span class="label_Value"
                                                      id="engineNo"> ${claimsDto.policyDto.engineNo}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>Chassis Number :</span>
                                                <span class="label_Value"
                                                      id="chassisNo">${claimsDto.policyDto.chassisNo} </span>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>Cover Note No :</span>
                                                <span class="label_Value"
                                                      id="coverNoteNo">${claimsDto.policyDto.coverNoteNo}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>Body Type :</span>
                                                <span class="label_Value"
                                                      id="bodyType">${claimsDto.policyDto.bodyType}</span>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>Fuel Type :</span>
                                                <span class="label_Value"
                                                      id="fuelType">${claimsDto.policyDto.fuelType}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>No of Seats :</span>
                                                <span class="label_Value"
                                                      id="noOfSeats">${claimsDto.policyDto.noOfSeat}</span>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>Capacity Cylinder :</span>
                                                <span class="label_Value"
                                                      id="capacityCylinder ">${claimsDto.policyDto.engCapacity}</span>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>
                                <fieldset class="border p-2 mt-1">
                                    <h6> Policy Details </h6>
                                    <hr class="my-2">
                                    <div class="form-group ">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>Policy Type :</span>
                                                <span class="label_Value"
                                                      id="policyChannelType">${claimsDto.policyDto.policyChannelType}</span>
                                            </div>
                                        </div>
                                        <hr class="m-0 mt-2">
                                    </div>
                                    <div class="form-group ">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>Usage :</span>
                                                <span class="label_Value"
                                                      id="usageofVehicle">${claimsDto.policyDto.vehicleUsage}</span>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>Cover Type :</span>
                                                <span class="label_Value"
                                                      id="coverType">${claimsDto.policyDto.coverType}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group ">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>Sum Insured (Rs.) :</span>
                                                <span class="label_Value" id="sumInsured"><fmt:formatNumber
                                                        value="${claimsDto.policyDto.sumInsured}"
                                                        pattern="###,##0.00;(###,##0.00)"
                                                        type="number"/></span>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>Manufacture Year:</span>
                                                <span class="label_Value"
                                                      id="Manufactureyear">${claimsDto.policyDto.manufactYear}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group ">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>Vehicle Age :</span>
                                                <span class="label_Value"
                                                      id="vehicleAge ">${claimsDto.policyDto.vehicleAge}</span>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>Lease Company and Branch :</span>
                                                <a href="" class="label_Value pointer"
                                                   style="text-decoration: underline;"
                                                   id="leaseCompanyAndBranch" data-toggle="modal"
                                                   data-target="#mortgageDetailsModal">${claimsDto.policyDto.financeCompany}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group ">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>Annual Premium (Premium Break Up) (Rs.) :</span>
                                                <a href="" class="label_Value pointer"
                                                   style="text-decoration: underline;"
                                                   id="annualPremium" data-toggle="modal"
                                                   data-target="#annualpreModal"><fmt:formatNumber
                                                        value="${claimsDto.policyDto.annualPremium}"
                                                        pattern="###,##0.00;(###,##0.00)" type="number"/></a>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>Outstanding Premium (Rs.) :</span>
                                                <span class="label_Value"
                                                      id="premiumOutstanding"><fmt:formatNumber
                                                        value="${claimsDto.policyDto.totPremOutstand}"
                                                        pattern="###,##0.00;(###,##0.00)" type="number"/></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group ">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>Paid Amount (Rs.) :</span>
                                                <a href="" class="label_Value pointer" id="paidAmount"
                                                   data-toggle="modal"
                                                   data-target="#paidAmountModal" style="text-decoration: underline;">
                                                    <fmt:formatNumber value="${claimsDto.policyDto.paidTotalAmount}"
                                                                      pattern="###,##0.00;(###,##0.00)" type="number"/>
                                                </a>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>Days :</span>
                                                <span class="label_Value "
                                                      id="days">${claimsDto.policyDto.noDayPremOutstand}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group ">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>Policy Period :</span>
                                                <span class="label_Value"
                                                      id="policyPeriod">${claimsDto.policyDto.inspecDate}
                                        <c:if test="${claimsDto.policyDto.coverNoteNo ne claimsDto.policyDto.policyNumber}">
                                            to ${claimsDto.policyDto.expireDate}
                                        </c:if></span>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>Policy Status :</span>
                                                <span class="label_Value"
                                                      id="policyStaus">${DbRecordCommonFunctionBean.getValueIdString("claim_policy_status", "V_STATUS_DESC", "V_STATUS_CODE", claimsDto.policyDto.polStatus)}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group ">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>NCB Rate:</span>
                                                <span class="label_Value"
                                                      id="ncbRate">${claimsDto.policyDto.ncbRate}%</span>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>NCB Value (Rs.) :</span>
                                                <span class="label_Value"
                                                      id="ncbAmount">
                                                    <fmt:formatNumber value="${claimsDto.policyDto.ncbAmount}"
                                                                      pattern="###,##0.00;(###,##0.00)" type="number"/>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group ">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>Excess Amount (Rs.) :</span>
                                                <a href="" class="label_Value pointer"
                                                   style="text-decoration: underline;"
                                                   id="excessAmount"
                                                   data-toggle="modal"
                                                   style="text-decoration: underline;"
                                                   data-target="#excessDetailModal">
                                                    <fmt:formatNumber value="${claimsDto.policyDto.excess}"
                                                                      pattern="###,##0.00;(###,##0.00)" type="number"/>
                                                </a>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>Product :</span>
                                                <span class="label_Value"
                                                      id="product">${(claimsDto.policyDto.product eq "") || (null eq claimsDto.policyDto.product) ? "N/A" : claimsDto.policyDto.product }</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group ">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>Last Modified Date :</span>
                                                <span class="label_Value"
                                                      id="lastModifiedDate">${claimsDto.policyDto.lastModifyDate}</span>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>Last Modified User :</span>
                                                <span class="label_Value"
                                                      id="lastModifiedUser">${claimsDto.policyDto.lastModifyUser}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group ">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>Deduction :</span>
                                                <span class="label_Value"
                                                      id="deductions">${claimsDto.policyDto.deduction}</span>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>Business Type :</span>
                                                <span class="label_Value"
                                                      id="businessType">${claimsDto.policyDto.bizType}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group ">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>Transaction Effect :</span>
                                                <span class="label_Value"
                                                      id="transactionEffect">${claimsDto.policyDto.inspecDate}</span>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>Issue Date :</span>
                                                <span class="label_Value"
                                                      id="issueDate">${claimsDto.policyDto.createDate}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group ">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>Expiry status :</span>
                                                <span class="label_Value"
                                                      id="polStatus"></span>
                                            </div>
                                            <script type="text/javascript">
                                                let exstatus = '${DbRecordCommonFunctionBean.getValueIdString("claim_policy_status", "V_STATUS_DESC", "V_STATUS_CODE", claimsDto.policyDto.currentPolStatus)}';
                                                if ('' === exstatus || null == exstatus) {
                                                    $('#polStatus').text('${claimsDto.policyDto.currentPolStatus}');
                                                } else {
                                                    $('#polStatus').text(exstatus);
                                                }
                                            </script>
                                            <div class="col-sm-6">
                                                <span>Branch :</span>
                                                <span class="label_Value"
                                                      id="policyBranch">${claimsDto.policyDto.policyBranch}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group ">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <span>Issue Branch :</span>
                                                <span class="label_Value"
                                                      id="branchCode">${claimsDto.policyDto.branchCode}</span>
                                            </div>
                                            <div class="col-sm-6">
                                                <span>Workflow :</span>
                                                <span class="label_Value"
                                                      id="workflow">${claimsDto.policyDto.workflow}</span>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>
                                <fieldset class="border p-2 mt-1 d-flex justify-content-center">
                                    <button class="btn btn-primary mr-1" type="button" name="btnTrailerDetails"
                                            id="btnTrailerDetails"
                                            onclick="getData('#trailerDetailsModal','TrailerDetails')">Trailer
                                        Details
                                    </button>
                                    <button class="btn btn-primary ml-1" type="button" name="btnTradePlate"
                                            id="btnTradePlate" onclick="getData('#tradePlateModal','TradePlate')">Trade
                                        Plate
                                    </button>
                                </fieldset>
                                <fieldset class="border p-2 mt-1">
                                    <h6> Other Details </h6>
                                    <hr class="my-2">

                                    <div id="policy-memo-card">
                                        <div class="card mt-2">
                                            <div class="card-header p-0" id="PolicyMemo">
                                                <h5 class="mb-0">
                                                    <a class="btn btn-link" data-toggle="collapse"
                                                       data-target="#PolicyMemo_Details"
                                                       aria-expanded="false" aria-controls="PolicyMemo_Details">
                                                        Policy Memo
                                                    </a>
                                                </h5>
                                            </div>

                                            <div id="PolicyMemo_Details" aria-labelledby="PolicyMemo_Details"
                                                 data-parent="#policy-memo-card"
                                                 class="collapse">
                                                <div class="card-body p-0">
                                                    <fieldset class="border p-2 mt-1">
                                                        <div class="form-group">
                                                            <%--                                                <p class="m-0"><b>Policy Memo</b></p>--%>
                                                            <div class="row">
                                                                <div class="col">
                                                                    <table width="100%" cellpadding="0" cellspacing="1"
                                                                           class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                                        <thead>
                                                                        <tr>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Memo
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Position
                                                                                Flag
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Date
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Exclusion
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">Order
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Delete
                                                                            </th>
                                                                        </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                        <c:forEach var="policyMemoDto"
                                                                                   items="${claimsDto.policyDto.policyMemoDtoList}">
                                                                            <tr>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${policyMemoDto.memo}</td>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${policyMemoDto.position}</td>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${policyMemoDto.memoDate}</td>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${policyMemoDto.exclusion}</td>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${policyMemoDto.order}</td>
                                                                                <td scope="row"
                                                                                    class="tbl_row_header">${policyMemoDto.delete eq 'Y' ? '<input type="checkbox" checked onclick="return false;">'
                                                                                        : '<input type="checkbox" readonly onclick="return false;" >'}</td>
                                                                            </tr>
                                                                        </c:forEach>
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </fieldset>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="cwe-detail-card">
                                        <div class="card mt-2">
                                            <div class="card-header p-0" id="CWEDetail">
                                                <h5 class="mb-0">
                                                    <a class="btn btn-link" data-toggle="collapse"
                                                       data-target="#CWE_Detail"
                                                       aria-expanded="false" aria-controls="CWE_Detail">
                                                        CWE Details
                                                    </a>
                                                </h5>
                                            </div>

                                            <div id="CWE_Detail" aria-labelledby="CWE_Detail"
                                                 data-parent="#cwe-detail-card"
                                                 class="collapse">
                                                <div class="card-body p-0">
                                                    <fieldset class="border p-2 mt-1">
                                                        <div class="form-group">
                                                            <%-- <p class="m-0"><b>CWE Detail</b></p>--%>
                                                            <div class="row">
                                                                <div class="col">
                                                                    <table width="100%" cellpadding="0" cellspacing="1"
                                                                           class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                                        <thead>
                                                                        <tr>
                                                                            <th scope="col" class="tbl_row_header"></th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Description
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Entered Date
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Delete
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Deleted Date
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">Order
                                                                                No
                                                                            </th>
                                                                        </tr>
                                                                        </thead>
                                                                        <tbody id="cweBody">
                                                                        <c:forEach var="CweDetailDto"
                                                                                   items="${claimsDto.policyDto.cweDetailDtoList}">
                                                                            <tr>
                                                                                <td scope="col" class="tbl_row_header">
                                                                                    <button class="btn-primary btn btn-sm float-right btn-xs"
                                                                                            data-toggle="modal"
                                                                                            data-target="#${CweDetailDto.order}${CweDetailDto.endorsementCount}"
                                                                                            type="button"
                                                                                            title="Cwe Detail">
                                                                                        <i class="fa fa-plus"
                                                                                           aria-hidden="true"></i>
                                                                                    </button>
                                                                                </td>
                                                                                <td scope="row"
                                                                                    class="tbl_row_header">${CweDetailDto.header}</td>
                                                                                <td scope="row"
                                                                                    class="tbl_row_header">${CweDetailDto.enteredDate}</td>
                                                                                <td scope="row"
                                                                                    class="tbl_row_header">${CweDetailDto.isDeleted eq 'Y' ? '<input type="checkbox" checked onclick="return false;">'
                                                                                        : '<input type="checkbox" readonly onclick="return false;" >'}</td>
                                                                                <td scope="row"
                                                                                    class="tbl_row_header">${CweDetailDto.deletedDate}</td>
                                                                                <td scope="row"
                                                                                    class="tbl_row_header">${CweDetailDto.order}</td>
                                                                            </tr>
                                                                            <div class="modal fade"
                                                                                 id="${CweDetailDto.order}${CweDetailDto.endorsementCount}">
                                                                                <div class="modal-dialog modal-lg"
                                                                                     style="min-width:52%">
                                                                                    <div class="modal-content">
                                                                                        <!-- Modal body -->
                                                                                        <div class="modal-body">
                                                                                            <div>
                                                                                                <h4 class="text-center font-weight-bold">
                                                                                                    Additional
                                                                                                    Information</h4>
                                                                                                <hr/>
                                                                                                <h5 style="line-height: 1.6">
                                                                                                    <pre>${CweDetailDto.text}</pre>
                                                                                                </h5>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </c:forEach>
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </fieldset>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="introducer_detail-card">
                                        <div class="card mt-2">
                                            <div class="card-header p-0" id="IntroducerDetail">
                                                <h5 class="mb-0">
                                                    <a class="btn btn-link" data-toggle="collapse"
                                                       onclick="getIntroducerDetails()"
                                                       data-target="#Introducer_Detail"
                                                       aria-expanded="false" aria-controls="Introducer_Detail">
                                                        Introducer Detail
                                                    </a>
                                                </h5>
                                            </div>

                                            <div id="Introducer_Detail" aria-labelledby="Introducer_Detail"
                                                 data-parent="#introducer_detail-card"
                                                 class="collapse">
                                                <div class="card-body p-0">
                                                    <fieldset class="border p-2 mt-1">
                                                        <div class="form-group">
                                                            <%--                                            <p class="m-0"><b>Introducer Detail</b></p>--%>
                                                            <br>
                                                            <%--                                            <fieldset class="border p-2 mt-1">--%>
                                                            <div class="form-group ">
                                                                <div class="row">
                                                                    <div class="col-sm-6">
                                                                        <span>Introducer Code :</span>
                                                                        <span class="label_Value" id="agentCode"></span>
                                                                    </div>
                                                                    <div class="col-sm-6">
                                                                        <span>Intorducer Name :</span>
                                                                        <span class="label_Value" id="agentName"></span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="form-group ">
                                                                <div class="row">
                                                                    <div class="col-sm-6">
                                                                        <span>Intorducer Mobile Number :</span>
                                                                        <span class="label_Value" id="contactNo"></span>
                                                                    </div>
                                                                    <div class="col-sm-6">
                                                                        <span>Intorducer E-Mail :</span>
                                                                        <span class="label_Value" id="email"></span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <%--                                            </fieldset>--%>
                                                            <div class="row">
                                                                <div class="col">
                                                                    <table width="100%" cellpadding="0" cellspacing="1"
                                                                           class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                                        <thead>
                                                                        <tr>
                                                                            <th scope="col" class="tbl_row_header">Agent
                                                                                Code
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">Agent
                                                                                Name
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Rank
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">Rank
                                                                                Desc.
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Percent
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Status
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">Start
                                                                                Date
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">End
                                                                                Date
                                                                            </th>
                                                                        </tr>
                                                                        </thead>
                                                                        <tbody id="introducerList"></tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </fieldset>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="endorsement-history-card">
                                        <div class="card mt-2">
                                            <div class="card-header p-0" id="EndorsementHistory">
                                                <h5 class="mb-0">
                                                    <a class="btn btn-link" data-toggle="collapse"
                                                       data-target="#Endorsement_History"
                                                       aria-expanded="false" aria-controls="Endorsement_History">
                                                        Endorsement History
                                                    </a>
                                                </h5>
                                            </div>

                                            <div id="Endorsement_History" aria-labelledby="Endorsement_History"
                                                 data-parent="#endorsement-history-card"
                                                 class="collapse">
                                                <div class="card-body p-0">
                                                    <fieldset class="border p-2 mt-1">
                                                        <div class="form-group">
                                                            <%--                                                            <p class="m-0"><b>Endorsement History</b></p>--%>
                                                            <div class="row">
                                                                <div class="col">
                                                                    <table width="100%" cellpadding="0" cellspacing="1"
                                                                           class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                                        <thead>
                                                                        <tr>
                                                                            <th scope="col" class="tbl_row_header ">
                                                                                Policy Number
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Description
                                                                            </th>
                                                                        </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                        <c:forEach var="endorsementHistoryDto"
                                                                                   items="${claimsDto.policyDto.endorsementHistoryDtoList}">
                                                                            <tr>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${endorsementHistoryDto.policyNo}</td>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${endorsementHistoryDto.description}</td>
                                                                            </tr>
                                                                        </c:forEach>
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </fieldset>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="billing-information-card">
                                        <div class="card mt-2">
                                            <div class="card-header p-0" id="BillingInformation">
                                                <h5 class="mb-0">
                                                    <a class="btn btn-link" data-toggle="collapse"
                                                       data-target="#Billing_Information"
                                                       aria-expanded="false" aria-controls="Billing_Information">
                                                        Billing Information
                                                    </a>
                                                </h5>
                                            </div>

                                            <div id="Billing_Information" aria-labelledby="Billing_Information"
                                                 data-parent="#billing-information-card"
                                                 class="collapse">
                                                <div class="card-body p-0">
                                                    <fieldset class="border p-2 mt-1">
                                                        <div class="form-group">
                                                            <%--                                            <p class="m-0"><b>Billing Information</b></p>--%>
                                                            <div class="row">
                                                                <div class="col">
                                                                    <table width="100%" cellpadding="0" cellspacing="1"
                                                                           class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                                        <thead>
                                                                        <tr>
                                                                            <th scope="col" class="tbl_row_header">Bill
                                                                                No
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Renewal Count
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">End
                                                                                Count
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">Bill
                                                                                Amount (Rs.)
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">Bill
                                                                                Date
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">Bill
                                                                                Status
                                                                            </th>
                                                                        </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                        <c:forEach var="billingInfoDto"
                                                                                   items="${claimsDto.policyDto.billingInfoDtoList}">
                                                                            <tr>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${billingInfoDto.billNo}</td>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${billingInfoDto.renewCount}</td>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${billingInfoDto.endCount}
                                                                                </td>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header"
                                                                                    style="text-align: right;">
                                                                                    <fmt:formatNumber
                                                                                            value="${billingInfoDto.billAmount}"
                                                                                            pattern="###,##0.00;(###,##0.00)"
                                                                                            type="number"/>
                                                                                </td>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${billingInfoDto.billDate}
                                                                                </td>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${billingInfoDto.billStatus}
                                                                                </td>
                                                                            </tr>
                                                                        </c:forEach>
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </fieldset>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="claim-history-card">
                                        <div class="card mt-2">
                                            <div class="card-header p-0" id="ClaimHistory">
                                                <h5 class="mb-0">
                                                    <a class="btn btn-link" data-toggle="collapse"
                                                       data-target="#Claim_History"
                                                       aria-expanded="false" aria-controls="Claim_History">
                                                        Claim History
                                                    </a>
                                                </h5>
                                            </div>

                                            <div id="Claim_History" aria-labelledby="Claim_History"
                                                 data-parent="#claim-history-card"
                                                 class="collapse">
                                                <div class="card-body p-0">
                                                    <fieldset class="border p-2 mt-1">
                                                        <hr class="my-2">
                                                        <div class="form-group">
                                                            <%--                                                            <p class="m-0"><b>Claim History</b></p>--%>
                                                            <div class="row">
                                                                <div class="col">
                                                                    <table width="100%" cellpadding="0" cellspacing="1"
                                                                           class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                                        <thead>
                                                                        <tr>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Accident Date
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">Claim
                                                                                No
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">Value
                                                                                Of Claim
                                                                                (Rs.)
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Status
                                                                            </th>
                                                                        </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                        <c:forEach var="claimHistory"
                                                                                   items="${claimsDto.claimHistory}">
                                                                            <c:if test="${claimHistory.claimNo != claimsDto.claimNo}">
                                                                                <tr>
                                                                                    <td scope="col"
                                                                                        class="tbl_row_header">${claimHistory.accidDate}</td>
                                                                                    <td scope="col"
                                                                                        class="tbl_row_header">${claimHistory.claimNo}</td>
                                                                                    <td scope="col"
                                                                                        class="tbl_row_header text-right">
                                                                                            ${claimHistory.totalAcr}
                                                                                    </td>
                                                                                    <td scope="col"
                                                                                        class="tbl_row_header">${DbRecordCommonFunctionBean.getValue("claim_status_para", "v_status_desc", "n_ref_id", claimHistory.claimStatus)}
                                                                                        <button id="${claimHistory.claimNo}"
                                                                                                class="btn-primary btn btn-sm float-right btn-xs"
                                                                                                type="button"
                                                                                                title="View Claim History"
                                                                                                onclick='viewClaimHistory("${claimHistory.polRefNo}","${claimHistory.claimNo}")'>
                                                                                            <i class="fa fa-eye"></i>
                                                                                        </button>
                                                                                    </td>
                                                                                </tr>
                                                                            </c:if>
                                                                        </c:forEach>
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </fieldset>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="benefits-covers-details-card">
                                        <div class="card mt-2">
                                            <div class="card-header p-0" id="Benefits/CoversDetails">
                                                <h5 class="mb-0">
                                                    <a class="btn btn-link" data-toggle="collapse"
                                                       data-target="#Benefits_Covers_Details"
                                                       aria-expanded="false" aria-controls="Benefits_Covers_Details">
                                                        Benefits/Covers Details
                                                    </a>
                                                </h5>
                                            </div>

                                            <div id="Benefits_Covers_Details" aria-labelledby="Benefits_Covers_Details"
                                                 data-parent="#benefits-covers-details-card"
                                                 class="collapse">
                                                <div class="card-body p-0">
                                                    <fieldset class="border p-2 mt-1">
                                                        <hr class="my-2">
                                                        <div class="form-group">
                                                            <%--                                                            <p class="m-0"><b> Benefits/Covers Details</b></p>--%>
                                                            <div class="row">
                                                                <div class="col">
                                                                    <table width="100%" cellpadding="0" cellspacing="1"
                                                                           class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                                        <thead>
                                                                        <tr>
                                                                            <th scope="col" class="tbl_row_header">Cover
                                                                                Details
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">Cover
                                                                                Amount (Rs.)
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Rate%
                                                                            </th>
                                                                        </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                        <c:forEach var="coverDto"
                                                                                   items="${claimsDto.policyDto.coverDtoList}">
                                                                            <tr>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${coverDto.coverDesc}</td>
                                                                                <td scope="col" class="tbl_row_header"
                                                                                    style="text-align: right">
                                                                                    <fmt:formatNumber
                                                                                            value="${coverDto.coverAmount}"
                                                                                            pattern="###,##0.00;(###,##0.00)"
                                                                                            type="number"/>
                                                                                </td>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${coverDto.coverRate}%
                                                                                </td>
                                                                            </tr>
                                                                        </c:forEach>
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </fieldset>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="loading-excess-details-card">
                                        <div class="card mt-2">
                                            <div class="card-header p-0" id="Loading/ExcessDetails">
                                                <h5 class="mb-0">
                                                    <a class="btn btn-link" data-toggle="collapse"
                                                       data-target="#Loading_Excess_Details"
                                                       aria-expanded="false" aria-controls="Loading_Excess_Details">
                                                        Loading/Excess Details
                                                    </a>
                                                </h5>
                                            </div>

                                            <div id="Loading_Excess_Details" aria-labelledby="Loading_Excess_Details"
                                                 data-parent="#loading-excess-details-card"
                                                 class="collapse">
                                                <div class="card-body p-0">
                                                    <fieldset class="border p-2 mt-1">
                                                        <hr class="my-2">
                                                        <div class="form-group">
                                                            <%--                                                            <p class="m-0"><b>Loading/Excess Details</b></p>--%>
                                                            <div class="row">
                                                                <div class="col">
                                                                    <table width="100%" cellpadding="0" cellspacing="1"
                                                                           class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                                        <thead>
                                                                        <tr>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Excess Details
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Excess Amount (Rs.)
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Rate%
                                                                            </th>
                                                                        </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                        <c:forEach var="excessDto"
                                                                                   items="${claimsDto.policyDto.excessDtoList}">
                                                                            <tr>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${excessDto.excessDesc}</td>
                                                                                <td scope="col" class="tbl_row_header"
                                                                                    style="text-align: right;">
                                                                                    <fmt:formatNumber
                                                                                            value="${excessDto.excessAmount}"
                                                                                            pattern="###,##0.00;(###,##0.00)"
                                                                                            type="number"/>
                                                                                </td>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${excessDto.excessRate}%
                                                                                </td>
                                                                            </tr>
                                                                        </c:forEach>
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </fieldset>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="learner-driver-details-card">
                                        <div class="card mt-2">
                                            <div class="card-header p-0" id="LearnerDriverDetails">
                                                <h5 class="mb-0">
                                                    <a class="btn btn-link" data-toggle="collapse"
                                                       data-target="#Learner_Driver_Details"
                                                       aria-expanded="false" aria-controls="Learner_Driver_Details">
                                                        Learner Driver Details
                                                    </a>
                                                </h5>
                                            </div>

                                            <div id="Learner_Driver_Details" aria-labelledby="Learner_Driver_Details"
                                                 data-parent="#learner-driver-details-card"
                                                 class="collapse">
                                                <div class="card-body p-0">
                                                    <fieldset class="border p-2 mt-1">
                                                        <hr class="my-2">
                                                        <div class="form-group">
                                                            <%--                                                            <p class="m-0"><b>Learner Driver Details</b></p>--%>
                                                            <div class="row">
                                                                <div class="col">
                                                                    <table width="100%" cellpadding="0" cellspacing="1"
                                                                           class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                                        <thead>
                                                                        <tr>
                                                                            <th scope="col" class="tbl_row_header">NIC
                                                                                No
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">ID
                                                                                Type
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Name
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">Age
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Premium
                                                                            </th>
                                                                        </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                        <c:forEach var="learnerDriverDetailsDto"
                                                                                   items="${claimsDto.policyDto.learnerDriverDetailsDtoList}">
                                                                            <tr>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${learnerDriverDetailsDto.nicNo}</td>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${learnerDriverDetailsDto.idType}</td>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${learnerDriverDetailsDto.name}
                                                                                </td>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${learnerDriverDetailsDto.age}
                                                                                </td>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header"
                                                                                    style="text-align: right;">
                                                                                    <fmt:formatNumber
                                                                                            value="${learnerDriverDetailsDto.premium}"
                                                                                            pattern="###,##0.00;(###,##0.00)"
                                                                                            type="number"/>
                                                                                </td>
                                                                            </tr>
                                                                        </c:forEach>
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </fieldset>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="ncb-history-details-card">
                                        <div class="card mt-2">
                                            <div class="card-header p-0" id="NCBHistoryDetails">
                                                <h5 class="mb-0">
                                                    <a class="btn btn-link" data-toggle="collapse"
                                                       data-target="#NCB_History_Details"
                                                       aria-expanded="false" aria-controls="NCB_History_Details">
                                                        NCB History Details
                                                    </a>
                                                </h5>
                                            </div>

                                            <div id="NCB_History_Details" aria-labelledby="NCB_History_Details"
                                                 data-parent="#ncb-history-details-card"
                                                 class="collapse">
                                                <div class="card-body p-0">
                                                    <fieldset class="border p-2 mt-1">
                                                        <div class="form-group">
                                                            <%--  <p class="m-0"><b>NCB History Details</b></p>--%>
                                                            <div class="row">
                                                                <div class="col">
                                                                    <table width="100%" cellpadding="0" cellspacing="1"
                                                                           class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                                        <thead>
                                                                        <tr>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Policy Year
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Renewal Count
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Endorsement Count
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">
                                                                                Status
                                                                            </th>
                                                                            <th scope="col" class="tbl_row_header">NCB
                                                                                (%)
                                                                            </th>
                                                                        </tr>
                                                                        </thead>
                                                                        <tbody id="ncdHistory">
                                                                        <c:forEach var="ncbHistoryDto"
                                                                                   items="${claimsDto.policyDto.ncbHistoryDetailsSummary}">
                                                                            <tr>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${ncbHistoryDto.policyYear}</td>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${ncbHistoryDto.renewalCount}</td>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${ncbHistoryDto.endorsementCount}</td>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${ncbHistoryDto.status}</td>
                                                                                <td scope="col"
                                                                                    class="tbl_row_header">${ncbHistoryDto.ncbPer}</td>
                                                                            </tr>
                                                                        </c:forEach>
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </fieldset>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                        </div>

                        <div class="modal fade" id="tradePlateModal">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <!-- Modal body -->
                                    <fieldset class="border p-2 m-2">
                                        <div class="form-group">
                                            <p class="m-0"><b>Trade Plate Detail</b></p>
                                            <div class="row">
                                                <div class="col">
                                                    <table width="100%" cellpadding="0" cellspacing="1"
                                                           class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                        <thead>
                                                        <tr>
                                                            <th scope="col" class="tbl_row_header">Trade Plate NO</th>
                                                            <th scope="col" class="tbl_row_header">Description</th>
                                                            <th scope="col" class="tbl_row_header">Delete</th>
                                                        </tr>
                                                        </thead>
                                                        <tbody id="tradePlateTbl">
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>
                            </div>
                        </div>

                        <div class="modal fade" id="trailerDetailsModal">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <!-- Modal body -->
                                    <fieldset class="border p-2 m-2">
                                        <h6> Trailer Details </h6>
                                        <hr class="my-2">
                                        <div class="form-group ">
                                            <div class="row">
                                                <div class="col-sm-6">
                                                    <span>Trailer No :</span>
                                                    <span class="label_Value"
                                                          id="trailerNo"></span>
                                                </div>
                                                <div class="col-sm-6">
                                                    <span>Sum Insured :</span>
                                                    <span class="label_Value"
                                                          id="trailersumInsured"></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group ">
                                            <div class="row">
                                                <div class="col-sm-6">
                                                    <span>Auto Premium :</span>
                                                    <span class="label_Value"
                                                          id="autoPremium"></span>
                                                </div>
                                                <div class="col-sm-6">
                                                    <span>Manual Premium :</span>
                                                    <span class="label_Value"
                                                          id="manualPremium"></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group ">
                                            <div class="row">
                                                <div class="col-sm-6">
                                                    <span>Remarks:</span>
                                                    <span class="label_Value"
                                                          id="trailerRemarks"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>
                            </div>
                        </div>

                        <div class="modal fade" id="mortgageDetailsModal">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <!-- Modal body -->
                                    <fieldset class="border p-2 m-2">
                                        <h6> Mortgage Details </h6>
                                        <hr class="my-2">
                                        <div class="form-group ">
                                            <div class="row">
                                                <div class="col-sm-6">
                                                    <span>Finance Company :</span>
                                                    <span class="label_Value"
                                                          id="finCompany">${claimsDto.policyDto.financeCompany}</span>
                                                </div>
                                                <div class="col-sm-6">
                                                    <span>Finance Company Code :</span>
                                                    <span class="label_Value"
                                                          id="finCompanyCode">${claimsDto.policyDto.finCompanyCode}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group ">
                                            <div class="row">
                                                <div class="col-sm-6">
                                                    <span>Finance Company Branch :</span>
                                                    <span class="label_Value"
                                                          id="finCompanyBranch">${claimsDto.policyDto.finCompanyBranch}</span>
                                                </div>
                                                <div class="col-sm-6">
                                                    <span>Bank Reference Number :</span>
                                                    <span class="label_Value"
                                                          id="bankRefNumber">${claimsDto.policyDto.bankRefNo}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group ">
                                            <div class="row">
                                                <div class="col-sm-6">
                                                    <span>Loan Account No :</span>
                                                    <span class="label_Value"
                                                          id="loanAccNo">${claimsDto.policyDto.loanAccNo}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>
                            </div>
                        </div>

                        <%--                        annual premium modal--%>
                        <div class="modal fade" id="annualpreModal">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <!-- Modal body -->
                                    <div class="modal-body">
                                        <div class="row">
                                            <div class="col">
                                                <c:set var="premiumBreakupFormDto"
                                                       value="${claimsDto.policyDto.premiumBreakupFormDto}"
                                                       scope="request"/>
                                                <c:set var="chargesBreakupDto"
                                                       value="${claimsDto.policyDto.premiumBreakupFormDto.chargesBreakupDto}"
                                                       scope="request"/>
                                                <table width="100%" cellpadding="0" cellspacing="1"
                                                       class="table table-hover table-sm table-responsive-md dataTable no-footer dtr-inline ">
                                                    <thead>
                                                    <tr>
                                                        <th scope="col" class="tbl_row_header">Details</th>
                                                        <th scope="col" class="tbl_row_header">Description</th>
                                                        <th scope="col" class="tbl_row_header">Premium</th>
                                                        <th scope="col" class="tbl_row_header">Contribution & Changed
                                                        </th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <tr>
                                                        <td>Riot & Strike</td>
                                                        <td>
                                                            <c:forEach var="policyPremiumDto"
                                                                       items="${premiumBreakupFormDto.policyPremiumRiotStrikeList}">
                                                                ${policyPremiumDto.description}<br>
                                                            </c:forEach>
                                                        </td>
                                                        <c:set var="totalRiotStrike" value="0" scope="request"/>
                                                        <td class="text-right">
                                                            <c:forEach var="policyPremiumDto"
                                                                       items="${premiumBreakupFormDto.policyPremiumRiotStrikeList}">
                                                                <fmt:formatNumber
                                                                        value="${policyPremiumDto.premiumAmount}"
                                                                        pattern="###,##0.00;(###,##0.00)"
                                                                        type="number"/><br>
                                                                <c:set var="totalRiotStrike"
                                                                       value="${totalRiotStrike+policyPremiumDto.premiumAmount}"
                                                                       scope="request"/>
                                                            </c:forEach>
                                                        </td>
                                                        <td class="text-right vertical-bottom">
                                                            <fmt:formatNumber
                                                                    value="${totalRiotStrike}"
                                                                    pattern="###,##0.00;(###,##0.00)" type="number"/>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>Commercial Vehicle
                                                        </td>
                                                        <td>
                                                            <c:forEach var="policyPremiumDto"
                                                                       items="${premiumBreakupFormDto.policyPremiumDtoList}">
                                                                ${policyPremiumDto.description}<br>
                                                            </c:forEach>
                                                        </td>
                                                        <c:set var="totalCommercialVehicle" value="0" scope="request"/>
                                                        <td class="text-right">
                                                            <c:forEach var="policyPremiumDto"
                                                                       items="${premiumBreakupFormDto.policyPremiumDtoList}">
                                                                <fmt:formatNumber
                                                                        value="${policyPremiumDto.premiumAmount}"
                                                                        pattern="###,##0.00;(###,##0.00)"
                                                                        type="number"/><br>
                                                                <c:set var="totalCommercialVehicle"
                                                                       value="${totalCommercialVehicle+policyPremiumDto.premiumAmount}"
                                                                       scope="request"/>
                                                            </c:forEach>
                                                        </td>
                                                        <td class="text-right vertical-bottom">
                                                            <fmt:formatNumber
                                                                    value="${totalCommercialVehicle}"
                                                                    pattern="###,##0.00;(###,##0.00)"
                                                                    type="number"/></td>
                                                    </tr>
                                                    <tr>
                                                        <td>Other Charges</td>
                                                        <td>Policy Fee<br>
                                                            Stamp Duty<br>
                                                            CESS<br>
                                                            Road Tax<br>
                                                            NBT<br>
                                                            VAT
                                                        </td>
                                                        <td class="text-right">
                                                            <fmt:formatNumber
                                                                    value="${chargesBreakupDto.pof}"
                                                                    pattern="###,##0.00;(###,##0.00)"
                                                                    type="number"/><br>
                                                            <fmt:formatNumber
                                                                    value="${chargesBreakupDto.sd}"
                                                                    pattern="###,##0.00;(###,##0.00)"
                                                                    type="number"/><br>
                                                            <fmt:formatNumber
                                                                    value="${chargesBreakupDto.cess}"
                                                                    pattern="###,##0.00;(###,##0.00)"
                                                                    type="number"/><br>
                                                            <fmt:formatNumber
                                                                    value="${chargesBreakupDto.rt}"
                                                                    pattern="###,##0.00;(###,##0.00)"
                                                                    type="number"/><br>
                                                            <fmt:formatNumber
                                                                    value="${chargesBreakupDto.nbt}"
                                                                    pattern="###,##0.00;(###,##0.00)"
                                                                    type="number"/><br>
                                                            <fmt:formatNumber
                                                                    value="${chargesBreakupDto.vat}"
                                                                    pattern="###,##0.00;(###,##0.00)"
                                                                    type="number"/><br>
                                                        </td>
                                                        <td class="text-right vertical-bottom">${chargesBreakupDto.pof+chargesBreakupDto.sd+chargesBreakupDto.cess+chargesBreakupDto.rt+chargesBreakupDto.nbt+chargesBreakupDto.vat}</td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <%--                        rejection letter modal--%>
                        <form name="frmRejectionDocumentModal${rejectionDocTypeId}"
                              id="frmRejectionDocumentModal${rejectionDocTypeId}">
                            <input type="hidden" name="documentTypeId"
                                   value="${rejectionDocTypeId}">
                            <input type="hidden" name="jobRefNo" value="0">
                            <input type="hidden" name="departmentId" value="5">
                            <input type="hidden" name="requestFormId" value="0">
                            <input type="hidden" name="claimNo" value="${claimsDto.claimNo}">
                            <div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog"
                                 id="rejectionUploadModal${rejectionDocTypeId}" aria-hidden="true"
                                 style="    background: #333333c2;">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content p-2" style="overflow: hidden">
                                        <div class="modal-header  p-2">
                                            <h6 class="modal-title"
                                                id="modalLabel${rejectionDocTypeId}">Rejection Letter</h6>
                                            <small class="text-danger pull-right"><b> .PNG / .JPG / .PDF File Formats
                                                Only.</b>
                                            </small>
                                        </div>
                                        <p id="errorUpload2${rejectionDocTypeId}"></p>
                                        <div class=" mt-4">
                                            <div class="col-sm-12">
                                                <!-- The fileinput-button span is used to style the file input field as button -->
                                                <span class="btn btn-success fileinput-button p-5 float-left mr-3 mb-3">
                                        <i class="fa fa-plus"></i>
                                        <span>Select files...</span>
                                                    <!-- The file input field used as target for the file upload widget -->
                                        <input id="fileUploadClaim2${rejectionDocTypeId}" type="file"
                                               name="files[]" multiple>
                                    </span>
                                                <!-- The global progress bar -->
                                                <div id="progressClaim${rejectionDocTypeId}"
                                                     class="progress">
                                                    <div class="progress-bar bg-success"></div>
                                                </div>
                                                <!-- The container for the uploaded files -->
                                                <div id="filesClaim2${rejectionDocTypeId}"
                                                     class="files"></div>
                                                <br>
                                            </div>
                                        </div>
                                        <div class="modal-footer p-1">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal"
                                                    onclick="loadDocumentUploadView();loadEFileUploadView()">
                                                Close
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <script>
                            rejectionDocumentUploadId.push('${rejectionDocTypeId}');
                        </script>

                        <%--                        paid amount modal--%>
                        <div class="modal fade" id="paidAmountModal">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <!-- Modal body -->
                                    <div class="modal-body">
                                        <div class="row">
                                            <div class="col">
                                                <table width="100%" cellpadding="0" cellspacing="1"
                                                       class="table table-hover table-sm table-responsive-md dataTable no-footer dtr-inline ">
                                                    <thead>
                                                    <tr>
                                                        <th scope="col" class="tbl_row_header">Date</th>
                                                        <th scope="col" class="tbl_row_header">Receipt Number</th>
                                                        <th scope="col" class="tbl_row_header">Amount</th>
                                                        <th scope="col" class="tbl_row_header">Payment Mode</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody id="receiptDetail">
                                                    <c:forEach var="paidDto"
                                                               items="${claimsDto.policyDto.paidDetailsDtoList}">
                                                        <tr>
                                                            <td scope="col"
                                                                class="tbl_row_header">${paidDto.paidDate}</td>
                                                            <td scope="col"
                                                                class="tbl_row_header">${paidDto.receiptNumber}</td>
                                                            <td scope="col" class="tbl_row_header"
                                                                style="text-align: right">
                                                                <fmt:formatNumber value="${paidDto.paidAmount}"
                                                                                  pattern="###,##0.00;(###,##0.00)"
                                                                                  type="number"/>
                                                            </td>
                                                            <td scope="col"
                                                                class="tbl_row_header">${paidDto.paymentMode}</td>
                                                        </tr>
                                                    </c:forEach>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <%--                        //Exess Detail Modal--%>

                        <%--                        <div class="modal fade" id="excessDetailModal">--%>
                        <%--                            <div class="modal-dialog modal-lg">--%>
                        <%--                                <div class="modal-content">--%>
                        <%--                                    <!-- Modal body -->--%>
                        <%--                                    <div class="modal-body">--%>
                        <%--                                        <div class="row">--%>
                        <%--                                            <div class="col">--%>
                        <%--                                               <h1>Excess Detail Modal</h1>--%>
                        <%--                                            </div>--%>
                        <%--                                        </div>--%>
                        <%--                                    </div>--%>
                        <%--                                </div>--%>
                        <%--                            </div>--%>
                        <%--                        </div>--%>
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="efiledetails">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_efiledetails"
                               aria-expanded="false" aria-controls="col_efiledetails">
                                E-File Details
                            </a>
                        </h5>
                    </div>
                    <div id="col_efiledetails" class="collapse ${tabIndex==1?'show':''}"
                         aria-labelledby="efiledetails"
                         data-parent="#accordion2">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div id="documentUploadContainer"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="definedocument">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_definedocument"
                               aria-expanded="false" aria-controls="col_definedocument">
                                Define Documents
                            </a>
                        </h5>
                    </div>
                    <div id="col_definedocument" class="collapse" aria-labelledby="definedocument"
                         data-parent="#accordion2">
                        <div class="card-body">
                            <div class="row">
                                <input type="hidden" name="claimNo" value="${claimsDto.claimNo}">
                                <div class="col-md-12">
                                    <div id="claimDefineDocumentContainer"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="engDocTabHeading">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#engDocTab"
                               aria-expanded="false" aria-controls="engDocTab">
                                Engineering Documents
                            </a>
                        </h5>
                    </div>
                    <div id="engDocTab" class="collapse ${tabIndex==11?'show':''}"
                         aria-labelledby="engDocTabHeading"
                         data-parent="#accordion2">
                        <div class="card-body p-2">
                            <div class="w-100 scroll" style="height: calc(100vh - 350px);"
                                 id="engDocUploadContainer">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="generatereminder" onclick="loadGenerateReminderView();">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_generatereminder"
                               aria-expanded="false" aria-controls="col_generatereminder">
                                Generate Reminder
                            </a>
                        </h5>
                    </div>
                    <div id="col_generatereminder" class="collapse" aria-labelledby="generatereminder"
                         data-parent="#accordion2">
                        <div class="card-body">
                            <div class="row">
                                <form name="frmGenerateReminder" id="frmGenerateReminder">
                                    <input type="hidden" name="claimNo" value="${claimsDto.claimNo}">
                                    <div id="claimGenerateReminderContainer"></div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <c:if test="${(IS_CLAIM_HANDLER_USER or IS_OFFER_TEAM_CLAIM_HANDLER_USER or IS_TOTAL_LOSS_CLAIM_HANDLER_USER)
                     or (claimHandlerDto.advanceForwardedUser eq G_USER.userId)}">
                    <div class="card mt-2">
                        <div class="card-header p-0" id="advanceAmt"
                             onclick="loadAdvanceAmountPage(${claimsDto.claimNo})">
                            <h5 class="mb-0">
                                <a class="btn btn-link" data-toggle="collapse" data-target="#col_advanceAmt"
                                   aria-expanded="false" aria-controls="col_advanceAmt">
                                    Advance Amount
                                </a>
                            </h5>
                        </div>
                        <div id="col_advanceAmt" class="collapse  ${tabIndex==16?'show':''}"
                             aria-labelledby="advanceAmt"
                             data-parent="#accordion2" style="overflow: auto">
                        </div>
                    </div>
                </c:if>
                <c:if test="${!IS_INIT_LIABILITY_USER and !IS_OFFER_TEAM_INIT_LIABILITY_USER}">
                    <div class="card mt-2">
                        <div class="card-header p-0" id="paymentoption">
                            <h5 class="mb-0">
                                <a class="btn btn-link" data-toggle="collapse" data-target="#col_paymentoption"
                                   aria-expanded="false" aria-controls="col_paymentoption">
                                    Payment Option
                                </a>
                            </h5>
                        </div>
                        <div id="col_paymentoption" class="collapse  ${tabIndex==15?'show':''}"
                             aria-labelledby="paymentoption"
                             data-parent="#accordion2" style="overflow: auto">
                        </div>
                    </div>
                    <div class="card mt-2">
                        <div class="card-header p-0" id="RefPaymentoption">
                            <h5 class="mb-0">
                                <a class="btn btn-link" data-toggle="collapse" data-target="#col_RefPaymentoption"
                                   aria-expanded="false" aria-controls="col_RefPaymentoption">
                                    Cover Based Payments
                                </a>
                            </h5>
                        </div>
                        <div id="col_RefPaymentoption" class="collapse  ${tabIndex==15?'show':''}"
                             aria-labelledby="RefPaymentoption"
                             data-parent="#accordion2" style="overflow: auto">
                            <!-- Content for the new div goes here -->
                        </div>
                    </div>

                    <%--                     development
                    --%>
                    <div class="card mt-2">
                        <div class="card-header p-0" id="invetigation">
                            <h5 class="mb-0">
                                <a class="btn btn-link" data-toggle="collapse" data-target="#col_invetigation"
                                   aria-expanded="false" aria-controls="col_invetigation">
                                    Investigation
                                </a>
                            </h5>
                        </div>
                        <div id="col_invetigation" class="collapse ${tabIndex==5?'show':''}"
                             aria-labelledby="invetigation"
                             data-parent="#accordion2">
                            <div id="claimInvestigationContainer"></div>
                        </div>
                    </div>

                    <script>
                        document.addEventListener("DOMContentLoaded", function () {
                            const collapseElement = document.getElementById("col_invetigation");

                            collapseElement.addEventListener("show.bs.collapse", function () {
                                const timestamp = new Date().toISOString();
                                console.log("Dropdown opened at:", timestamp);
                                // You can also perform other actions here, like sending it to a server or storing it
                            });
                        });
                    </script>

                </c:if>
                <div class="card mt-2">
                    <div class="card-header p-0" id="driverdetail">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_driverdetail"
                               aria-expanded="false" aria-controls="col_driverdetail">
                                Driver Details
                            </a>
                        </h5>
                    </div>
                    <div id="col_driverdetail" class="collapse" aria-labelledby="driverdetail"
                         data-parent="#accordion2">
                        <div id="driverDetailContainer"></div>
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="supplyorder">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_supplyorder"
                               aria-expanded="false" aria-controls="col_supplyorder">
                                Supply Order
                            </a>
                        </h5>
                    </div>
                    <div id="col_supplyorder" class="collapse ${tabIndex==6?'show':''}"
                         aria-labelledby="supplyorder"
                         data-parent="#accordion2">
                        <div id="claimSupplyOrderContainer"></div>
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="previousClaims">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_previousClaims"
                               aria-expanded="false" aria-controls="col_previousClaims">
                                Previous Claims
                            </a>
                        </h5>
                    </div>
                    <div id="col_previousClaims" class="collapse"
                         aria-labelledby="previousClaims"
                         data-parent="#accordion2">
                        <div id="previousClaimsContainer"></div>
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="claimSummary">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_claimSummary"
                               aria-expanded="false" aria-controls="col_claimSummary">
                                Claim Summary
                            </a>
                        </h5>
                    </div>
                    <div id="col_claimSummary" class="collapse"
                         aria-labelledby="claimSummary"
                         data-parent="#accordion2">
                        <div id="claimSummaryContainer"></div>
                    </div>
                </div>
                <div class="card mt-2" id="divPanelDecision">
                    <div class="card-header p-0" id="panelDecision">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_panelDecision"
                               aria-expanded="false" aria-controls="col_panelDecision">
                                Panel Decision
                            </a>
                        </h5>
                    </div>
                    <div id="col_panelDecision" class="collapse"
                         aria-labelledby="panelDecision"
                         data-parent="#accordion2">
                        <div id="panelDecisionContainer"></div>
                    </div>
                </div>
                <script>
                    if (${isPanelDecision}) {
                        $("#divPanelDecision").show();
                    } else {
                        $("#divPanelDecision").hide();
                    }
                </script>
                <div class="card mt-2 d-none">
                    <div class="card-header p-0" id="cofirmationrecept">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse"
                               data-target="#col_cofirmationrecept"
                               aria-expanded="false" aria-controls="col_cofirmationrecept">
                                Confirmation Recept
                            </a>
                        </h5>
                    </div>
                    <div id="col_cofirmationrecept" class="collapse" aria-labelledby="cofirmationrecept"
                         data-parent="#accordion2">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <table width="100%" cellpadding="0" cellspacing="1"
                                           class="table table-hover table-xs dataTable no-footer dtr-inline">
                                        <thead>
                                        <tr>
                                            <th scope="col" class="tbl_row_header">No</th>
                                            <th scope="col" class="tbl_row_header">Order No</th>
                                            <th scope="col" class="tbl_row_header">Vehicle No</th>
                                            <th scope="col" class="tbl_row_header">Handed over by</th>
                                            <th scope="col" class="tbl_row_header">Date / Time</th>
                                            <th scope="col" class="tbl_row_header">Branch</th>
                                            <th scope="col" class="tbl_row_header">Input User</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <%--<div class="card mt-2">
                    <div class="card-header p-0" id="paymentdetails">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_paymentdetails"
                               aria-expanded="false" aria-controls="col_paymentdetails">
                                Payment Details
                            </a>
                        </h5>
                    </div>
                    <div id="col_paymentdetails" class="collapse " aria-labelledby="paymentdetails"
                         data-parent="#accordion2">
                        <div class="card-body">
                        </div>
                    </div>
                </div>--%>

                <div class="card mt-2">
                    <div class="card-header p-0" id="specialRemarks">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#specialRemarksDiv"
                               aria-expanded="false" aria-controls="col_logdetails">
                                Special Remarks
                            </a>
                        </h5>
                    </div>
                    <div id="specialRemarksDiv" class="collapse " aria-labelledby="logdetails"
                         data-parent="#accordion2">
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="branchRemarks">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#branchRemarksDiv"
                               aria-expanded="false" aria-controls="col_logdetails">
                                Branch Remarks
                            </a>
                        </h5>
                    </div>
                    <div id="branchRemarksDiv" class="collapse " aria-labelledby="logdetails"
                         data-parent="#accordion2">
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="logdetails">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_logdetails"
                               aria-expanded="false" aria-controls="col_logdetails">
                                Log Details
                            </a>
                        </h5>
                    </div>
                    <div id="col_logdetails" class="collapse" aria-labelledby="logdetails"
                         data-parent="#accordion2">
                    </div>
                </div>

            </div>
        </div>
        <fieldset class="col-md-12">
            <div id="accordion3">
                <div class="card mt-2">
                    <div class="card-header p-0" id="heading8">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse8"
                               aria-expanded="false" aria-controls="collapse8">
                                Photo Comparison
                            </a>
                        </h5>
                    </div>
                    <div id="collapse8" class="collapse " aria-labelledby="heading8"
                         data-parent="#accordion3">
                        <div class="card-body p-0">
                            <div class="row">
                                <div class="col-lg-12">
                                    <iframe width="50%" style="height: 100vh;" frameborder="0"
                                            id="iframePhotoCom1" name="iframePhotoCom1"
                                            height="90vh"
                                            class="scroll float-left"
                                            src="${pageContext.request.contextPath}/PhotoComparisonController/initViewComparisonViewer?vehicleNumber=${claimsDto.vehicleNo}&comparisionTabNo=1&policyNumber=${claimsDto.policyDto.policyNumber}"></iframe>
                                    <iframe width="50%" style="height: 100vh;" frameborder="0"
                                            id="iframePhotoCom2" name="iframePhotoCom2"
                                            height="90vh"
                                            class="scroll float-left"
                                            src="${pageContext.request.contextPath}/PhotoComparisonController/initViewComparisonViewer?vehicleNumber=${claimsDto.vehicleNo}&comparisionTabNo=2&policyNumber=${claimsDto.policyDto.policyNumber}"></iframe>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <c:if test="${PREVIOUS_INSPECTION!='Y'}">
                    <div class="float-right mt-3">
                        <button class="btn btn-link" type="button" onclick="goBack()"><b>Back</b></button>
                    </div>
                </c:if>
            </div>
        </fieldset>
        <div style="DISPLAY: none" class=demo-description>
            <P>Click tabs to swap between content that is broken into logical sections.</P>
        </div>
    </div>
</div>
</div>
<div id="dialog" style="display:none;" title="${CompanyTitle} Lanka PLC.">
    <p><span class="ui-icon ui-icon-info" style="float:left; margin:0 7px 0 0;"></span></p>
    <p id="dialog-email" class="textGrey"></p>
</div>
<div class="viewDocumentContainer">
    <div class="card ">
        <div class="card-header p-2">
            <h5 class="mb-0 float-left">
                <i class="fa arrow-to-left "></i>Document View
            </h5>
            <a href="#" class="viewDocumentClose float-right p-1" style="width: auto;"><i class="fa fa-times"></i></a>
        </div>
        <div class="card-body p-1">
            <iframe src="" frameborder="0" id="viewDocument" width="100%" height="100vh"></iframe>
        </div>
    </div>
</div>
<div class="modal fade bd-example-modal-md" role="dialog"
     id="advanceForwardModal" aria-hidden="true"
     style="    background: #333333c2;">
    <div class="modal-dialog modal-md">
        <div class="modal-content p-2" style="overflow: hidden">
            <div class="modal-header  p-2">
                <h6 class="modal-title"
                    id="txtAdvanceForwardTitle">Forward for Advance Amount Request</h6>
            </div>
            <div class="modal-content p-2">
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Forward to :</label>
                    <div class="col-sm-8">
                        <select class="form-control" id="selectUserType"
                                onchange="toggleForwardUsers(); validateAdvanceRequestBtn()">
                            <option value="0">Please Select</option>
                            <option value="1">Spare Parts Coordinator</option>
                            <option value="2">Bill Checking User</option>
                        </select>
                    </div>
                    <label class="col-sm-4 col-form-label mt-2">Select User :</label>
                    <div class="col-sm-8 mt-2">
                        <select class="form-control" id="selectDefault"
                                onchange="validateAdvanceRequestBtn()">
                            <option value="0">Please Select</option>
                        </select>
                        <select class="form-control" id="selectspCood" style="display: none"
                                onchange="validateAdvanceRequestBtn()">
                            <option value="0">Please Select</option>
                            <%out.print(spCoodList);%>
                        </select>
                        <select class="form-control" id="selectScrTeam" style="display: none"
                                onchange="validateAdvanceRequestBtn()">
                            <option value="0">Please Select</option>
                            <%out.print(scrList);%>
                        </select>
                    </div>
                </div>
            </div>
            <div class="modal-footer p-1">
                <button type="button" class="btn btn-secondary" data-dismiss="modal"
                        onclick="">
                    Close
                </button>
                <button type="button" class="btn btn-primary" id="btnRequestForAdvance" onclick="requestForAdvance()"
                        disabled>
                    Forward
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">

    $("#accordion .card-header a").click(function () {
        var id = $(this).parent().parent().parent().prev().children().attr('id');
        $('#accordion').ScrollTo({
            callback: function () {
                $('#' + id).ScrollTo();
            }
        });
    });

    $("#tabs a").click(function (e) {
        e.preventDefault();
        $(this).tab('show');
    });
    $("#Claim-tabs a").click(function (e) {
        e.preventDefault();
        $(this).tab('show');
    });

    let trailerDetailsCount = 0;
    let tradePlateCount = 0;

    function getData(modal, value) {
        if ("TrailerDetails" === value && 0 === trailerDetailsCount) {
            trailerDetailsCount++;
            $.ajax({
                type: 'GET',
                url: contextPath + `/ClaimHandlerController/getTrailerDetail?PolicyNo=${claimsDto.policyDto.policyNumber}&PolicyChannelType=${claimsDto.policyDto.policyChannelType}`,
                success: function (value) {
                    let result = JSON.parse(value);
                    $("#trailerNo").append(result.trailerNo);
                    $("#trailersumInsured").append(result.sumInsured);
                    $("#autoPremium").append(result.autoPremium);
                    $("#manualPremium").append(result.manualPremium);
                    $("#trailerRemarks").append(result.remark);
                }
            });
        }

        if ("TradePlate" === value && 0 === tradePlateCount) {
            tradePlateCount++;
            $.ajax({
                type: 'GET',
                url: contextPath + `/ClaimHandlerController/getTradePlateDetail?PolicyNo=${claimsDto.policyDto.policyNumber}&PolicyChannelType=${claimsDto.policyDto.policyChannelType}`,
                success: function (value) {
                    let result = JSON.parse(value);
                    result.map((data) => {
                        let checkBox;
                        if (data.isDelete === "Y") {
                            checkBox = `<input type="checkbox" checked onclick="return false;">`;
                        } else {
                            checkBox = `<input type="checkbox" onclick="return false;">`;
                        }
                        $("#tradePlateTbl").append(`<tr><td scope="row" class="tbl_row_header">` + data.tradePlateNo + `</td><td scope="row" class="tbl_row_header">` + data.desc + `</td><td scope="row" class="tbl_row_header">` + checkBox + `</td></tr>`);
                    });
                }
            });
        }
        $(modal).modal('show')
    }

    let introducerCount = 0;

    function getIntroducerDetails() {
        if (0 === introducerCount) {
            introducerCount++;
            $("#agentCode").append('${claimsDto.policyDto.introducerDto.agentCode}');
            $("#agentName").append('${claimsDto.policyDto.introducerDto.agentName}');
            $("#contactNo").append('${claimsDto.policyDto.introducerDto.contactNo}');
            $("#email").append('${claimsDto.policyDto.introducerDto.email}');
            $("#introducerList").append(`<c:forEach var="sellingDto" items="${claimsDto.policyDto.sellingAgentDetailsDtoList}"><tr><td scope="col" class="tbl_row_header">${sellingDto.agentCode}</td><td scope="col" class="tbl_row_header">${sellingDto.agentName}</td>
                                      <td scope="col" class="tbl_row_header">${sellingDto.rank}</td><td scope="col" class="tbl_row_header">${sellingDto.rankDesc}</td><td scope="col" class="tbl_row_header">${sellingDto.percent}</td>
                                      <td scope="col" class="tbl_row_header">${sellingDto.agentStatus}</td><td scope="col" class="tbl_row_header">${sellingDto.startDate}</td><td scope="col" class="tbl_row_header">${sellingDto.endDate}</td></tr></c:forEach>`);
        }

    }

    function logdetails() {
        $("#col_logdetails").load("${pageContext.request.contextPath}/ClaimHandlerController/viewLogTrail?P_N_CLIM_NO=${claimsDto.claimNo}");
        $('body').removeClass('modal-open');
        $('.modal-backdrop').remove();
    }

    function specialRemarksDiv() {
        $("#specialRemarksDiv").load("${pageContext.request.contextPath}/ClaimHandlerController/viewSpecialRemark?P_N_CLIM_NO=${claimsDto.claimNo}");
        $('body').removeClass('modal-open');
        $('.modal-backdrop').remove();

    }

    function branchRemarksDiv() {
        $("#branchRemarksDiv").load("${pageContext.request.contextPath}/ClaimHandlerController/viewBranchRemark?P_N_CLIM_NO=${claimsDto.claimNo}");
        $('body').removeClass('modal-open');
        $('.modal-backdrop').remove();
    }

    $("#specialRemarks a").click(function (e) {
        specialRemarksDiv();
    });

    $("#branchRemarks a").click(function (e) {
        branchRemarksDiv();
    });

    $("#logdetails a").click(function (e) {
        logdetails();
    });


    function viewSpecialRemarks() {
        $("#specialRemaksDiv").load(contextPath + "/CallCenter/viewSpecialRemarks?P_N_CLIM_NO=${claimsDto.claimNo}");
    }

    function viewDoc(refNo, jobRefNo, previousInspection, sideClass) {
        $('.viewDocumentContainer').addClass(sideClass).resizable({
            handles: "e, w",
            ghost: true,
            helper: "resizable-helper"
        });
        $('.viewDocumentContainer  iframe#viewDocument').attr("src", "${pageContext.request.contextPath}/ClaimHandlerController/documentViewer?refNo=" + refNo + "&jobRefNo=" + jobRefNo + "&PREVIOUS_INSPECTION=" + previousInspection);

        $(".viewDocumentClose").click(function () {
            $('.viewDocumentContainer').removeClass(sideClass + " ui-resizable").removeAttr("style");
        });
    }

    $(function () {

        $(".iscat").addClass("text-mute").prop('disabled', true).trigger("chosen:updated");
        $("#firstStatementReqReason").addClass("text-mute").prop('disabled', true).trigger("chosen:updated");

        var randomColor = Math.floor(Math.random() * 16777215).toString(16);

        $(".font-bg").addClass(randomColor).css({
            backgroundColor: '#' + randomColor

        });

        if (${claimsDto.policyDto.polStatus != 'LAP'}) {
            $('#status').addClass()
        }

    });

    let driverDetailSubmitted = false;

    function saveDriverDetails() {
        var message = "Are you sure you want to submit Driver Details?";

        $("#txtClaimNumber").val(${claimsDto.claimNo});

        $("#driverStatus").prop('disabled', false);
        $("#driverTitle").prop('disabled', false);
        $("#driverName").prop('disabled', false);
        $("#driverReleshipInsured").prop('disabled', false);
        $("#driverNic").prop('disabled', false);
        $("#dlNo").prop('disabled', false);
        $("#reporterRemark").prop('disabled', false);
        $("#notRelevant").prop('disabled', false);

        let driverFormData = $("#frmDriverDetails").serialize();

        bootbox.confirm({
            message: message,
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            }, callback: function (result) {
                if (result == true) {
                    showLoader();
                    $.ajax({
                        url: contextPath + "/Claim/driverUpdate",
                        type: 'POST',
                        data: driverFormData,
                        success: function (result) {
                            const resp = JSON.parse(JSON.stringify(result));
                            if (resp.message == 'Success!') {
                                notify("Driver Details Updated Successfully!", "success");
                                $('#saveDriverDetailBtn').hide();
                                driverDetailSubmitted = true;
                                afterSubmit();
                            } else {
                                notify(resp.message, "danger");
                            }
                            hideLoader();
                        }
                    });
                }
            }
        });
    }

    function afterSubmit() {
        $("#driverStatus").prop('disabled', true);
        $("#driverTitle").prop('disabled', true);
        $("#driverName").prop('disabled', true);
        $("#driverReleshipInsured").prop('disabled', true);
        $("#driverNic").prop('disabled', true);
        $("#dlNo").prop('disabled', true);
        $("#reporterRemark").prop('disabled', true);
        $("#notRelevant").prop('disabled', true);
    }

    //alerts
    var title = "<b>Alert !</b><br>";

    function AlertErrorTirdprty(msg, type) {
        $.notify({title: title, message: msg}, {type: type, delay: 10000});
    }

    function notify(msg, type) {
        $.notify({title: title, message: msg}, {type: type, delay: 10000});
    }


    $(document).ready(function () {
        if ('${claimsDto.policyDto.polStatus}' == 'PRP') {
            $('#status').addClass('badge-primary')
        } else if ('${claimsDto.policyDto.polStatus}' == 'CAN') {
            $('#status').addClass('badge-danger')
        } else if ('${claimsDto.policyDto.polStatus}' == 'INF') {
            $('#status').addClass('badge-success')
        } else if ('${claimsDto.policyDto.polStatus}' == 'EXP') {
            $('#status').addClass('badge-primary')
        } else if ('${claimsDto.policyDto.polStatus}' == 'LAP') {
            $('#status').addClass('badge-info')
        } else if ('${claimsDto.policyDto.polStatus}' == 'SUS') {
            $('#status').addClass('badge-dark')
        }
    });


    function viewClaimHistory(polRefNo, claimNo) {
        $("#" + claimNo).colorbox({

            width: "100%",
            height: "100%",
            iframe: true,
            href: contextPath + "/CallCenter/viewClaimHistory?P_N_CLIM_NO=" + claimNo,
            onCleanup: function () {
            }
        });
    }


    function updateLiabilityCheckList() {
        bootbox.confirm({
            message: "Do you want to update liability check list?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    showLoader();
                    var formData = $('#frmLiabilityCheck').serialize();
                    $.ajax({
                        url: contextPath + "/ClaimHandlerController/updateLiabilityCheckList",
                        type: 'POST',
                        data: formData,
                        success: function (result) {
                            var obj = JSON.parse(result);
                            if (obj != "") {
                                notify(obj, "success");
                                specialRemarksDiv();
                                branchRemarksDiv();
                                logdetails();
                                $('#checkBtn').hide();
                            } else {
                                notify("Can not be updated", "danger")
                            }
                            hideLoader();
                        }
                    });
                }
            }
        });
    }


    function showDefineDocument(index) {

        var result = false;
        for (i = 0; index > i; i++) {
            var defineDocStatus = $("#defineStatus" + i).val();
            var changeDocStatus = $("#isMandatory" + i).val();
            if (defineDocStatus == 'Y' && (changeDocStatus == 'N')) {
                result = true;
                break;
            }
        }

        if (result) {
            $('#defineDocumentModal').modal('show');
        } else {
            updateDefineDocument(false);
        }

    }


    function updateDefineDocument(isRemarkMandatory) {

        var defineRemark = $('#defineDocRemark').val();

        if (isRemarkMandatory == false || defineRemark != '') {
            showLoader();
            var formData = $('#frmDefineDocument').serialize();
            $.ajax({
                url: contextPath + "/ClaimHandlerController/updateDefineDocument",
                type: 'POST',
                data: formData,
                success: function (result) {
                    var obj = JSON.parse(result);
                    if (obj == "SUCCESS") {
                        notify("Saved Successfully", "success");
                        loadGenerateReminderView();
                        loadDefineDocumentView();
                        loadDocumentUploadView();
                    } else if (obj != "") {
                        notify(obj, "danger");
                        loadGenerateReminderView();
                        loadDefineDocumentView();
                        loadDocumentUploadView();
                        closeDiv();
                    } else {
                        notify("Can not be updated", "danger");
                    }
                    hideLoader();
                }
            });

        } else {
            $('#errorDiv').show();
        }

    }

    function hideErrorDiv() {
        var defineRemark = $('#defineDocRemark').val();
        if (defineRemark != '') {
            $('#errorDiv').hide();
        } else {
            $('#errorDiv').show();
        }
    }

    function hideErrorDivAfterApproved() {
        var defineRemark = $('#defineRemark').val();
        if (defineRemark != '') {
            $('#errorDivAfterDefined').hide();
        } else {
            $('#errorDivAfterDefined').show();
        }
    }

    function closeDiv() {
        $('#errorDiv').hide();
        $('#defineDocRemark').val('');
        $('#defineDocumentModal').modal('hide');
    }


    function generateReminderPrint() {

        bootbox.confirm({
            message: "Do you want to generate reminder letter?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    showLoader();
                    var formData = $('#frmGenerateReminder').serialize();
                    $.ajax({
                        url: contextPath + "/ClaimHandlerController/generateReminder",
                        type: 'POST',
                        data: formData,
                        success: function (result) {
                            var obj = JSON.parse(result);
                            if (obj != "") {
                                notify(obj, "success");
                                loadGenerateReminderView();
                                loadDefineDocumentView();
                            } else {
                                notify("Can not be generated", "danger");
                            }
                            hideLoader();
                        }
                    });
                }
            }
        });
    }

    async function checkCoverNoteAndApproveInitLiability() {

        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }
        var claimNo = '${claimsDto.claimNo}';

        var bootboxMsg = '';
        var isCoverNote = false;
        let validationResult = await validateClosedAndPremiumOutstandingPolicies();
        /*validationResult = 0 ? Both Cancelled and Premium Outstanding Policy
        validationResult = 1 ? Cancelled Policy
        validationResult = 2 ? Premium Outstanding Policy*/

        $.ajax({
            url: contextPath + "/ClaimHandlerController/isCheckedLiablity?claimNo=" + claimNo,
            success: function (result) {
                var message = JSON.parse(result);

                if ((message == 'P')
                    || ('Y' != isAllDocUploaded || 'Y' != isAllDocMndUploaded)) {
                    bootbox.alert("Please upload and check all mandatory documents before forwarding and approving initial liability!");
                    return;
                }

                if (message == 'Y') {
                    bootboxMsg = "Do you want to initial liability check and forward for payment?";
                    var policyNo = $('#policyCoverNoteNo').val();
                    var covn = policyNo.substring(0, 4);

                    if (validationResult == 0) {
                        bootboxMsg = "This is a cancelled policy and has Rs." + $("#OUTSTANDING_PREMIUM").val() + " of outstanding premium amount. Do you want to proceed?";
                    } else if (validationResult == 1) {
                        bootboxMsg = "This is a cancelled policy. Do you want to proceed?";
                    } else if (validationResult == 2) {
                        bootboxMsg = "This policy has Rs." + $("#OUTSTANDING_PREMIUM").val() + " of outstanding premium amount. Do you want to proceed?";
                    }

                    if (('COVN' == covn)) {
                        bootboxMsg = "Policy not mapped. Do you want to approve initial liability ?";
                        isCoverNote = true;
                    }
                } else {
                    bootbox.alert("Please check all Liability Check List before approve liability!");
                    return;
                }

                bootbox.confirm({
                    message: bootboxMsg,
                    buttons: {
                        cancel: {
                            label: 'No',
                            className: 'btn-secondary float-right'
                        },
                        confirm: {
                            label: 'Yes',
                            className: 'btn-primary'
                        }
                    },
                    callback: function (result) {
                        if (result == true) {
                            if (isCoverNote) {
                                var claimNo = '${claimsDto.claimNo}';
                                $.ajax({
                                    url: contextPath + "/ClaimHandlerController/coverNoteApprove?claimNo=" + claimNo,
                                    type: 'POST',
                                    success: function (result) {
                                        var messageType = JSON.parse(result);
                                        var message = "";
                                        if (messageType == "SUCCESS") {
                                            approveInitLiability();
                                        } else {
                                            notify("System Error", "danger");
                                        }
                                    }
                                });
                            } else {
                                approveInitLiability();
                            }
                        }
                    }
                });
            }
        });
    }

    function approveInitLiability() {
        showLoader();
        var formData = $('#frmRemark').serialize();
        var claimNo = '${claimsDto.claimNo}';
        $.ajax({
            url: contextPath + "/ClaimHandlerController/approveInitLiability?claimNo=" + claimNo,
            type: 'POST',
            data: formData,
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "Approved Successfully";
                } else if (messageType == "USER_NOT_FOUND") {
                    message = "User not found to assign claim";
                } else {
                    message = "Approve Failed";
                }
                viewClaimDetails(claimNo, message, messageType);
                hideLoader();
            }
        });
    }

    function referClaimPanel() {

        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }


        var claimNo = '${claimsDto.claimNo}';
        $.ajax({
            url: contextPath + "/ClaimHandlerController/isCheckedLiablity?claimNo=" + claimNo,
            success: function (result) {
                var message = JSON.parse(result);

                if ((message == 'P')) {
                    bootbox.alert("Please upload and check all mandatory documents before forwarding to Decision Maker!");
                    return;
                }

                if (message == 'Y') {
                    bootbox.confirm({
                        message: "Do you want to forward the Decision Maker?",
                        buttons: {
                            confirm: {
                                label: 'Yes',
                                className: 'btn-primary'
                            },
                            cancel: {
                                label: 'No',
                                className: 'btn-secondary float-right'
                            }
                        },
                        callback: function (result) {
                            if (result) {
                                showLoader();
                                var claimNo = '${claimsDto.claimNo}';
                                var rejectReason = $('#repudiatedReason').val();
                                var formData = $('#frmRemark').serialize();
                                $.ajax({
                                    url: contextPath + "/ClaimHandlerController/referClaimPanel?claimNo=" + claimNo + "&PANEL_ID=" + rejectReason,
                                    type: 'POST',
                                    data: formData,
                                    success: function (result) {
                                        var messageType = JSON.parse(result);
                                        var message = "";
                                        if (messageType == "SUCCESS") {
                                            message = "Forwarded Successfully";
                                        } else if (messageType == "USER_NOT_FOUND") {
                                            message = "User not found to assign claim";
                                        } else {
                                            message = "Forward Failed";
                                        }
                                        viewClaimDetails(claimNo, message, messageType);
                                        hideLoader();
                                    }
                                });
                            } else {

                            }
                        }
                    });
                } else {
                    bootbox.alert("Please check all Liability Check List before forwarding to Decision Maker!");
                }


            }
        });

    }


    function specialToClaimPanel() {

        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }

        $.ajax({
            url: contextPath + "/ClaimHandlerController/getUserList",
            type: 'GET',
            success: function (result) {

                var optionArr = [];
                var userArr = JSON.parse(result);
                var option1 = {text: 'Please Select', value: ''};
                optionArr.push(option1);
                for (var i = 0; i < userArr.length; i++) {
                    var option = {text: userArr[i], value: userArr[i]};
                    optionArr.push(option);
                }

                bootbox.prompt({
                    title: "Please select a user to forward",
                    inputType: 'select',
                    inputOptions: optionArr,
                    callback: function (result) {
                        if (result === null) {
                            // Prompt dismissed
                        } else {
                            if (result === '') {
                                return false;
                            }
                            var claimNo = '${claimsDto.claimNo}';
                            var formData = $('#frmRemark').serialize();
                            $.ajax({
                                url: contextPath + "/ClaimHandlerController/updateSpecialCommentUsers?claimNo=" + claimNo + "&userId=" + result,
                                type: 'POST',
                                data: formData,
                                success: function (result) {
                                    var messageType = JSON.parse(result);
                                    var message = "";
                                    if (messageType == "SUCCESS") {
                                        message = "Forwarded Successfully";
                                    } else if (messageType == "CANNOT") {
                                        message = "Already Forwarded";
                                    } else {
                                        message = "Forward Failed";
                                    }
                                    viewClaimDetails(claimNo, message, messageType);
                                }
                            });
                        }
                    }
                });
            }
        });
    }

    function forwardToSparePart() {

        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }

        bootbox.confirm({
            message: "Do you want to process DO?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    showLoader();
                    var claimNo = '${claimsDto.claimNo}';
                    var formData = $('#frmRemark').serialize();
                    $.ajax({
                        url: contextPath + "/ClaimHandlerController/requestForSupplierOrder?claimNo=" + claimNo,
                        type: 'POST',
                        data: formData,
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Forwarded Successfully";
                            } else if (messageType == "USER_NOT_FOUND") {
                                message = "User not found to assign claim";
                            } else {
                                message = "Forward Failed";
                            }
                            viewClaimDetails(claimNo, message, messageType);
                            hideLoader();
                        }
                    });
                }
            }
        });
    }

    function recallDO() {

        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }

        bootbox.confirm({
            message: "Do you want to Recall DO?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    showLoader();
                    var claimNo = '${claimsDto.claimNo}';
                    var formData = $('#frmRemark').serialize();
                    $.ajax({
                        url: contextPath + "/ClaimHandlerController/requestForRecallDO?claimNo=" + claimNo,
                        type: 'POST',
                        data: formData,
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Recall Successfully";
                            } else {
                                message = "Recall Failed";
                            }
                            viewClaimDetails(claimNo, message, messageType);
                            hideLoader();
                        }
                    });
                }
            }
        });
    }


    function storeFile() {

        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }

        if ("A" != initLiabilityAprvStatus) {
            if ('Y' != isReminderLetterPrint) {
                notify("Please generate reminer letter before store the claim file!", "danger");
                $('#panleRemark').addClass('bg-badge-danger text-white');
                $('#panleRemark').focus();
                return;
            }
        }

        bootbox.confirm({
            message: "Do you want to store?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    showLoader();
                    var claimNo = '${claimsDto.claimNo}';
                    var formData = $('#frmRemark').serialize();
                    $.ajax({
                        url: contextPath + "/ClaimHandlerController/storeFile?claimNo=" + claimNo,
                        type: 'POST',
                        data: formData,
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Stored Successfully";
                            } else {
                                message = "Store Failed";
                            }
                            viewClaimDetails(claimNo, message, messageType);
                            hideLoader();
                        }
                    });
                }
            }
        });


    }

    async function checkCoverNoteAndApproveInitLiabilityAndStoreFile() {

        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }
        var claimNo = '${claimsDto.claimNo}';

        var bootboxMsg = '';
        let isCancelledPolicy = false;
        var isCoverNote = false;
        let validationResult = await validateClosedAndPremiumOutstandingPolicies();
        /*validationResult = 0 ? Both Cancelled and Premium Outstanding Policy
        validationResult = 1 ? Cancelled Policy
        validationResult = 2 ? Premium Outstanding Policy*/

        $.ajax({
            url: contextPath + "/ClaimHandlerController/isCheckedLiablity?claimNo=" + claimNo,
            success: function (result) {
                var message = JSON.parse(result);
                if (message == 'P') {
                    bootbox.alert("Please upload and check all mandatory documents before approving initial liability!");
                    return;
                }
                if (message == 'Y') {
                    if ("A" != initLiabilityAprvStatus) {
                        if ('Y' != isReminderLetterPrint) {
                            notify("Please generate reminder letter before store the claim file!", "danger");
                            $('#panleRemark').addClass('bg-badge-danger text-white');
                            $('#panleRemark').focus();
                            return;
                        }

                        bootboxMsg = "Do you want to check and store for initial liability?";
                        var policyNo = $('#policyCoverNoteNo').val();
                        var covn = policyNo.substring(0, 4);

                        if (('COVN' == covn)) {
                            bootboxMsg = "Policy not mapped. Do you want to approve initial liability ?";
                            isCoverNote = true;
                        }

                        if (validationResult == 0) {
                            bootboxMsg = "This is a cancelled policy and has Rs." + $("#OUTSTANDING_PREMIUM").val() + " of outstanding premium amount. Do you want to proceed?";
                            isCancelledPolicy = true;
                        } else if (validationResult == 1) {
                            bootboxMsg = "This is a cancelled policy. Do you want to proceed?";
                            isCancelledPolicy = true;
                        } else if (validationResult == 2) {
                            bootboxMsg = "This policy has Rs." + $("#OUTSTANDING_PREMIUM").val() + " of outstanding premium amount. Do you want to proceed?";
                        }
                    }
                } else {
                    bootbox.alert("Please check all Liability Check List before approve liability!");
                    return;
                }

                bootbox.confirm({
                    message: bootboxMsg,
                    buttons: {
                        cancel: {
                            label: 'No',
                            className: 'btn-secondary float-right'
                        },
                        confirm: {
                            label: 'Yes',
                            className: 'btn-primary'
                        }
                    },
                    callback: function (result) {
                        if (result == true) {

                            if (isCoverNote) {
                                $.ajax({
                                    url: contextPath + "/ClaimHandlerController/coverNoteApprove?claimNo=" + claimNo,
                                    type: 'POST',
                                    success: function (result) {
                                        var messageType = JSON.parse(result);
                                        if (messageType == "SUCCESS") {
                                            approveInitLiabilityAndStoreFile();
                                        } else {
                                            notify("System Error", "danger");
                                        }
                                    }
                                });
                            } else {
                                approveInitLiabilityAndStoreFile();
                            }
                        }
                    }
                });
            }
        });

    }


    function approveInitLiabilityAndStoreFile() {
        showLoader();
        var formData = $('#frmRemark').serialize();
        var claimNo = '${claimsDto.claimNo}';
        $.ajax({
            url: contextPath + "/ClaimHandlerController/approveInitialLiabilityAndStoreFile?claimNo=" + claimNo,
            type: 'POST',
            data: formData,
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "Approved And Store Successfully";
                } else if (messageType == "USER_NOT_FOUND") {
                    message = "User not found to assign claim";
                } else {
                    message = "Approve Failed";
                }
                viewClaimDetails(claimNo, message, messageType);
                hideLoader();
            }
        });
    }

    function restoreFile() {
        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }
        bootbox.confirm({
            message: "Do you want to restore?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    showLoader();
                    var claimNo = '${claimsDto.claimNo}';
                    var formData = $('#frmRemark').serialize();
                    $.ajax({
                        url: contextPath + "/ClaimHandlerController/restoreFile?claimNo=" + claimNo,
                        type: 'POST',
                        data: formData,
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Restored Successfully";
                            } else {
                                message = "Restore Failed";
                            }
                            viewClaimDetails(claimNo, message, messageType);
                            hideLoader();
                        }
                    });
                }
            }
        });


    }

    function forwardToPanel(panelId) {
        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }

        bootbox.confirm({
            message: "Do you want to forward?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    showLoader();
                    var claimNo = '${claimsDto.claimNo}';
                    var panleRemark = $("#panleRemark").val();
                    var repudiatedReason = $("#repudiatedReason").val();
                    var formData = $('#frmRemark').serialize();
                    $.ajax({
                        url: contextPath + "/ClaimHandlerController/forwardToPanel",
                        type: 'POST',
                        data: {
                            claimNo,
                            PANEL_ID: panelId,
                            panleRemark,
                            repudiatedReason
                        },
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Forwarded Successfully";
                                if (panelId == 2) {
                                    $("#divPanelDecision").show();
                                    loadPanelDecision();
                                }
                            } else if (messageType == "NOTFOUND") {
                                message = "User not found to assign claim";
                                notify(message, "danger");
                            } else {
                                message = "Forward Failed";
                            }
                            viewClaimDetails(claimNo, message, messageType);
                            hideLoader()
                        }
                    });
                }
            }
        });


    }

    var pId;
    var rejectionReasonOther;
    var formDataOther;

    function generateRejectionLetter(panelId) {
        pId = panelId;
        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }
        if ('0' == $('#repudiatedReason').val()) {
            notify("Please Select Repudiated Reason!", "danger");
            $('#repudiatedReason').focus();
            return;
        }

        var optionArr = [];
        $.ajax({
            url: contextPath + "/ClaimHandlerController/loadReasons",
            type: 'GET',
            success: function (result) {
                var userArr = JSON.parse(result);
                var option1 = {text: 'Please Select', value: '', style: 'selected'};
                optionArr.push(option1);
                for (var i = 0; i < userArr.length; i++) {
                    var option = {text: userArr[i].repudiatedLetterTypeDesc, value: userArr[i].repudiatedLetterType};
                    optionArr.push(option);
                }

                bootbox.prompt({
                    title: "Please select a letter type to Generate",
                    inputType: 'select',
                    inputOptions: optionArr,
                    callback: function (result) {
                        if (result === null) {
                            // Prompt dismissed
                        } else if (result === '0') {
                            return false;
                        } else if (result == '6') {
                            isOther = "true";
                            var rejectReason = $('#repudiatedReason').val();
                            rejectionReasonOther = rejectReason;
                            var formData = $('#frmRemark').serialize();
                            formDataOther = formData;
                            manualdocUpload(${rejectionDocTypeId});

                        } else {
                            var claimNo = '${claimsDto.claimNo}';
                            var rejectReason = $('#repudiatedReason').val();
                            var formData = $('#frmRemark').serialize();
                            $.ajax({
                                url: contextPath + "/ClaimHandlerController/generateRejectionLetter?claimNo=" + claimNo + "&PANEL_ID=" + panelId + "&rejectReason=" + rejectReason + "&rejectionLatterType=" + result,
                                type: 'POST',
                                data: formData,
                                success: function (result) {
                                    var messageType = JSON.parse(result);
                                    var message = "";
                                    if (messageType == "SUCCESS") {
                                        message = "Generated Successfully";
                                    } else {
                                        message = "Generate Failed";
                                    }
                                    viewClaimDetails(claimNo, message, messageType);
                                }
                            });
                        }

                    }
                });
            }
        });

    }

    function generateRejectionLetterOnSelect() {
        var claimNo = '${claimsDto.claimNo}';
        var rejectReason = $('#repudiatedReason').val();
        var formData = $('#frmRemark').serialize();
        $.ajax({
            url: contextPath + "/ClaimHandlerController/generateRejectionLetter?claimNo=" + claimNo + "&PANEL_ID=" + pId + "&rejectReason=" + rejectReason + "&rejectionLatterType=" + result,
            type: 'POST',
            data: formData,
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "Generated Successfully";
                } else {
                    message = "Generate Failed";
                }
                viewClaimDetails(claimNo, message, messageType);
            }
        });
    }

    function attachRejectionFileOnOtherSelect() {
        var claimNo = '${claimsDto.claimNo}';
        $.ajax({
            url: contextPath + "/ClaimHandlerController/attachRejectionFileOnOtherSelect?claimNo=" + claimNo,
            type: 'POST',
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "Attached Successfully";
                } else {
                    message = "Attach Failed";
                }
            }
        });

    }

    async function checkCoverNoteApproveLiability(panelId, liabilityApproveType) {
        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }

        let alreadySubmitted = ${claimsDto.driverDetailSubmit == 'Y'};
        const status = $("#driverStatus").val() == 0 ? null : $("#driverStatus").val();
        const title = $("#driverTitle").val() == 0 ? null : $("#driverTitle").val();
        const name = $("#driverName").val().trimEnd();
        const relationship = $("#driverReleshipInsured").val() == 0 ? null : $("#driverReleshipInsured").val();
        const nic = $("#driverNic").val().trimEnd();
        const dlno = $("#dlNo").val().trimEnd();

        if (status && title && name && relationship && nic && dlno) {
            driverDetailSubmitted = true;
        }

        if (!alreadySubmitted && !driverDetailSubmitted && (${G_USER.accessUserType eq 41 or G_USER.accessUserType eq 61 or G_USER.accessUserType eq 45})) {
            notify("Please submit Driver Details prior to approving the liability !", "danger");
            return;
        }

        if ('' == isLcChk1 || null == isLcChk1
            || '' == isLcChk2 || null == isLcChk2
            || '' == isLcChk3 || null == isLcChk3
            || '' == isLcChk4 || null == isLcChk4
            || '' == isLcChk5 || null == isLcChk5
            || '' == isLcChk6 || null == isLcChk6
            || '' == isLcChk7 || null == isLcChk7
            || '' == isLcChk8 || null == isLcChk8

        ) {
            notify("Please check all Liability Check List before approve liability!", "danger");
            return;
        }
        var claimNo = '${claimsDto.claimNo}';

        let validationResult = await validateClosedAndPremiumOutstandingPolicies();

        $.ajax({
            url: contextPath + "/ClaimHandlerController/checkedAllLiabilityApproveDocument?claimNo=" + claimNo,
            success: function (result) {
                var message = JSON.parse(result);
                if (message == 'P') {
                    bootbox.alert("Please upload and check all mandatory documents before approving liability!");
                    return;
                }

                let bootboxMsg = "Do you want to approve liability?";
                let isCancelledPolicy = false;

                var policyNo = $('#policyCoverNoteNo').val();
                var covn = policyNo.substring(0, 4);
                if ('COVN' == covn && ${G_USER.accessUserType eq 45 or G_USER.accessUserType eq 41 or G_USER.accessUserType eq 61}) {
                    bootboxMsg = "This is not a mapped policy. Kindly map the policy in order to approve the liability";
                }

                if (validationResult == 0) {
                    bootboxMsg = "This is a cancelled policy and has Rs." + $("#OUTSTANDING_PREMIUM").val() + " of outstanding premium amount. Do you want to proceed?";
                } else if (validationResult == 1) {
                    bootboxMsg = "This is a cancelled policy. Do you want to proceed?";
                } else if (validationResult == 2) {
                    bootboxMsg = "This policy has Rs." + $("#OUTSTANDING_PREMIUM").val() + " of outstanding premium amount. Do you want to proceed?";
                }

                bootbox.confirm({
                    message: bootboxMsg,
                    buttons: {
                        cancel: {
                            label: 'No',
                            className: 'btn-secondary float-right'
                        },
                        confirm: {
                            label: 'Yes',
                            className: 'btn-primary'
                        }
                    },
                    callback: function (result) {
                        if (result == true) {
                            approveLiability(panelId, liabilityApproveType);
                        } else {
                            return;
                        }
                    }
                });


            }
        });

    }

    function approveLiability(panelId, liabilityApproveType) {
        showLoader();
        var claimNo = '${claimsDto.claimNo}';
        var formData = $('#frmRemark').serialize();
        $.ajax({
            url: contextPath + "/ClaimHandlerController/approveLiability?claimNo=" + claimNo + "&PANEL_ID=" + panelId + "&LB_APPROVE_TYPE=" + liabilityApproveType,
            type: 'POST',
            data: formData,
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "Approved Successfully";
                } else if (messageType == "USER_NOT_FOUND") {
                    message = "User not found to assign claim";
                } else {
                    message = "Approve Failed";
                }
                loadPaymentOptionPage(claimNo);
                viewClaimDetails(claimNo, message, messageType);
                hideLoader();
            }
        });
    }

    async function approveAndStoreFileLiability() {
        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }

        var message = "Please upload all documents before approve initial liability";
        var claimNo = '${claimsDto.claimNo}';
        var formData = $('#frmRemark').serialize();

        let validateResult = await validateClosedAndPremiumOutstandingPolicies();

        $.ajax({
            url: contextPath + "/ClaimHandlerController/checkedAllInitialLiabilityApproveDocument",
            type: 'POST',
            data: formData,
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "P") {
                    message = "Please upload and check all mandatory documents before approving initial liability!";
                    bootbox.alert(message);
                } else {
                    message = "Do you want to approve initial liability?";
                    if (validateResult == 0) {
                        message = "This is a cancelled policy and has Rs." + $("#OUTSTANDING_PREMIUM").val() + " of outstanding premium amount. Do you want to proceed?";
                    } else if (validateResult == 1) {
                        message = "This is a cancelled policy. Do you want to proceed?";
                    } else if (validateResult == 2) {
                        message = "This policy has Rs." + $("#OUTSTANDING_PREMIUM").val() + " of outstanding premium amount. Do you want to proceed?";
                    }

                    bootbox.confirm({
                        message: message,
                        buttons: {
                            cancel: {
                                label: 'No',
                                className: 'btn-secondary float-right'
                            },
                            confirm: {
                                label: 'Yes',
                                className: 'btn-primary'
                            }
                        },
                        callback: function (result) {
                            if (result == true) {
                                showLoader();
                                var claimNo = '${claimsDto.claimNo}';
                                var formData = $('#frmRemark').serialize();
                                $.ajax({
                                    url: contextPath + "/ClaimHandlerController/approveLiabilityAndStoreFile?claimNo=" + claimNo,
                                    type: 'POST',
                                    data: formData,
                                    success: function (result) {
                                        var messageType = JSON.parse(result);
                                        var message = "";
                                        if (messageType == "SUCCESS") {
                                            message = "Approved Successfully";
                                        } else if (messageType == "USER_NOT_FOUND") {
                                            message = "User not found to assign claim";
                                        } else {
                                            message = "Approve Failed";
                                        }
                                        viewClaimDetails(claimNo, message, messageType);
                                        hideLoader();
                                    }
                                });
                            }
                        }
                    });
                }
            }
        });
    }

    function pendingLiability() {
        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }
        bootbox.confirm({
            message: "Do you want to pending liability?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    showLoader();
                    var claimNo = '${claimsDto.claimNo}';
                    var formData = $('#frmRemark').serialize();
                    $.ajax({
                        url: contextPath + "/ClaimHandlerController/pendingLiability?claimNo=" + claimNo,
                        type: 'POST',
                        data: formData,
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Marked As Pending Successfully";
                            } else {
                                message = "Mark As Pending Failed";
                            }
                            viewClaimDetails(claimNo, message, messageType);
                            hideLoader();
                        }
                    });
                }
            }
        });


    }

    function approveRejectionByPanel(panelId) {

        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }
        if ('0' == $('#repudiatedReason').val() && '2' == panelId) {

            var claimNo = '${claimsDto.claimNo}';
            $.ajax({
                url: contextPath + "/ClaimHandlerController/isAllMainPanelApproved?claimNo=" + claimNo,
                type: 'GET',
                success: function (result) {
                    var messageType = JSON.parse(result);
                    if (messageType == "SUCCESS") {
                        notify("Please Select Repudiated Reason!", "danger");
                        $('#repudiatedReason').focus();
                        $('#repudiatedReason').addClass('bg-badge-danger text-white');
                    } else {
                        bootbox.confirm({
                            message: "Do you want to reject claim?",
                            buttons: {
                                cancel: {
                                    label: 'No',
                                    className: 'btn-secondary float-right'
                                },
                                confirm: {
                                    label: 'Yes',
                                    className: 'btn-primary'
                                }
                            },
                            callback: function (result) {
                                if (result == true) {
                                    showLoader();
                                    mainPanelApprove(panelId);
                                }
                            }
                        });
                    }
                }
            });
        } else {
            bootbox.confirm({
                message: "Do you want to reject claim?",
                buttons: {
                    cancel: {
                        label: 'No',
                        className: 'btn-secondary float-right'
                    },
                    confirm: {
                        label: 'Yes',
                        className: 'btn-primary'
                    }
                },
                callback: function (result) {

                    if (result == true) {
                        showLoader();
                        mainPanelApprove(panelId);
                    }
                }
            });
        }

    }

    function mainPanelApprove(panelId) {
        var claimNo = '${claimsDto.claimNo}';
        $.ajax({
            url: contextPath + "/ClaimHandlerController/approveRejectionByPanel",
            type: 'POST',
            data: {
                claimNo,
                repudiatedReason: $("#repudiatedReason").val(),
                PANEL_ID: panelId,
                panleRemark: $("#panleRemark").val(),
            },
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "Claim Rejected Successfully";
                } else {
                    message = "Claim Rejection Failed";
                }
                viewClaimDetails(claimNo, message, messageType);
                loadPanelDecision();
                hideLoader();
            }
        });
    }

    function rejectRejectionByPanel(panelId) {
        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }
        bootbox.confirm({
            message: "Do you want to approve claim?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    showLoader();
                    var claimNo = '${claimsDto.claimNo}';
                    var formData = $('#frmRemark').serialize();
                    $.ajax({
                        url: contextPath + "/ClaimHandlerController/rejectRejectionByPanel?claimNo=" + claimNo + "&PANEL_ID=" + panelId,
                        type: 'POST',
                        data: formData,
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Claim Approved Successfully";
                            } else {
                                message = "Claim Appove Failed";
                            }
                            viewClaimDetails(claimNo, message, messageType);
                            loadPanelDecision();
                            hideLoader();
                        }
                    });
                }
            }
        });


    }

    function requestForInvestigationByPanel(panelId) {
        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }
        var claimNo = '${claimsDto.claimNo}';
        var formData = $('#frmRemark').serialize();
        $.ajax({
            url: contextPath + "/ClaimHandlerController/requestForInvestigationByPanel?claimNo=" + claimNo + "&PANEL_ID=" + panelId,
            type: 'POST',
            data: formData,
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "Requested Successfully";
                } else {
                    message = "Request Failed";
                }
                viewClaimDetails(claimNo, message, messageType);
                hideLoader();
            }
        });
    }

    function requestForInvestigationByDecisionMaker(panelId) {
        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }
        var claimNo = '${claimsDto.claimNo}';
        var formData = $('#frmRemark').serialize();
        $.ajax({
            url: contextPath + "/ClaimHandlerController/requestForInvestigationByDecisionMaker?claimNo=" + claimNo + "&PANEL_ID=" + panelId,
            type: 'POST',
            data: formData,
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "Requested Successfully";
                } else {
                    message = "Request Failed";
                }
                viewClaimDetails(claimNo, message, messageType);
                hideLoader();
            }
        });
    }

    function requestForInvestigationByClaimHandler() {
        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }


        bootbox.confirm({
            message: "Do you want to forward the Decision Maker?",
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                }
            },
            callback: function (result) {
                if (result) {
                    showLoader();
                    var claimNo = '${claimsDto.claimNo}';
                    var formData = $('#frmRemark').serialize();
                    $.ajax({
                        url: contextPath + "/ClaimHandlerController/requestForInvestigationByClaimHandler?claimNo=" + claimNo,
                        type: 'POST',
                        data: formData,
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Requested Successfully";
                            } else if (messageType == "USER_NOT_FOUND") {
                                message = "User not found to assign claim";
                            } else {
                                message = "Request Failed";
                            }
                            viewClaimDetails(claimNo, message, messageType);
                            hideLoader();
                        }
                    });
                } else {

                }
            }
        });


    }

    function arrangeInvestigation(panelId) {
        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }
        var claimNo = '${claimsDto.claimNo}';
        var formData = $('#frmRemark').serialize();
        $.ajax({
            url: contextPath + "/ClaimHandlerController/arrangeInvestigation?claimNo=" + claimNo + "&PANEL_ID=" + panelId,
            type: 'POST',
            data: formData,
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "Arranged Successfully";
                } else {
                    message = "Arrange Failed";
                }
                viewClaimDetails(claimNo, message, messageType);
                hideLoader();
            }
        });
    }

    function arrangeInvestigationByDecisionMaker() {
        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }
        var claimNo = '${claimsDto.claimNo}';
        var formData = $('#frmRemark').serialize();
        $.ajax({
            url: contextPath + "/ClaimHandlerController/arrangeInvestigationByDecisionMaker?claimNo=" + claimNo,
            type: 'POST',
            data: formData,
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "Arranged Successfully";
                } else {
                    message = "Arrange Failed";
                }
                viewClaimDetails(claimNo, message, messageType);
                hideLoader();
            }
        });
    }


    function viewClaimDetails(claimNo, message, messageType) {
        loadClaimStampContainer(claimNo);
        loadClaimHandlerBtnPanelContainer(claimNo);
        $('#panleRemark').val('');
        $('#panleRemark').removeClass('bg-badge-danger text-white');

        if ('' != messageType) {
            if ('SUCCESS' == messageType) {
                notify(message, "success");
            } else if ('ERROR' == messageType) {
                notify(message, "danger");
            } else if ('CANNOT' == messageType) {
                notify(message, "danger");
            } else if ('USER_NOT_FOUND' == messageType) {
                notify(message, "danger");
            } else if ('FAIL' == messageType) {
                notify(message, "danger");
            }
        }
    }

    function refreshClaimView() {
        var claimNo = '${claimsDto.claimNo}';
        loadClaimStampContainer(claimNo);
    }

    function loadClaimStampContainer(claimNo) {
        $("#claimStampContainer").load(contextPath + "/ClaimHandlerController/loadClaimStampPage?claimNo=" + claimNo);
    }

    function loadClaimHandlerBtnPanelContainer(claimNo) {
        $("#claimHandlerBtnPanel").load(contextPath + "/ClaimHandlerController/loadClaimHandlerBtnPanel?claimNo=" + claimNo);
    }

    function loadPaymentOptionPage(claimNo) {
        $("#col_paymentoption").load(contextPath + "/CalculationSheetController/loadPaymentOptionPage?claimNo=" + claimNo);
        $("#col_RefPaymentoption").load(contextPath + "/ReferenceTwoCalculationSheetController/loadPaymentOptionPage?claimNo=" + claimNo);
    }

    function loadAdvanceAmountPage(claimNo) {
        $("#col_advanceAmt").load(contextPath + "/ClaimHandlerController/loadAdvanceAmountPage?claimNo=" + claimNo);
    }

    function hideSelctotrs() {
        var repudiatedReason = $('#repudiatedReason').val();
        if (repudiatedReason > 0) {

            $('.storefile').hide();
            $('.initliability').hide();
        } else {
            $('.storefile').show();
            $('.initliability').show();
        }
    }


    function goBack() {
        let type = ${TYPE};
        bootbox.confirm({
            message: "Are you sure you want to close this Page?",
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                }

            },
            callback: function (result) {
                if (result == true) {
                    parent.document.getElementById("loading").style.display = "block";
                    parent.document.getElementById("cell1").style.display = "block";
                    document.frmForm.action = type === 103 ? contextPath + "/ClaimHandlerController/letterPanelList" : type === 30 ? contextPath + "/ClaimHandlerController/claimBulkCloseClaimList?TYPE=30" : contextPath + "/ClaimHandlerController/claimHandlerList?TYPE=1";
                    document.frmForm.submit();
                }
            }
        });
    }

    $(document).ready(function () {

        var count = $(".dropdown-menu .dropdown-item").length;
        if (count == 0) {
            $('.dropdown-menu').hide();
            $('#btnGroupDrop1').hide();
        }

        var claimNo = '${claimsDto.claimNo}';
        loadClaimStampContainer(claimNo);
        loadClaimHandlerBtnPanelContainer(claimNo);
        loadPaymentOptionPage(claimNo);
        loadAdvanceAmountPage(claimNo);
        showHideBtn();
        $('#theftAndFound').prop('checked', ${claimsDto.isTheftAndFound == 'Y'});
        $('#theftAndFound').prop('disabled', ${claimsDto.isTheftAndFound == 'Y'});
    });


    function checkedLiablity() {
        var claimNo = '${claimsDto.claimNo}';
        $.ajax({
            url: contextPath + "/ClaimHandlerController/isCheckedLiablity?claimNo=" + claimNo,
            success: function (result) {
                var message = JSON.parse(result);
                return message;

            }
        });
    }

    function viewSuperDashboard(claimNo) {

        $("#txtSuperDashboardClaimNo").val(claimNo);
        document.getElementById("frmSuperDashboard").target = "_blank";
        document.getElementById('frmSuperDashboard').action = contextPath + "/DashboardController/viewSuperDashboard";
        document.getElementById('frmSuperDashboard').submit();
    }

    function showHideBtn() {
        var staus = '${claimHandlerDto.closeStatus}';

        if (staus == 'CLOSE' || staus == 'SETTLE') {
            $('#closeBtn').hide();
            $('#reOpenBtn').show();
            //$('#claimHandlerBtnPanel').hide();
        } else if (staus == 'PENDING' || staus == 'REOPEN') {
            $('#closeBtn').show();
            $('#reOpenBtn').hide();
            // $('#claimHandlerBtnPanel').show();
        } else if (staus == 'SETTLE_PENDING') {
            $('#closeBtn').hide();
            $('#reOpenBtn').hide();
        }

    }

    function requestedAri() {
        $('#btnAriRequest').prop('disabled', true);
        $('#chkIsNotRequested').prop('disabled', true);
        $('#chkIsRequested').prop('disabled', true);
        var claimNo = '${claimsDto.claimNo}';
        $.ajax({
            url: contextPath + "/ClaimHandlerController/requestedAri?N_CLAIM_NO=" + claimNo,
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "ARI Requested Successfully";
                    $('#chkIsRequested').prop('checked', true);
                    $('#btnAriRequest').hide();
                } else {
                    message = "ARI Requested Failed";
                    $('#btnAriRequest').prop('disabled', false);
                    $('#chkIsNotRequested').prop('disabled', false);
                    $('#chkIsRequested').prop('disabled', false);
                }
                viewClaimDetails(claimNo, message, messageType);
                hideLoader();
            }
        });

    }

    async function claimReopen() {
        const result = await isSpecialCommentApprovel();
        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }
        if (result == "SUCCESS") {
            notify("Please reply to special comment before re-open", "danger");
            return;
        }
        bootbox.prompt({
//                message: "Are you sure you want to close this Page?",
            title: "Do you want to reopen ?",
            message: "Do you want to reopen ?",
            inputType: 'select',
            inputOptions: [
                {
                    text: 'Please Select one',
                    value: ''
                },
                {
                    text: 'NORMAL',
                    value: 'NORMAL'
                },
                {
                    text: 'EX-GRATIA',
                    value: 'EX',
                },
            ],
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                }

            },
            callback: function (result) {

                if (null != result) {
                    if ('' != result) {
                        showLoader();
                        reOpenStatus(result);
                    } else {
                        notify("Please select reopen type?", "danger");

                    }

                }


            }
        });
    }


    function claimClose() {
        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }
        bootbox.confirm({
            message: "Do you want to close ?",
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                }

            },
            callback: function (result) {
                if (result) {
                    showLoader();
                    closeStatus();

                }
            }
        });
    }

    function closeStatus() {
        var claimNo = '${claimsDto.claimNo}';
        var remark = $('#panleRemark').val();
        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }
        $.ajax({
            url: contextPath + "/ClaimHandlerController/closeClaim?P_N_CLIM_NO=" + claimNo + "&panleRemark=" + remark,
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "Closed Successfully";
                    $('#closeBtn').hide();
                    $('#reOpenBtn').show();
                    // $('#claimHandlerBtnPanel').hide();
                    loadPaymentOptionPage(claimNo);
                } else {
                    message = "Closed Failed";
                }

                viewClaimDetails(claimNo, message, messageType);
                hideLoader();
            }
        });
    }


    function reOpenStatus(type) {
        var claimNo = '${claimsDto.claimNo}';
        var remark = $('#panleRemark').val();

        $.ajax({
            url: contextPath + "/ClaimHandlerController/reOpenClaim?P_N_CLIM_NO=" + claimNo + "&reOpenType=" + type + "&panleRemark=" + remark,
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "Reopened Successfully";
                    $('#reOpenBtn').hide();
                    $('#closeBtn').show();
                    // $('#claimHandlerBtnPanel').show();
                    loadPaymentOptionPage(claimNo);
                } else if (messageType == "USER_NOT_FOUND") {
                    message = "User not found to assign claim";
                } else {
                    message = "Reopened Failed";
                }
                viewClaimDetails(claimNo, message, messageType);
                hideLoader();
            }
        });
    }

    function forwardToClaimHandler() {
        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }
        var claimNo = '${claimsDto.claimNo}';
        var formData = $('#frmRemark').serialize();
        $.ajax({
            url: contextPath + "/ClaimHandlerController/forwardToClaimHandler?claimNo=" + claimNo,
            type: 'POST',
            data: formData,
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "Successfully Forwarded";
                } else {
                    message = "Successfully Failed";
                }
                viewClaimDetails(claimNo, message, messageType);
                hideLoader();
            }
        });
    }

    function checkValidation() {
        $('#frmDriverDetails')
            .formValidation({
                framework: 'bootstrap',
                //  excluded: ':disabled',
                // excluded: ':disabled,input:hidden',
                excluded: ':disabled',
                disable: false,
                icon: {
                    valid: 'glyphicon glyphicon-ok',
                    invalid: 'glyphicon glyphicon-remove',
                    validating: 'glyphicon glyphicon-refresh'
                },
                fields: {
                    driverNic: {
                        validators: {
                            notEmpty: {
                                message: 'This field is required and cannot be empty.'
                            },
                            regexp: {
                                regexp: /^(([0-9]{9}[vVxX])|([0-9]{12}))$/i,
                                message: 'Invalid NIC No format (E.g. 123456789V or 123456789123).'
                            },
                        }
                    },
                }
            });
        const status = $("#driverStatus").val() == 0 ? null : $("#driverStatus").val();
        const title = $("#driverTitle").val() == 0 ? null : $("#driverTitle").val();
        const name = $("#driverName").val().trimEnd();
        const relationship = $("#driverReleshipInsured").val() == 0 ? null : $("#driverReleshipInsured").val();
        const nic = $("#driverNic").val().trimEnd();
        const dlno = $("#dlNo").val().trimEnd();
        const notRelevant = $("#notRelevant").is(":checked");

        let invalidFields = 1;

        if (nic) {
            invalidFields = $("#frmDriverDetails").data('formValidation').enableFieldValidators('driverNic', true).$invalidFields.length > 0;
        }

        if (!notRelevant && (!status || !title || !name || !relationship || invalidFields > 0 || !dlno)) {
            $("#saveDriverDetailBtn").hide();
        } else {
            $("#saveDriverDetailBtn").show();
        }
    }

    function viewAllImages() {
        var claimNo = '${sessionClaimHandlerDto.claimsDto.claimNo}';
        var colorbox = $("#viewAllImagesBtn").colorbox({
            width: "100%",
            height: "100%",
            iframe: true,
            href: contextPath + "/ClaimHandlerController/viewAllImages?CLAIM_NO=" + claimNo,
            onCleanup: function () {
                loadInvestigationImagesView();
            }
        });
    }

    function validateClosedAndPremiumOutstandingPolicies() {
        return new Promise(resolve => {

            $.ajax({
                url: contextPath + "/Claim/getPolicyValidity",
                data: {
                    V_POL_NO: '${claimHandlerDto.claimsDto.policyDto.policyNumber}'
                },
                type: 'POST',
                success: function (result) {
                    var response = JSON.parse(result);
                    $("#OUTSTANDING_PREMIUM").val(response.outstandingPremium);
                    $("#IS_CANCELLED_POLICY").val(response.policyStatus);
                    if (response.policyStatus == 'CAN' && response.outstandingPremium > 0) {
                        return resolve(0);
                    } else if (response.policyStatus == 'CAN') {
                        return resolve(1);
                    } else if (response.outstandingPremium > 0) {
                        return resolve(2);
                    } else {
                        return resolve(null);
                    }
                }
            });
        });
    }

    function returnToDecisionMaker() {
        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }
        bootbox.confirm({
            message: "Do you want to Return file to Decision Maker?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    showLoader();
                    var claimNo = '${claimsDto.claimNo}';
                    var formData = $('#frmRemark').serialize();
                    $.ajax({
                        url: contextPath + "/ClaimHandlerController/returnToDecisionMaker?claimNo=" + claimNo,
                        type: 'POST',
                        data: formData,
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Returned Successfully";
                                loadPanelDecision();
                            } else {
                                message = "Return Failed";
                            }
                            viewClaimDetails(claimNo, message, messageType);
                            hideLoader();
                        }
                    });
                }
            }
        });
    }

    function forwardForAdvance() {
        if ('' == $('#panleRemark').val()) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        } else {
            $("#txtAdvanceAmountRemark").val($('#panleRemark').val());
        }

        $("#advanceForwardModal").modal({
            backdrop: 'static',
            refresh: true,
            show: true
        });
    }

    function toggleForwardUsers() {
        var userType = $("#selectUserType").val();
        var defaultSelect = document.getElementById('selectDefault');
        var select1 = document.getElementById('selectspCood');
        var select2 = document.getElementById('selectScrTeam');
        if (userType == 1) {
            select1.style.display = "block";
            select2.style.display = "none";
            defaultSelect.style.display = "none";
        } else if (userType == 2) {
            select2.style.display = "block";
            select1.style.display = "none";
            defaultSelect.style.display = "none";
        } else {
            defaultSelect.style.display = "block";
            select1.style.display = "none";
            select2.style.display = "none";
        }
    }

    function validateAdvanceRequestBtn() {
        var userType = $("#selectUserType").val();
        var val1 = $("#selectspCood").val();
        var val2 = $("#selectScrTeam").val();

        if (userType == 1 && (val1 != '' && val1 != 0)) {
            $("#btnRequestForAdvance").prop('disabled', false);
        } else if (userType == 2 && (val2 != '' && val2 != 0)) {
            $('#btnRequestForAdvance').prop('disabled', false);
        } else {
            $('#btnRequestForAdvance').prop('disabled', true);
        }
    }

    function requestForAdvance() {
        showLoader();
        var isForward = $("#selectUserType").prop('disabled');
        var userType = $("#selectUserType").val();
        var advanceAmount = parseFloat(isNaN($("#balanceAdvanceAmount").val()) || $("#balanceAdvanceAmount").val() == '' ? 0.00 : $("#balanceAdvanceAmount").val());
        var assignUser = '';
        var accessUserCode = '';

        if (userType == 1) {
            assignUser = $("#selectspCood").val();
            accessUserCode = 27;
        } else if (userType == 2) {
            assignUser = $("#selectScrTeam").val();
            accessUserCode = 28;
        }

        var url = '';
        var remark = $("#txtAdvanceAmountRemark").val();

        if (isForward) {
            url = contextPath + "/ClaimHandlerController/forwardForAdvance?TYPE=" + userType + "&AMOUNT=" + advanceAmount;
        } else {
            url = contextPath + "/ClaimHandlerController/requestForAdvance";
        }

        $.ajax({
            url: url,
            type: 'POST',
            data: {
                assignUser: assignUser,
                accessUserCode: accessUserCode,
                claimNo: ${claimsDto.claimNo},
                remark: remark
            },
            success: function (result) {
                var response = JSON.parse(result);
                if (response == 'SUCCESS') {
                    loadAdvanceAmountPage(${claimsDto.claimNo});
                    $("#txtAdvanceAmountRemark").val('');
                    $("#advanceForwardModal").modal('hide');
                    notify("Successfully Forwarded", "success");
                } else {
                    $("#advanceForwardModal").modal('hide');
                    notify("Failed to Forward", "danger");
                }
                $('#panleRemark').val('');
                $('#panleRemark').removeClass('bg-badge-danger text-white');
                loadClaimHandlerBtnPanelContainer(${claimsDto.claimNo});
                hideLoader();
            }
        });

    }

    function recallAdvanceRequestByClaimHandler() {
        var remark = $('#panleRemark').val();
        if ('' == remark) {
            notify("Please enter remark!", "danger");
            $('#panleRemark').addClass('bg-badge-danger text-white');
            $('#panleRemark').focus();
            return;
        }

        bootbox.confirm({
            message: "Do You want to Recall Advance Approval Request?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            }, callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/ClaimHandlerController/recallAdvanceRequest",
                        type: 'POST',
                        data: {
                            claimNo: ${claimsDto.claimNo},
                            remark: remark
                        },
                        success: function (result) {
                            var response = JSON.parse(result);
                            if (response == 'SUCCESS') {
                                notify("Advance Request Recalled Successfully", 'success');
                                loadAdvanceAmountPage(${claimsDto.claimNo});
                            } else {
                                notify('Failed to Recall', 'danger');
                                loadAdvanceAmountPage(${claimsDto.claimNo});
                            }
                            $('#panleRemark').val('');
                            $('#panleRemark').removeClass('bg-badge-danger text-white');
                            loadClaimHandlerBtnPanelContainer(${claimsDto.claimNo});
                        }
                    });
                }
            }
        });
    }

    function markTheftAndFound(isChecked) {
        var isTheftAndFound = ${claimsDto.isTheftAndFound=='Y'};
        if (isTheftAndFound) {
            notify("Already Marked as Theft and Found", "danger");
            return;
        }
        if (isChecked) {
            bootbox.dialog({
                title: 'Do you want to mark as Vehicle Found?',
                message: "<textarea class='form-control' placeholder='Please Enter Remark' id='txtTheftFoundRemark'></textarea>",
                size: 'medium',
                buttons: {
                    cancel: {
                        label: "No",
                        className: 'btn-secondary',
                        callback: function () {
                            $('#theftAndFound').prop('checked', false);
                        }
                    },
                    ok: {
                        label: "Yes",
                        className: 'btn-primary',
                        callback: function () {
                            var claimNo = ${claimsDto.claimNo};
                            var remark = $('#txtTheftFoundRemark').val();
                            if (remark == '') {
                                $('#txtTheftFoundRemark').addClass('bg-badge-danger text-white');
                                $('#txtTheftFoundRemark').focus();
                                notify('Please Enter Remark', 'danger');
                                return false;
                            } else {
                                $.ajax({
                                    url: contextPath + "/CallCenter/theftAndFound",
                                    type: 'POST',
                                    data: {
                                        N_CLAIM_NO: claimNo,
                                        V_REMARK: remark
                                    },
                                    success: function (result) {
                                        var response = JSON.parse(result);
                                        if (response == 'SUCCESS') {
                                            notify('Successfully Updated', 'success');
                                            $('#divTheftAndFound').hide();
                                            loadLogDetails();
                                            loadSpecialRemarks();
                                            loadClaimHandlerBtnPanelContainer(${claimsDto.claimNo});
                                            loadClaimStampContainer(${claimsDto.claimNo});
                                        } else if (response == 'FAIL') {
                                            notify('Failed to Update', 'danger');
                                            $('#theftAndFound').prop('checked', false);
                                        } else {
                                            notify('System Error', 'danger');
                                            $('#theftAndFound').prop('checked', false);
                                        }
                                    }
                                })
                            }
                        }
                    }
                }
            })
        } else {
            notify('Cannot Re-Convert as Theft Claim', 'danger');
            return;
        }
    }

    function manualdocUpload(rejectionDocTypeId) {
        $('#rejectionUploadModal' + rejectionDocTypeId).modal({
            backdrop: 'static',
            keyboard: false,
            refresh: true,
            show: true
        });
    }


    function documentFileUploderr(rejectionDocTypeId) {
        'use strict';
        var progress = 0;
        var url = '${pageContext.request.contextPath}/DocumentUploadController';
        $('#fileUploadClaim2' + rejectionDocTypeId).fileupload({

            url: url,
            dataType: 'json',
            add: function (e, data) {
                data.submit();
                return;
            },
            done: function (e, data) {
                $.each(data.files, function (index, file) {
                    $('<i class="fa fa-file-pdf-o fa-4x m-3"></i>').appendTo('#filesClaim2' + rejectionDocTypeId);
                });
                $('#errorUpload2' + rejectionDocTypeId).removeClass("bg-primary");
                $('#errorUpload2' + rejectionDocTypeId).removeClass("bg-danger");
                $('#errorUpload2' + rejectionDocTypeId).addClass("bg-success");
                $('#errorUpload2' + rejectionDocTypeId).html("");
                $('#errorUpload2' + rejectionDocTypeId).fadeIn();
                $('<span class="text-light d-block p-1 text-center" >Document Uploaded Successfully!</span>').appendTo('#errorUpload2' + rejectionDocTypeId);
                $('#errorUpload2' + rejectionDocTypeId).fadeOut(4000);


                var claimNo = '${claimsDto.claimNo}';
                $.ajax({
                    url: contextPath + "/ClaimHandlerController/attachRejectionFileOnOtherSelect?claimNo=" + claimNo,
                    type: 'POST',
                    success: function (result) {
                        var messageType = JSON.parse(result);
                        var message = "";
                        if (messageType == "SUCCESS") {
                            message = "Attached Successfully";
                        } else {
                            message = "Attach Failed";
                        }
                    }
                });

                $.ajax({
                    url: contextPath + "/ClaimHandlerController/updateDefineDocumentOnOther?claimNo=" + claimNo,
                    type: 'POST',
                    success: function (result) {
                        var messageType = JSON.parse(result);
                        var message = "";
                        if (messageType == "SUCCESS") {
                            message = "Attached Successfully";
                        } else {
                            message = "Attach Failed";
                        }
                    }
                });

                $.ajax({
                    url: contextPath + "/ClaimHandlerController/generateRejectionLetter?claimNo=" + claimNo + "&PANEL_ID=" + pId + "&rejectReason=" + rejectionReasonOther + "&rejectionLatterType=" + 6,
                    type: 'POST',
                    data: formDataOther,
                    success: function (result) {
                        var messageType = JSON.parse(result);
                        var message = "";
                        if (messageType == "SUCCESS") {
                            message = "Generated Successfully";
                        } else {
                            message = "Generate Failed";
                        }
                        isOther = "false";
                        viewClaimDetails(claimNo, message, messageType);
                    }
                });

            },
            progressall: function (e, data) {
                progress = parseInt(data.loaded / data.total * 100, 10);

                $('#progressClaim' + rejectionDocTypeId + ' .progress-bar').css(
                    'width',
                    progress + '%'
                );
            },
            fail: function (e, data) {
                $('#errorUpload2' + rejectionDocTypeId).removeClass("bg-primary");
                $('#errorUpload2' + rejectionDocTypeId).removeClass("bg-success");
                $('#errorUpload2' + rejectionDocTypeId).addClass("bg-danger");
                $('#errorUpload2' + rejectionDocTypeId).html("");
                $('#errorUpload2' + rejectionDocTypeId).fadeIn();
                $('<span class="text-light d-block p-1 text-center">Document Upload failed.</span>').appendTo('#errorUpload2' + rejectionDocTypeId);
                $('#errorUpload2' + rejectionDocTypeId).fadeOut(4000);
            }, change: function (e, data) {
                progress = 0;
                $.each(data.files, function (index, file) {

                    $('#progressClaim' + rejectionDocTypeId + ' .progress-bar').css(
                        'width',
                        progress + '%'
                    );
                    $('#errorUpload2' + rejectionDocTypeId).removeClass("bg-primary");
                    $('#errorUpload2' + rejectionDocTypeId).removeClass("bg-danger");
                    $('#errorUpload2' + rejectionDocTypeId).removeClass("bg-success");

                    $('#errorUpload2' + rejectionDocTypeId).addClass("bg-primary");
                    $('#errorUpload2' + rejectionDocTypeId).html("");
                    $('#errorUpload2' + rejectionDocTypeId).fadeIn();
                    $('<span class="text-light d-block p-1 text-center">Processing file...</span>').appendTo('#errorUpload2' + rejectionDocTypeId);


                });
            }, drop: function (e, data) {
                e.preventDefault();
            }, dragover: function (e, data) {
                // return false;
            }
        }).prop('disabled', !$.support.fileInput)
            .parent().addClass($.support.fileInput ? undefined : 'disabled');
    }

    function isSpecialCommentApprovel() {
        var claimNo = '${claimsDto.claimNo}';
        return new Promise(resolve => {
            $.ajax({
                    url: contextPath + "/ClaimHandlerController/isSpecailCommentApprovel?claimNo=" + claimNo,
                    type: "GET",
                    success: function (result) {
                        var messageType = JSON.parse(result);
                        var message = "";
                        if (messageType == "SUCCESS") {
                            return resolve("SUCCESS");
                        }
                        return resolve(null);
                    }
                }
            );
        });
        return resolve(null);
    }

    rejectionDocumentUploadId.forEach(function (t) {
        documentFileUploderr(t)
    });


    function remarkSubmitForm(button) {
        var textarea = document.getElementById('remark');
        var type = button.value;
        var remark = textarea.value;
        var formData = $('#frmRemark').serialize();

        if (remark != '') {
            if ("SC" === type) {
                button.style.display = 'none';
            }
            $.ajax({
                url: contextPath + "/ClaimHandlerController/saveRemark?remarkType=" + type,
                type: 'POST',
                data: formData,
                success: function (result) {
                    var obj = JSON.parse(result);
                    if (obj != "") {
                        textarea.value = "";
                        notify(obj, "success");
                        specialRemarksDiv();
                        logdetails();
                    } else {
                        textarea.value = "";
                        notify("Can not be updated", "danger");
                    }
                }
            });
        } else {
            textarea.focus();
            textarea.style.borderColor = 'red';
            textarea.style.background = '#f8d7da';
            notify("Remark field is required and cannot be empty!", "danger");
        }
    }

    // todo duplicate endpoint call issue fix
    $(document).ready(function () {
        const toggleButton = $('[data-target="#col_invetigation"]');

        if (toggleButton.length) {
            let lastExpandedState = toggleButton.attr("aria-expanded");

            function logExpansion() {
                const isExpanded = toggleButton.attr("aria-expanded") === "true";

                // Only proceed if the state has changed to "true"
                if (isExpanded && lastExpandedState !== "true") {
                    let claimVal = '${claimsDto.claimNo}';
                    console.log(claimVal);

                    $.ajax({
                        url: contextPath + "/ClaimFlowDetailController/save-claimLogs?claimNo=" + claimVal,
                        type: "POST",
                        contentType: "application/json",
                        success: function (response) {
                            console.log("Log saved successfully:", response);
                        },
                        error: function (xhr, status, error) {
                            console.error("Error saving log:", error);
                        }
                    });
                }

                // Update last known state
                lastExpandedState = toggleButton.attr("aria-expanded");
            }

            const observer = new MutationObserver(function (mutations) {
                mutations.forEach(function (mutation) {
                    if (mutation.attributeName === "aria-expanded") {
                        logExpansion();
                    }
                });
            });

            observer.observe(toggleButton[0], {attributes: true});
        }
    });

</script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/claimhandler/claimhandler-form-validations.js?v8">

</script>
<script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload.js"></script>
<script src="${pageContext.request.contextPath}/resources/js/jquery-scrollto.js"></script>


</body>
</html>
