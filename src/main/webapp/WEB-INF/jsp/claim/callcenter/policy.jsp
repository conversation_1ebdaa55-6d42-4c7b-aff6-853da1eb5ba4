<%@ page import="com.misyn.mcms.admin.admin.dto.ProductDetailListDto" %>
<%@ page import="java.util.ArrayList" %>
<%@ page import="java.util.List" %>
<%@ page import="com.misyn.mcms.claim.dto.ExcessDetailsDto" %>
<%--
    Document   : policy
    Created on : Feb 11, 2011, 12:01:25 PM
    Product    : Aviva Claim System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 1.0
--%>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<jsp:useBean id="DbRecordCommonFunctionBean" class="com.misyn.mcms.dbconfig.DbRecordCommonFunction"
             scope="session"/>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <jsp:include page="/WEB-INF/jsp/claim/common/policyDetailsModal.jsp"></jsp:include>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/callcenter/policypage.js?v12"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/callcenter/policy-form-validation.js?v5"></script>

    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery.popupWindow.js"></script>

    <script type="text/javascript">
        showLoader();

        function resizeIframe(obj) {
            obj.style.height = obj.contentWindow.document.body.scrollHeight + 'px';
        }
    </script>
    <c:choose>
        <c:when test="${FORM_TYPE!='HISTORY'}">
            <c:set var="claimsDto" value="${claimsDto}" scope="session"/>
        </c:when>
        <c:when test="${FORM_TYPE=='HISTORY'}">
            <c:set var="claimsDto" value="${historyClaimsDto}" scope="request"/>
        </c:when>
    </c:choose>
</head>
<body class="scroll" onload="validateThirdParty()">
<div class="container-fluid">
    <div class="row header-bg bg-dark">
        <div class="col-sm-12 py-2" id="status">
            <h6 class="float-left text-dark hide-for-small"> Accident Details -
                <small>${UtilityBean.getCustomDateFormat(claimsDto.inpTime,"yyyy-MM-dd HH:mm:ss","d MMM yyyy 'at' hh:mm a")}
                </small>
                <small>
                    <c:if test="${claimsDto.inpUser!=''}">
                        by  ${claimsDto.inpUser}
                    </c:if>
                </small>
            </h6>
            <h5 class="hide-sm"
                style=" position: absolute; right: 47%; top: 10px; text-transform: uppercase;">${DbRecordCommonFunctionBean.getValueIdString("claim_policy_status", "V_STATUS_DESC", "V_STATUS_CODE", claimsDto.policyDto.polStatus)}</h5>
            <c:if test="${null != claimsDto.claimNo && claimsDto.claimNo!=0  }">
                <c:if test="${claimsDto.policyDto.vehicleNumber != ''}">
                    <h6 class="text-dark float-right">Vehicle No : ${claimsDto.policyDto.vehicleNumber} </h6>
                    <h6 class="text-dark float-right mr-3">Claim No : ${claimsDto.claimNo} |</h6>
                    <p class="text-danger float-right mr-3">ISF Claim No
                        : ${not empty claimsDto.isfClaimNo  ? claimsDto.isfClaimNo:'PENDING'} |</p>
                </c:if>

                <c:if test="${claimsDto.policyDto.vehicleNumber == ''}">
                    <h6 class="text-dark float-right">Cover Note No : ${claimsDto.coverNoteNo} </h6>
                    <h6 class="text-dark float-right mr-3">Claim No : ${claimsDto.claimNo}</h6>
                </c:if>

            </c:if>
            <div class="clearfix"></div>
        </div>
<%--        <div class="col-sm-12 py-2" style="--%>
<%--            display: flex;--%>
<%--            flex-direction: row;--%>
<%--            justify-content: space-around;--%>
<%--            align-items: center;--%>
<%--            background: #97c9de;--%>
<%--        ">--%>
<%--            <h6 style="margin-bottom: 0; text-transform: uppercase; color: #ff0000;">--%>
<%--                ${(claimsDto.policyDto.product eq "") || (null eq claimsDto.policyDto.product) ? "N/A" : claimsDto.policyDto.product }--%>
<%--            </h6>--%>
<%--            <h6 id="service-factor-header" style="margin-bottom: 0; text-transform: uppercase; color: #ff0000;">--%>
<%--                Service Factors--%>
<%--            </h6>--%>
<%--        </div>--%>
    </div>
    <div>
        <div>
            <div class="ErrorNote"></div>
        </div>
        <form name="frmForm" id="frmForm" method="post">
            <input type="hidden" value="${FORM_TYPE}" name="FORM_TYPE"/>
            <input type="hidden" value="${TYPE}" name="TYPE"/>
            <input type="hidden" value="${latestIntimateDateStatus}" id="latestIntimateDateStatus"/>
            <input type="hidden" value="${latestIntimateCallUser}" id="latestIntimateCallUser"/>
            <input type="hidden" value="${claimsDto.policyDto.latestClmIntimDate}" id="latestClmIntimDate"/>
            <div class="row">
                <div class="col-sm-12">
                    <div id="claimStampContainer" class="stamp-container">
                        <c:if test="${claimsDto.policyDto.categoryDescription eq 'VIP'}">
                            <img src="${pageContext.request.contextPath}/resources/stamps/vip.png"
                                 class="stamp-container-vip"
                            >
                        </c:if>
                    </div>
                    <div class="f1-steps">
                        <div class="f1-progress">
                            <div class="f1-progress-line" data-now-value="10" data-number-of-steps="5"
                                 style="width: 10%;"></div>
                        </div>
                        <div class="f1-step active">
                            <div class="f1-step-icon ${claimsDto.claimStatus>=1?"active":""}">1</div>
                            <p>Call Center</p>
                        </div>
                        <div class="f1-step ${claimsDto.claimStatus>=3?"active":""}">
                            <div class="f1-step-icon">2</div>
                            <p>Assessor Coordinator</p>
                        </div>
                        <div class="f1-step">
                            <div class="f1-step-icon">3</div>
                            <p>Assessor</p>
                        </div>
                        <div class="f1-step">
                            <div class="f1-step-icon">4</div>
                            <p>Motor Engineer</p>
                        </div>
                        <div class="f1-step">
                            <div class="f1-step-icon">5</div>
                            <p>Claim Handler</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col">
                <div id="Note" class="noteDivClass text-right text-danger">Note : <span
                        class="text-muted font-weight-bold">Fields marked with  <span
                        class="text-danger font-weight-bold"> *</span> are mandatory.</span>
                </div>
            </div>
            <%--end new hidden fields--%>
            <div class="row">
                <div class="col-lg-5 scroll" style="height: calc(100vh - 200px);">


                    <fieldset class="border p-2 mt-1" style="display: none;">
                        <div class="row">
                            <div class="col-sm-6" style="display: flex">
                                <div class="btn-group dropright" style="display: flex;justify-content: space-between;align-items: center;">
                                    <button id="service-factor-btn" type="button" class="btn btn-primary dropdown-toggle"  data-toggle="modal"
                                            data-target="#serviceFactorModal" style="display: flex;justify-content: space-between;align-items: center;">
                                       Service Factors
                                    </button>
                                </div>
                            </div>
                        </div>
                    </fieldset>

                    <fieldset class=" border p-2 mt-1">
                        <h6>Customer Details </h6>
                        <hr class="my-2">
                        <div id="accordion">
                            <div class="card ">
                                <div class="card-header p-0" id="efiledetails"
                                     style="background-color: rgba(255, 235, 59, 0.5) !important;">
                                    <h5 class="mb-0">
                                        <a class="btn btn-link" data-toggle="collapse" data-target="#col_efiledetails"
                                           aria-expanded="false" aria-controls="col_efiledetails">
                                            Claim Form
                                        </a>
                                    </h5>
                                </div>
                                <div id="col_efiledetails" class="collapse" aria-labelledby="efiledetails"
                                     data-parent="#accordion">
                                    <div class="card-body p-1">
                                        <div class="row">
                                            <div class="col-12 pdf-thumbnails">
                                                <c:forEach var="estimateDocument"
                                                           items="${claimsDto.estimateDocumentList}">
                                                    <c:set var="iconColorCls" value=" text-dark "/>
                                                    <c:if test="${estimateDocument.documentStatus=='A'}">
                                                        <c:set var="iconColorCls" value=" text-success"/>
                                                    </c:if>
                                                    <c:if test="${estimateDocument.documentStatus=='H'}">
                                                        <c:set var="iconColorCls" value=" text-warning"/>
                                                    </c:if>
                                                    <c:if test="${estimateDocument.documentStatus=='R'}">
                                                        <c:set var="iconColorCls" value=" text-danger"/>
                                                    </c:if>
                                                    <a onclick="viewDocument('${estimateDocument.refNo}','${estimateDocument.refNo}','${PREVIOUS_INSPECTION}','right');"
                                                       href="#"
                                                       class="claimView${estimateDocument.refNo} ${iconColorCls}">
                                                    <span>
                                                        <i class="fa fa-file-pdf-o fa-2x m-3 "></i>
                                                    </span>
                                                    </a>
                                                </c:forEach>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mt-2">
                            <div class="row">
                                <div class="col-sm-6">
                                    <span class="float-left">Customer Name : </span>
                                    <span class="label_Value ml-2"
                                          id="custName">${claimsDto.policyDto.custName}</span>
                                </div>
                                <div class="col-sm-6">
                                    <span>Customer NIC Number : </span>
                                    <span class="label_Value" id="customerNicNo">${claimsDto.policyDto.custNic} </span>
                                </div>
                            </div>
                            <hr class="m-0 mt-2">
                        </div>
                        <div class="form-group ">
                            <div class="row">
                                <div class="col-sm-6">
                                    <span class="float-left">Contact Address : </span>
                                    <span class="label_Value "
                                          id="contactAddress">${claimsDto.policyDto.custAddressLine1} </span>
                                    <span class="label_Value"
                                          id="contactAddress2">${claimsDto.policyDto.custAddressLine2} </span>
                                    <span class="label_Value"
                                          id="contactAddress3">${claimsDto.policyDto.custAddressLine3} </span>
                                </div>
                                <div class="col-sm-6">
                                    <span>Customer Contact Number : </span>
                                    <span class="label_Value "
                                          id="contactNumber">${claimsDto.policyDto.custMobileNo} </span>
                                </div>
                            </div>
                        </div>
                    </fieldset>

                    <fieldset class="border p-2 mt-1">
                        <%--                                    <div class="form-group" >--%>
                        <div class="row">
                            <div class="col-sm-2"
                                 style="display: flex; justify-content: flex-start; align-items: center;">
                                <span>Product Name : </span>
                            </div>
                            <div class="col-sm-8"
                                 style="display: flex; justify-content: center; align-items: center;">
                                <h6 class="font-weight-bold m-0 p-2 border rounded"
                                    style="text-align: center;">
                                    ${(claimsDto.policyDto.product eq "") || (null eq claimsDto.policyDto.product) ? "N/A" : claimsDto.policyDto.product }</h6>
                            </div>
                            <div class="col-sm-2"
                                 style="display: none; justify-content: flex-end; align-items: center;">
                                <button type="button" class="btn btn-primary" name="btnPolyPolicyDetails"
                                        id="btnPolyPolicyDetails" data-toggle="modal"
                                        data-target="#policyDetailsModal">
                                    Policy
                                    Details
                                </button>
                            </div>
                        </div>
                        <%--                                    </div>--%>
                    </fieldset>

                    <iframe id="assessorAllocFrame1" name="assessorAllocFrame1" width="100%"
                            frameborder="0" style="height: calc(100vh - 310px);"
                            class="scroll"
                            src='${pageContext.request.contextPath}/CallCenter/underWrittingDetails?claimNo=${claimsDto.claimNo}&P_POL_REF_NO=${claimsDto.policyDto.policyRefNo}&FORM_TYPE=${FORM_TYPE}'></iframe>
                </div>
                <div class="col-md-7 scroll" style="height: calc(100vh - 200px);">
                    <div class="bd-example bd-example-tabs" id="tabs">
                        <ul class="nav nav-tabs" role="tablist">
                            <li class="nav-item"><a href="#tabs-1" class="nav-link active">Basic Information</a></li>
                            <li class="nav-item"><a href="#tabs-2" class="nav-link">Damage Vehicle Parts</a></li>
                            <li class="nav-item"><a href="#tabs-3" class="nav-link">Third Party Details</a></li>
                            <c:if test="${claimsDto.claimNo != null && claimsDto.claimNo >0 }">
                                </li>
                                <c:if test="${null ==hideAllocation }">
                                    <li class="nav-item"><a href="#tabs-7" class="nav-link">Assessor Allocation</a></li>
                                </c:if>
                                <li class="nav-item"><a href="#tabs-4" class="nav-link">Follow Up Call & Promotions </a>
                                <li class="nav-item"><a href="#tabs-8" class="nav-link">Log Details</a></li>
                            </c:if>
                            <li class="nav-item"><a href="#tabs-9" class="nav-link" onclick="viewSpecialRemarks()">Special
                                Remarks</a></li>
                            <li class="nav-item"><a href="#tabs-10" class="nav-link" onclick="viewClaimFlow()">Claim
                                Flow Details</a></li>
                        </ul>
                        <div class="tab-content">
                            <div class="tab-pane fade active show" role="tabpanel" id="tabs-1">
                                <div class="w-100 scroll " style="height: calc(100vh - 300px);">
                                    <fieldset class="border p-3 ">
                                        <fieldset class="border p-3 ">
                                            <h6> Driver & Reporter Details</h6>
                                            <hr class="my-1">
                                            <c:if test="${isTheftClaim}">
                                                <div class="form-group row" id="divTheftAndFound"
                                                     style="background-color: deepskyblue">
                                                    <label class="col-sm-4 col-form-label"></label>
                                                    <div class="col-sm-8">
                                                        <div class="row">
                                                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 col-sm-12 pl-3 col-form-label check-container">
                                                                <input name="theftAndFound" title="Theft and Found"
                                                                       class="align-middle"
                                                                       type="checkbox"
                                                                       id="theftAndFound"
                                                                       onclick="markTheftAndFound(this.checked)"
                                                                       value="Y"/>
                                                                <span class="checkmark"></span>
                                                                <span class="custom-control-description">Theft and Found </span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </c:if>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Preferred Language <span
                                                        class="text-danger font-weight-bold">  *</span>:</label>
                                                <div class="col-sm-8">
                                                    <div class="row">
                                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-3 col-form-label check-container">
                                                            <input name="preferredLanguage" type="radio"
                                                                   value="E" ${claimsDto.preferredLanguage=='E'?'checked':''}
                                                                   class="align-middle" checked/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">English</span>
                                                        </label>
                                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-3 col-form-label check-container">
                                                            <input name="preferredLanguage" type="radio"
                                                                   value="S" ${claimsDto.preferredLanguage=='S'?'checked':''}
                                                                   class="align-middle"/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">Sinhala</span>
                                                        </label>
                                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-3 col-form-label check-container">
                                                            <input name="preferredLanguage" type="radio"
                                                                   value="T" ${claimsDto.preferredLanguage=='T'?'checked':''}
                                                                   class="align-middle"/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">Tamil</span>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Relationship to Insured <span
                                                        class="text-danger font-weight-bold">  *</span> :</label>
                                                <div class="col-sm-8 input-group">
                                                    <select class=" form-control form-control-sm" id="releshipInsurd"
                                                            name="releshipInsurd"
                                                            style="width: 20%;">
                                                        ${DbRecordCommonFunctionBean.getPopupList("claim_insured_relationship", "n_id", "v_desc")}
                                                    </select>
                                                </div>
                                                <script>
                                                    $("#releshipInsurd").val("${claimsDto.releshipInsurd}");
                                                </script>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Name Of Reporter <span
                                                        class="text-danger font-weight-bold">  *</span>:</label>
                                                <div class="col-sm-8 input-group">
                                                    <select class=" form-control form-control-sm" id="reporterTitle"
                                                            name="reporterTitle"
                                                            style="width: 20%;">
                                                        ${DbRecordCommonFunctionBean.getPopupList("claim_salutation", "salutation_id", "salutation_name")}
                                                    </select>
                                                    <script>
                                                        $("#reporterTitle").val("${claimsDto.reporterTitle}");
                                                    </script>
                                                    <input name="reporterName" class="form-control form-control-sm"
                                                           id="reporterName" maxlength="255" title="Name Of Reporter"
                                                           style="width:80%;"
                                                           type="text" value="${claimsDto.reporterName}"/>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Reporter NIC :</label>
                                                <div class="col-sm-8">
                                                    <input name="reporterId" class="form-control form-control-sm"
                                                           id="reporterId" maxlength="40"
                                                           title="Reporter NIC"
                                                           type="text" value="${claimsDto.reporterId}"/>
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">CLI Number <span
                                                        class="text-danger font-weight-bold">  *</span> :</label>
                                                <div class="col-sm-8">
                                                    <input class=" form-control form-control-sm" name="cliNo"
                                                           id="cliNo" maxlength="10" title="CLI Number" type="text"
                                                           value="${claimsDto.cliNo}"/>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Other Contact No :</label>
                                                <div class="col-sm-8">
                                                    <input class=" form-control form-control-sm" name="otherContNo"
                                                           id="otherContNo"
                                                           title="Other Contact No" type="text"
                                                           value="${claimsDto.otherContNo}"/>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Is Reporter & Driver Same
                                                    :</label>
                                                <div class="col-sm-8">
                                                    <div class="row">
                                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-3 col-form-label check-container">
                                                            <input name="isSameReportDriver"
                                                                   type="radio" ${claimsDto.isSameReportDriver=='Y'?'checked':''}
                                                                   class="align-middle"
                                                                   value="Y" id="radio_button"/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">Yes</span>
                                                        </label>
                                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-3 col-form-label check-container">
                                                            <input name="isSameReportDriver"
                                                                   type="radio"  ${claimsDto.isSameReportDriver=='N'?'checked':''}
                                                                   class="align-middle"
                                                                   value="N" id="radio_button_no"/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">No</span>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Driver Status <span
                                                        class="text-danger font-weight-bold">  *</span> :</label>
                                                <div class="col-sm-8 ">
                                                    <select class="form-control form-control-sm" id="driverStatus"
                                                            name="driverStatus">
                                                        ${DbRecordCommonFunctionBean.getPopupList("claim_driver_status", "N_ID", "V_DRIVER_STATUS_DESC")}
                                                    </select>
                                                </div>
                                                <script>
                                                    $("#driverStatus").val("${claimsDto.driverStatus}");
                                                </script>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Driver's Name :</label>
                                                <div class="col-sm-8 input-group">
                                                    <select class=" form-control form-control-sm" id="driverTitle"
                                                            name="driverTitle"
                                                            style="width: 20%;">
                                                        ${DbRecordCommonFunctionBean.getPopupList("claim_salutation", "salutation_id", "salutation_name")}
                                                    </select>
                                                    <script>
                                                        $("#driverTitle").val("${claimsDto.driverTitle}");
                                                    </script>
                                                    <input name="driverName" id="driverName"
                                                           class=" form-control form-control-sm" maxlength="200"
                                                           title="Driver Name" type="text" style="width:80%;"
                                                           value="${claimsDto.driverName}"/>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Relationship To Insured :</label>
                                                <div class="col-sm-8 input-group">
                                                    <select class=" form-control form-control-sm"
                                                            id="driverReleshipInsurd"
                                                            name="driverReleshipInsurd"
                                                            style="width: 20%;">
                                                        ${DbRecordCommonFunctionBean.getPopupList("claim_insured_relationship", "n_id", "v_desc")}
                                                    </select>
                                                </div>
                                                <script>
                                                    $("#driverReleshipInsurd").val("${claimsDto.driverReleshipInsurd}");
                                                </script>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Driver's NIC :</label>
                                                <div class="col-sm-8">
                                                    <input name="driverNic" id="driverNic" maxlength="20"
                                                           class=" form-control form-control-sm"
                                                           title="Driver National Identity Card Number" type="text"
                                                           value="${claimsDto.driverNic}"/>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-sm-8 offset-sm-4">
                                                    <div id="accordion2">
                                                        <div class="card ">
                                                            <div class="card-header p-0" id="driving_License"
                                                                 style="background-color: rgba(255, 235, 59, 0.5) !important;">
                                                                <h5 class="mb-0">
                                                                    <a class="btn btn-link" data-toggle="collapse"
                                                                       data-target="#driving_License2"
                                                                       aria-expanded="false"
                                                                       aria-controls="driving_License2">
                                                                        Driving License
                                                                    </a>
                                                                </h5>
                                                            </div>
                                                            <div id="driving_License2" class="collapse"
                                                                 aria-labelledby="driving_License2"
                                                                 data-parent="#accordion2">
                                                                <div class="card-body p-1">
                                                                    <div class="row">
                                                                        <div class="col-12 pdf-thumbnails">
                                                                            <a href="" class="">
                                                                                <c:forEach var="drivenLicenseDocument"
                                                                                           items="${claimsDto.drivenLicenseDocumentList}">
                                                                                    <c:set var="iconColorCls"
                                                                                           value=" text-dark "/>
                                                                                    <c:if test="${drivenLicenseDocument.documentStatus=='A'}">
                                                                                        <c:set var="iconColorCls"
                                                                                               value=" text-success"/>
                                                                                    </c:if>
                                                                                    <c:if test="${drivenLicenseDocument.documentStatus=='H'}">
                                                                                        <c:set var="iconColorCls"
                                                                                               value=" text-warning"/>
                                                                                    </c:if>
                                                                                    <c:if test="${drivenLicenseDocument.documentStatus=='R'}">
                                                                                        <c:set var="iconColorCls"
                                                                                               value=" text-danger"/>
                                                                                    </c:if>
                                                                                    <a onclick="viewDocument('${drivenLicenseDocument.refNo}','${drivenLicenseDocument.refNo}','${PREVIOUS_INSPECTION}','left');"
                                                                                       href="#"
                                                                                       class="claimView${drivenLicenseDocument.refNo} ${iconColorCls}">
                                                    <span><i class="fa fa-file-pdf-o fa-2x m-3 ">

                                                    </i></span>
                                                                                    </a>
                                                                                    <%--<script type="text/javascript">--%>
                                                                                    <%--$('.claimView${drivenLicenseDocument.refNo}').popupWindow({--%>
                                                                                    <%--height: screen.height,--%>
                                                                                    <%--width: screen.width,--%>
                                                                                    <%--centerBrowser: 0,--%>
                                                                                    <%--left: 0,--%>
                                                                                    <%--resizable: 1,--%>
                                                                                    <%--centerScreen: 1,--%>
                                                                                    <%--scrollbars: 1,--%>
                                                                                    <%--windowName: 'callCenter${drivenLicenseDocument.refNo}'--%>
                                                                                    <%--});--%>
                                                                                    <%--</script>--%>
                                                                                </c:forEach>
                                                                            </a>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group row mt-2"><label class="col-sm-4 col-form-label">Driver's
                                                Licenses Number :</label>
                                                <div class="col-sm-8">
                                                    <input name="dlNo" id="dlNo" maxlength="20"
                                                           title="Driver License Number" type="text"
                                                           class=" form-control form-control-sm"
                                                           value="${claimsDto.dlNo}"/>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Driver's License Type <span
                                                        class="text-danger font-weight-bold"> *:</span></label>
                                                <div class="col-sm-8 input-group">
                                                    <select class=" form-control form-control-sm"
                                                            id="driverLicenceType"
                                                            name="driverLicenceType"
                                                            style="width: 20%; ">
                                                        ${DbRecordCommonFunctionBean.getPopupList("claim_driver_license_type", "n_id", "v_type")}
                                                    </select>
                                                </div>
                                                <script>
                                                    $("#driverLicenceType").val("${claimsDto.driverLicenceType}");
                                                </script>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Remarks :</label>
                                                <div class="col-sm-8">
                                                <textarea name="reporterRemark" id="reporterRemark"
                                                          class=" form-control form-control-sm"
                                                          title="Remarks" cols=""
                                                          rows="3">${claimsDto.reporterRemark}</textarea>
                                                </div>
                                            </div>
                                        </fieldset>
                                        <fieldset class="border p-3 mt-3">
                                            <h6> Accident Details</h6>
                                            <hr class="my-1">
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Intimation Type <span
                                                        class="text-danger font-weight-bold">  *</span> :</label>
                                                <div class="col-sm-8">
                                                    <div class="row">
                                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                            <input name="intimationType" type="radio"
                                                                   value="1" ${claimsDto.intimationType=='1'?'checked':''}
                                                                   onclick="Intimation_Type_disable_enable('N','${claimsDto.lateIntimateReason}')"
                                                                   class="align-middle" checked/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">On-site</span>
                                                        </label>
                                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                            <input name="intimationType" type="radio"
                                                                   value="2" ${claimsDto.intimationType=='2'?'checked':''}
                                                                   onclick="Intimation_Type_disable_enable('Y','${claimsDto.lateIntimateReason}')"
                                                                   class="align-middle"/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">Late Intimation</span>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label IntimationType text-mute">Reasons
                                                    For
                                                    Late Intimation <span
                                                            class="text-danger font-weight-bold">  *</span> :</label>
                                                <div class="col-sm-8 input-group">
                                                    <select class=" form-control form-control-sm IntimationType disable"
                                                            disabled id="lateIntimateReason"
                                                            name="lateIntimateReason">
                                                        ${DbRecordCommonFunctionBean.getPopupList("claim_late_intimation_reason", "N_ID", "V_INTIMATION_REASON")}
                                                    </select>
                                                </div>
                                                <script type="text/javascript">
                                                    $("#lateIntimateReason").val('${claimsDto.lateIntimateReason}').trigger("chosen:updated");
                                                </script>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Accident
                                                    Date & Time<span
                                                            class="text-danger font-weight-bold"> *</span> :</label>
                                                <div class="col-sm-8 input-group">
                                                    <input name="accidDateTime" class=" form-control form-control-sm"
                                                           id="accidDateTime" title="Accident Date/Time" type="text"
                                                           value="${claimsDto.accidDate} ${claimsDto.accidTime}"
                                                           onkeydown=""/>


                                                    <p id="accidentDateAlert" class="m-0 text-danger"
                                                       style="width: 100%; font-size:11px; ">Accident Date is not with
                                                        in
                                                        Policy Valid Period.</p>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Report Date & Time<span
                                                        class="text-danger font-weight-bold"> *</span> :</label>
                                                <div class="col-sm-8 input-group">
                                                    <input name="dateTimeOfReport" class=" form-control form-control-sm"
                                                           id="dateTimeOfReport" title="Report Date/Time" type="text"
                                                           onkeydown=""
                                                           value="${claimsDto.dateOfReport} ${claimsDto.timeOfReport}"/>

                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label ">Cause of loss
                                                    <span
                                                            class="text-danger font-weight-bold">  *</span> :</label>
                                                <div class="col-sm-8">
                                                    <select class=" form-control form-control-sm" id="causeOfLoss"
                                                            tabindex="-1"
                                                            name="causeOfLoss">
                                                        ${DbRecordCommonFunctionBean.getPopupList("claim_cause_of_loss_type", "N_ID", "V_CAUSE_OF_LOSS")}
                                                    </select>
                                                </div>
                                                <script>
                                                    $("#causeOfLoss").val("${claimsDto.causeOfLoss}");
                                                </script>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label"></label>
                                                <div class="col-sm-8">
                                                    <div class="row">
                                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 col-sm-12 pl-3 col-form-label check-container">
                                                            <input name="isCatEvent" title="Is Policy CAT Event"
                                                                   class="align-middle checkbox_check"
                                                                   type="checkbox" ${claimsDto.isCatEvent=='Y'?'checked':''}
                                                                   id="isCatEvent"
                                                                   value="Y"/>
                                                            <span class="checkmark"></span>
                                                            <span class="custom-control-description">Is CAT Event </span>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label iscat text-mute">CAT Code <span
                                                        class="text-danger font-weight-bold">  *</span> :</label>
                                                <div class="col-sm-8">
                                                    <select class=" form-control form-control-sm iscat"
                                                            id="catEventCode"
                                                            name="catEventCode">
                                                        ${DbRecordCommonFunctionBean.getPopupList("claim_cat_event", "N_ID", "V_CAT_EVENT_DESC")}
                                                    </select>
                                                </div>
                                                <script>
                                                    $("#catEventCode").val("${claimsDto.catEventCode}");
                                                </script>
                                            </div>
                                            <div class="form-group row d-none">
                                                <label class="col-sm-4 col-form-label iscat">CAT Event <span
                                                        class="text-danger font-weight-bold">  *</span> :</label>
                                                <div class="col-sm-8 input-group">
                                                    <select class=" form-control form-control-sm iscat" id="catEvent"
                                                            name="catEvent">
                                                        <option value="">Select</option>
                                                        <option value="1">cat123456</option>
                                                        <option value="2">dsfdsf</option>
                                                        <option value="3">vvvv</option>
                                                    </select>
                                                </div>
                                                <script>
                                                    $("#catEvent").val("${claimsDto.catEvent}");
                                                </script>
                                            </div>
                                            <div class="form-group row mt-2">
                                                <label class="col-sm-4 col-form-label" for="accidDesc">Accident Description<span
                                                        id="accidDescMandatory" class="text-danger font-weight-bold" style="display: none;"> *</span> :</label>
                                                <div class="col-sm-8">
                                                <textarea name="accidDesc" id="accidDesc"
                                                          class=" form-control form-control-sm"
                                                          title="Accident Description" cols=""
                                                          rows="3">${claimsDto.accidDesc}</textarea>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Place Of Accident<span
                                                        class="text-danger font-weight-bold"> *</span> :</label>
                                                <div class="col-sm-8">
                                                    <input name="placeOfAccid" class=" form-control form-control-sm"
                                                           id="placeOfAccid" title="Place Of Accident" maxlength="200"
                                                           type="text" value="${claimsDto.placeOfAccid}"/>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">District :</label>
                                                <div class="col-sm-8 input-group">
                                                    <select class=" form-control form-control-sm " id="districtCode"
                                                            name="districtCode">
                                                        ${DbRecordCommonFunctionBean.getPopupList("claim_district", "N_TXN_ID", "V_DISTRICT_NAME")}
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Nearest City :</label>
                                                <div class="col-sm-8 input-group">
                                                    <select class=" form-control form-control-sm " id="nearestCity"
                                                            name="nearestCity">
                                                        ${DbRecordCommonFunctionBean.getPopupList("claim_gramas", "N_GRAMA_CODE", "V_GRAMA_NAME")}
                                                    </select>
                                                </div>
                                                <script>
                                                    $("#districtCode").val("${claimsDto.districtCode}");
                                                    $("#nearestCity").val("${claimsDto.nearestCity}");
                                                </script>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Current Location Of The Vehicle
                                                    :</label>
                                                <div class="col-sm-8">
                                                    <input name="currentLocation"
                                                           class=" form-control form-control-sm"
                                                           id="currentLocation" title="Current Locaton Of The Vehicle"
                                                           maxlength="200"
                                                           type="text" value="${claimsDto.currentLocation}"/>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Nearest Police Station <span
                                                        class="text-danger font-weight-bold"> *:</span></label>
                                                <div class="col-sm-8 input-group">
                                                    <select class=" form-control form-control-sm "
                                                            id="nearPoliceStation"
                                                            name="nearPoliceStation">
                                                        ${DbRecordCommonFunctionBean.getPopupList("claim_police_station", "N_REF_NO", "V_POLICE_NAME")}
                                                    </select>
                                                </div>
                                                <script>
                                                    $("#nearPoliceStation").val("${claimsDto.nearPoliceStation}");
                                                </script>
                                            </div>
                                            <fieldset class="border px-2 mt-2">
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label"> Is 1<sup>st</sup> Statement
                                                        Required
                                                        <span class="text-danger font-weight-bold"> *:</span>
                                                    </label>
                                                    <div class="col-sm-8">
                                                        <div class="row">
                                                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-md-2 col-form-label check-container">
                                                                <input name="isFirstStatementReq" type="radio"
                                                                       class="align-middle"
                                                                       value="Y" ${claimsDto.isFirstStatementReq=='Y'?'checked':''}
                                                                       onclick="Statement_disable_enable('Y');"/>
                                                                <span class="radiomark"></span>
                                                                <span class="custom-control-description">Yes</span>
                                                            </label>
                                                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-md-2 col-form-label check-container">
                                                                <input name="isFirstStatementReq" type="radio"
                                                                       class="align-middle"
                                                                       value="N" ${claimsDto.isFirstStatementReq=='N'?'checked':''}
                                                                       onclick="Statement_disable_enable('N');"/>
                                                                <span class="radiomark"></span>
                                                                <span class="custom-control-description">No</span>
                                                            </label>
                                                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-md-5 col-form-label check-container">
                                                                <input name="isFirstStatementReq" type="radio"
                                                                       class="align-middle"
                                                                       value="WD" ${claimsDto.isFirstStatementReq=='WD'?'checked':''}
                                                                       onclick="Statement_disable_enable('N');"/>
                                                                <span class="radiomark"></span>
                                                                <span class="custom-control-description">Assessor Will Decide</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label disable text-mute">Reason
                                                        <span
                                                                class="text-danger font-weight-bold">  *</span>
                                                        :</label>
                                                    <div class="col-sm-8">
                                                        <select class=" form-control form-control-sm "
                                                                id="firstStatementReqReason"
                                                                name="firstStatementReqReason">
                                                            ${DbRecordCommonFunctionBean.getPopupList("claim_first_statement_reason", "N_ID", "V_REASON")}
                                                        </select>
                                                    </div>
                                                    <script>
                                                        $("#firstStatementReqReason").val("${claimsDto.firstStatementReqReason}");
                                                    </script>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">Remarks :</label>
                                                    <div class="col-sm-8">
                                                <textarea name="firstStatementRemark" id="firstStatementRemark"
                                                          class=" form-control form-control-sm"
                                                          title="Damage Remarks" cols=""
                                                          rows="3">${claimsDto.firstStatementRemark}</textarea>
                                                    </div>
                                                </div>
                                            </fieldset>
                                            <fieldset class="border px-2 mt-2">
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">Doubt Claim</label>
                                                    <input type="hidden" id="stat" name="stat"
                                                           value="${claimsDto.isDoubt}"/>
                                                    <div class="col-sm-8">
                                                        <div class="row">
                                                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                <input name="isDoubt" onclick="changeStat('Y')"
                                                                       type="radio" ${claimsDto.isDoubt=='Y'?'checked':''}
                                                                       class="align-middle" value="Y"/>
                                                                <span class="radiomark"></span>
                                                                <span class="custom-control-description">Yes</span>
                                                            </label>
                                                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                <input name="isDoubt" onclick="changeStat('N')"
                                                                       type="radio" ${claimsDto.isDoubt=='N'?'checked':''}
                                                                       class="align-middle" value="N"/>
                                                                <span class="radiomark"></span>
                                                                <span class="custom-control-description">No</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label isDoubtRemark">Remarks<span
                                                            class="text-danger font-weight-bold"> *</span> :</label>
                                                    <div class="col-sm-8">
                                                <textarea name="isDoubtRemark" id="isDoubtRemark"
                                                          class=" form-control form-control-sm isDoubtRemark"
                                                          title="Remark" cols=""
                                                          rows="3">${claimsDto.isDoubtRemark}</textarea>
                                                        <p class="help-block errormessageremark text-danger"
                                                           id="errormessage"
                                                           data-fv-result="INVALID" style="display: none;">This field is
                                                            required and
                                                            cannot be empty.</p>
                                                    </div>
                                                </div>
                                                <div class="text-right">
                                                    <c:if test="${null != claimsDto.claimNo && claimsDto.claimNo!=0}">
                                                        <button type="button" name="cancel" id="isDoubtRemarkButton"
                                                                class="btn btn-primary mb-2 isDoubtRemark"
                                                                onclick="updateDeboutClaimButton()" disabled>
                                                            Update
                                                        </button>
                                                    </c:if>
                                                </div>
                                            </fieldset>

                                            <div class="form-group row d-none">
                                                <label class="col-sm-4 col-form-label">Inspection Type :</label>
                                                <div class="col-sm-8">
                                                    <div class="row">
                                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                            <input name="inspectionType" type="radio"
                                                                   class="align-middle"
                                                                   value="OnSite" ${claimsDto.inspectionType=='OnSite'?'checked':''}
                                                                   onclick="Inspection_Type_disable_enable('N');"/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">On-Site</span>
                                                        </label>
                                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                            <input name="inspectionType" type="radio"
                                                                   class="align-middle"
                                                                   value="CallEstimate"  ${claimsDto.inspectionType=='CallEstimate'?'checked':''}
                                                                   onclick="Inspection_Type_disable_enable('Y');"/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">Call Estimate</span>
                                                        </label>
                                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                            <input name="inspectionType" type="radio"
                                                                   class="align-middle"
                                                                   value="OffSite"  ${claimsDto.inspectionType=='OffSite'?'checked':''}
                                                                   onclick="Inspection_Type_disable_enable('Y');"/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">Off-site</span>
                                                        </label>
                                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                            <input name="inspectionType" type="radio"
                                                                   class="align-middle"
                                                                   value="Other"  ${claimsDto.inspectionType=='Other'?'checked':''}
                                                                   onclick="Inspection_Type_disable_enable('Y');"/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">Other</span>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group row d-none">
                                                <label class="col-sm-4 col-form-label Inspectiondisable text-mute">Reason
                                                    :</label>
                                                <div class="col-sm-8">
                                                    <select class=" form-control form-control-sm Inspectiondisable text-mute"
                                                            id="inspectionTypeReason" name="inspectionTypeReason"
                                                            disabled>
                                                        ${DbRecordCommonFunctionBean.getPopupList("claim_inspection_type_reason", "N_ID", "V_REASON")}
                                                    </select>
                                                </div>
                                                <script>
                                                    $("#inspectionTypeReason").val("${claimsDto.inspectionTypeReason}");
                                                </script>
                                            </div>
                                            <div class="form-group row mt-2">
                                                <label class="col-sm-4 col-form-label">Draft Reason :</label>
                                                <div class="col-sm-8">
                                                    <select class=" form-control form-control-sm" id="draftReason"
                                                            name="draftReason">
                                                        ${DbRecordCommonFunctionBean.getPopupList("claim_draft_reason", "N_REF_ID", "V_REASION")}
                                                    </select>
                                                </div>
                                                <script>
                                                    $("#draftReason").val("${claimsDto.draftReason}");
                                                </script>
                                            </div>
                                            <c:if test="${FORM_TYPE!='HISTORY'}">
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label"><b>Special Remarks
                                                        :</b></label>
                                                    <div class="col-sm-8">
                                                <textarea name="basicInformationRemark"
                                                          class=" form-control form-control-sm"
                                                          id="basicInformationRemark"
                                                          title="Remarks" cols="" rows="3"></textarea>
                                                    </div>
                                                    <div class="col text-right">
                                                        <button type="button" name="cancel" id="remarkButton"
                                                                name="remarkButton"
                                                                class="btn btn-primary mt-2"
                                                                onclick="addRemarks('1','Basic Information','basicInformationRemark')">
                                                            Add Remarks
                                                        </button>
                                                    </div>
                                                </div>
                                            </c:if>
                                        </fieldset>
                                    </fieldset>
                                </div>
                            </div>
                            <div class="tab-pane fade" role="tabpanel" id="tabs-2">
                                <fieldset class="border p-2 ">
                                    <div class="form-group row">
                                        <label class="col-sm-2 col-form-label"></label>
                                        <div class="col-sm-10">
                                            <div class="row">
                                                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                    <input name="chkIsNoDamage" onclick="selectDamage()"
                                                           title="Is No Damage" type="checkbox" class="align-middle"
                                                           id="chkIsNoDamage"
                                                           name="" ${claimsDto.isNoDamage=='Y'?'checked':''}
                                                           value="Y"/>

                                                    <input name="isNoDamage" id="isNoDamage"
                                                           value="${claimsDto.isNoDamage}"/>

                                                    <span class="checkmark"></span>
                                                    <span class="custom-control-description">No Damages </span>
                                                </label>
                                                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                    <input name="chkDamageNotGiven" onclick="selectDamageNotGiven()"
                                                           title="Damage Parts not Given" type="checkbox"
                                                           class="align-middle"
                                                           id="chkDamageNotGiven"${claimsDto.damageNotGiven=='Y'?'checked':''}
                                                           name="chkDamageNotGiven"
                                                           value="Y"/>
                                                    <input name="damageNotGiven" id="damageNotGiven"
                                                           value="${claimsDto.damageNotGiven}"/>

                                                    <span class="checkmark"></span>
                                                    <span class="custom-control-description">Damage Parts not Given</span>
                                                </label>
                                                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                    <input name="chkIsHugeDamage"
                                                           id="chkIsHugeDamage" ${claimsDto.isHugeDamage=='Y'?'checked':''}
                                                           title="Huge Damage" type="checkbox" class="align-middle"
                                                           value="Y" onclick="serveDamage()"/>
                                                    <input name="isHugeDamage" id="isHugeDamage"
                                                           value="${claimsDto.isHugeDamage}"/>
                                                    <span class="checkmark"></span>
                                                    <span class="custom-control-description">Severe Damage</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row formcontrolhide">
                                        <label class="col-sm-2 col-form-label">Class Of Vehicle <span
                                                class="text-danger font-weight-bold"> *</span> :</label>
                                        <div class="col-sm-6 pr-sm-3">
                                            <select name="vehClsId" id="vehClsId"
                                                    class=" form-control form-control-sm"
                                                    onchange="viewDamageBodyParts('${pageContext.request.contextPath}/CallCenter/viewDamagePart?TYPE=0&FORM_TYPE=${FORM_TYPE}',this.value);setURL(this.value); ">
                                                ${DbRecordCommonFunctionBean.getPopupList("claim_vehicle_class", "N_VEH_CLS_ID", "V_VEH_CLS_NAME")}
                                            </select>
                                            <script>
                                                $("#vehClsId").val("${claimsDto.vehClsId}");
                                            </script>
                                        </div>
                                        <div class="col-sm-2 pl-0">
                                            <div id="SpanViewVehicleModel" style="display: none;">
                                                <a name="viewVehicleModel" class="viewVehicleModel" href="#"
                                                   onclick="viewVehicleModelImage()">
                                                    <input type="button" name="cmdViewImage"
                                                           id="cmdViewImage" value="View Image"
                                                           class="btn btn-secondary" style="    padding: 5px 10px;"/>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row formcontrolhide">
                                        <label class="col-sm-12 col-form-label">Damage Vehicle Parts<span
                                                class="text-danger font-weight-bold"> *</span> :
                                            <span class="warning-mg" id="vehicalParts" style="display: none;">Please Select Damage Vehicle Parts</span>
                                        </label>
                                        <div class="col-sm-12">
                                            <div id="DamagePartsView" class="border"
                                                 style="overflow:auto;max-height:400px; min-height:40px; border-radius: .2rem;">
                                                <p class="p-2">Please Select Vehicle
                                                    Class</p></div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Damage Remarks :</label>
                                        <div class="col-sm-8">
                                            <textarea name="damageRemark" id="damageRemark"
                                                      class=" form-control form-control-sm" title="Damage Remarks"
                                                      cols="" rows="3">${claimsDto.damageRemark}</textarea>
                                        </div>
                                    </div>
                                    <fieldset class="border px-2 ">
                                    </fieldset>

                                </fieldset>
                            </div>
                            <div class="tab-pane fade" role="tabpanel" id="tabs-3">
                                <fieldset class="border py-2">
                                    <iframe width="100%" style="height: calc(100vh - 310px);" frameborder="0"
                                            class="scroll"
                                            src="${pageContext.request.contextPath}/CallCenter/viewThirdPartyDetails?P_N_CLIM_NO=${claimsDto.claimNo}"></iframe>
                                </fieldset>
                            </div>
                            <div class="tab-pane fade collapse ${tabIndex==3?'show':''}" role="tabpanel" id="tabs-4">
                                <fieldset class="border p-2">
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Call Done By :</label>
                                        <div class="col-sm-8">
                                            <input name="followCallUserId" id="followCallUserId"
                                                   title="Call Done By" type="text"
                                                   value="${claimsDto.followCallUserId}"
                                                   class=" form-control form-control-sm" readonly/>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Contact Person <span
                                                class="text-danger font-weight-bold"> *</span>:</label>
                                        <div class="col-sm-8 input-group">
                                            <select class=" form-control form-control-sm"
                                                    name="followCallContactPersonTitle"
                                                    id="followCallContactPersonTitle" style="width: 20%;"
                                                    onchange="removeTitleValidation();">
                                                ${DbRecordCommonFunctionBean.getPopupList("claim_salutation", "salutation_id", "salutation_name")}
                                            </select>
                                            <script>
                                                $("#followCallContactPersonTitle").val("${claimsDto.followCallContactPersonTitle}");
                                            </script>
                                            <input name="followCallContactPersonName"
                                                   class="form-control form-control-sm" id="followCallContactPersonName"
                                                   value="${claimsDto.followCallContactPersonName}"
                                                   title="" style="width:80%;" type="text">
                                            <div class="invalid-feedback" id="nameDiv">
                                                This field is required and cannot be empty.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Contact Number <span
                                                class="text-danger font-weight-bold"> *</span> :</label>
                                        <div class="col-sm-8">
                                            <input name="followCallContactNumber" class="form-control form-control-sm"
                                                   id="followCallContactNumber" maxlength="10"
                                                   title="Contact Number" type="text"
                                                   value="${claimsDto.followCallContactNumber}">
                                            <div class="invalid-feedback" id="contactDiv">
                                                This field is required and cannot be empty.
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>
                                <fieldset class="border p-2">
                                    <div class="bd-example bd-example-tabs">
                                        <ul class="nav nav-tabs" id="Claim-tabs" role="tablist">
                                            <li class="nav-item">
                                                <a href="#tabs-31" class="nav-link active">Customer
                                                    Satisfaction</a></li>
                                            <c:if test="${claimsDto.policyDto.ncbAmount > 0}">
                                                <li class="nav-item"><a href="#tabs-41" class="nav-link">NCB
                                                    Details</a></li>
                                            </c:if>
                                            <li class="nav-item" id="tab-51"><a href="#tabs-51"
                                                                                class="nav-link">Empanel /
                                                LOMO</a></li>
                                        </ul>
                                        <div class="tab-content">
                                            <div class="tab-pane fade active show" role="tabpanel" id="tabs-31">
                                                <fieldset class="border p-2 ">
                                                    <div class="form-group row">
                                                        <label class="col-sm-4 col-form-label">Customer Satisfaction
                                                            :</label>
                                                        <div class="col-sm-8">
                                                            <div id="followCallAgnetServiceRateDiv"
                                                                 class="starrr"></div>
                                                            <h6 class="rating"></h6>
                                                        </div>
                                                    </div>
                                                    <div class="form-group row">
                                                        <label class="col-sm-4 col-form-label">Assessor Service
                                                            :</label>
                                                        <div class="col-sm-8">
                                                            <div id="followCallAssessorServiceRateDiv"
                                                                 class="starrr"></div>
                                                            <h6 class="rating"></h6>
                                                        </div>
                                                    </div>
                                                    <input type="hidden" value="${claimsDto.followCallAgnetServiceRate}"
                                                           name="followCallAgnetServiceRate"
                                                           id="followCallAgnetServiceRate">
                                                    <input type="hidden"
                                                           value="${claimsDto.followCallAssessorServiceRate}"
                                                           name="followCallAssessorServiceRate"
                                                           id="followCallAssessorServiceRate">
                                                    <c:if test="${FORM_TYPE!='HISTORY'}">
                                                        <div class="form-group row">
                                                            <label class="col-sm-4 col-form-label"><b>Special Remarks
                                                                :</b></label>
                                                            <div class="col-sm-8">
                                                            <textarea name="satisfactionSpecialRemarks" maxlength="300"
                                                                      id="satisfactionSpecialRemarks" title="Remarks"
                                                                      cols="" rows="6"
                                                                      class=" form-control form-control-sm"></textarea>
                                                            </div>
                                                            <div class="col text-right">
                                                                <button type="button" name="cancel" id="remarkButton"
                                                                        name="remarkButton"
                                                                        class="btn btn-primary mt-2"
                                                                        onclick="addRemarks('1','Customer Satisfaction','satisfactionSpecialRemarks')">
                                                                    Add Remarks
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </c:if>
                                                </fieldset>
                                            </div>
                                            <div class="tab-pane fade" role="tabpanel" id="tabs-41">
                                                <fieldset class="border p-2 ">
                                                    <div class="form-group row">
                                                        <label class="col-sm-4 col-form-label">NCB Promoted :</label>
                                                        <div class="col-sm-8">
                                                            <div class="row">
                                                                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-3 col-form-label check-container">
                                                                    <input name="ncbProm"
                                                                           type="radio" ${claimsDto.ncbProm=='Y'?'checked':''}
                                                                           class="align-middle" value="Y"/>
                                                                    <span class="radiomark"></span>
                                                                    <span class="custom-control-description">Yes</span>
                                                                </label>
                                                                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-3 col-form-label check-container">
                                                                    <input name="ncbProm"
                                                                           type="radio" ${claimsDto.ncbProm=='N'?'checked':''}
                                                                           class="align-middle" value="N"
                                                                           checked="checked"/>
                                                                    <span class="radiomark"></span>
                                                                    <span class="custom-control-description">No</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="form-group row">
                                                        <label class="col-sm-4 col-form-label" id="lblV_ncb_cus_agre">Reason
                                                            :</label>
                                                        <div class="col-sm-8">
                                                            <select name="ncbReason" id="ncbReason"
                                                                    class=" form-control form-control-sm">
                                                                ${DbRecordCommonFunctionBean.getPopupList("claim_ncb_reason", "N_ID", "V_REASON")}
                                                            </select>
                                                            <script>
                                                                $("#ncbReason").val("${claimsDto.ncbReason}");
                                                            </script>
                                                        </div>
                                                    </div>
                                                    <div class="form-group row">
                                                        <label class="col-sm-4 col-form-label"><b>Special Remarks :</b></label>
                                                        <div class="col-sm-8">
                                                  <textarea name="ncbRemark" id="ncbRemark" title="Remarks"
                                                            maxlength="300"
                                                            cols="" rows="3" class=" form-control form-control-sm"
                                                            onkeydown="focusToTab(event,'tabs-51')">${claimsDto.ncbRemark}</textarea>
                                                        </div>
                                                    </div>
                                                </fieldset>
                                            </div>
                                            <div class="tab-pane fade" role="tabpanel" id="tabs-51">
                                                <fieldset class="border p-2 ">
                                                    <div class="form-group row">
                                                        <label class="col-sm-4 col-form-label">LOMO Promoted :</label>
                                                        <div class="col-sm-8">
                                                            <div class="row">
                                                                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-3 col-form-label check-container">
                                                                    <input name="lomoProm"
                                                                           type="radio" ${claimsDto.lomoProm=='Y'?'checked':''}
                                                                           class="align-middle" value="Y"/>
                                                                    <span class="radiomark"></span>
                                                                    <span class="custom-control-description">Yes</span>
                                                                </label>
                                                                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-3 col-form-label check-container">
                                                                    <input name="lomoProm"
                                                                           type="radio" ${claimsDto.lomoProm=='N'?'checked':''}
                                                                           class="align-middle" value="N"
                                                                           />
                                                                    <span class="radiomark"></span>
                                                                    <span class="custom-control-description">No</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="form-group row">
                                                        <label class="col-sm-4 col-form-label">Reason
                                                            :</label>
                                                        <div class="col-sm-8">
                                                            <select name="lomoReason" id="lomoReason"
                                                                    class=" form-control form-control-sm">
                                                                ${DbRecordCommonFunctionBean.getPopupList("claim_lomo_reason", "N_ID", "V_REASON")}
                                                            </select>
                                                            <script>
                                                                $("#lomoReason").val("${claimsDto.lomoReason}");
                                                            </script>
                                                        </div>
                                                    </div>
                                                    <c:if test="${FORM_TYPE!='HISTORY'}">
                                                        <div class="form-group row">
                                                            <label class="col-sm-4 col-form-label"><b>Special Remarks
                                                                :</b></label>
                                                            <div class="col-sm-8">
                                                  <textarea name="lomoPromotedRemarks" id="lomoPromotedRemarks"
                                                            maxlength="300"
                                                            title="Remarks"
                                                            rows="3" class=" form-control form-control-sm"
                                                            onkeydown="focusToTab(event,'tabs-5')"></textarea>
                                                                <div class="col text-right">
                                                                    <button type="button" name="cancel"
                                                                            id="remarkButton"
                                                                            name="remarkButton"
                                                                            class="btn btn-primary mt-2"
                                                                            onclick="addRemarks('1','Empanel / LOMO','lomoPromotedRemarks')">
                                                                        Add Remarks
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </c:if>
                                                </fieldset>
                                            </div>
                                            <c:if test="${FORM_TYPE!='HISTORY' && claimsDto.isFollowupCallDone!='Y' }">
                                                <div class="mt-2 text-right">
                                                    <button type="button" name="cmdFollowUpCall" id="cmdFollowUpCall"
                                                            value="FUP"
                                                            title="Save Call & Promotions" onclick="saveFollowUpCall()"
                                                            class="btn btn-success ">Save Call & Promotions
                                                    </button>
                                                </div>
                                            </c:if>
                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="tab-pane fade" role="tabpanel" id="tabs-7">
                                <iframe id="assessorAllocFrame" name="assessorAllocFrame" width="100%"
                                        frameborder="0" style="height: calc(100vh - 310px);"
                                        class="scroll"
                                        src='${pageContext.request.contextPath}/CallCenter/viewAssessorAllocation?claimNo=${claimsDto.claimNo}&FORM_TYPE=${FORM_TYPE}&onsiteReview=${onsiteReview}&requestedInspectionId=${requestedInspectionId}&isOnsiteReviewAssignedUser=${isOnsiteReviewAssignedUser}'></iframe>

                            </div>
                            <script>
                                if ('${claimsDto.claimStatus}' == '30') {
                                    $('#tabs a[href="#tabs-7"]').tab('show');

                                } else if (${(TYPE == '2' or TYPE == '5' or TYPE == '6')  and   tabIndex ne 3}) {
                                    $('#tabs a[href="#tabs-7"]').tab('show');

                                } else {
                                    $('#tabs a[href="#tabs-1"]').tab('show');
                                }

                                if (${tabIndex eq 3}) {
                                    $('#tabs a[href="#tabs-4"]').tab('show');

                                }


                            </script>
                            <div class="tab-pane fade" role="tabpanel" id="tabs-8">
                                <fieldset class="border p-2 ">
                                    <h6>Log Details</h6>
                                    <div class="form-group row">
                                        <div class="col-sm-12">
                                            <div class="w-100 scroll" style="height: calc(100vh - 350px);">
                                                <c:forEach var="logDetail" items="${claimsDto.logList}">
                                                    <a href="#"
                                                       class="list-group-item list-group-item-action flex-column align-items-start">
                                                        <div class="font-bg log-left"
                                                             style="width: 50px; height:50px; overflow: hidden; margin-top: 25px;">
                                                            <h2 class="name text-white">${logDetail.userId}</h2>
                                                        </div>
                                                        <div class="float-left log-right">
                                                            <div class="w-100">
                                                                    <%--<h5 class="mb-1">${logDetail.userId}</h5>--%>
                                                                <p class="text-right mb-0">
                                                                    claim-${logDetail.claimNo}</p>
                                                            </div>
                                                            <hr class="m-0 mt-1 mb-1">
                                                            <div class="row">
                                                                <div class="col-md-6">
                                                                    <p class="mb-0">Changed Field</p>
                                                                    <p class="mb-1"><b>${logDetail.fieldName}</b></p>
                                                                </div>
                                                                <div class="col-md-6 border-left">
                                                                    <p class="mb-0">Changed Value</p>
                                                                    <p class="mb-1"><b>${logDetail.fieldValue}</b></p>
                                                                </div>
                                                            </div>
                                                            <hr class="m-0 mt-1 mb-1">
                                                            <h6 class="float-right">${logDetail.userId} </h6>
                                                            <p class="float-left">${logDetail.inputDateTime}</p>
                                                        </div>
                                                        <div class="clearfix"></div>
                                                    </a>
                                                </c:forEach>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>
                            </div>

                            <div class="tab-pane fade" role="tabpanel" id="tabs-9">
                                <fieldset class="border p-2 ">
                                    <h6>Special Remarks</h6>
                                    <div class="form-group row">
                                        <div class="col-sm-12">
                                            <div id="specialRemaksDiv" class="scroll"
                                                 style="height: calc(100vh - 350px);">
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="tab-pane fade" role="tabpanel" id="tabs-10">
                                <fieldset class="border p-2 ">
                                    <div class="form-group row">
                                        <div class="col-sm-12">
                                            <div id="claimWorkflowDiv" class="scroll"
                                                 style="height: calc(100vh - 350px);">
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                        </div>
                    </div>
                    <div style="DISPLAY: none" class=demo-description>
                        <P>Click tabs to swap between content that is broken into logical sections.</P>
                    </div>
                    <input name="txtN_ref_no" id="txtN_ref_no" type="hidden" value=""/>
                    <c:if test="${ FORM_TYPE!='HISTORY'}">
                        <c:if test="${(TYPE !=4 && TYPE !=3)}">
                            <div class="mt-3 btndisable">
                                <c:if test="${userRight.v_modify=='checked' && (claimsDto.claimStatus==2 || claimsDto.claimStatus==3 || claimsDto.claimStatus==30 && claimsDto.isfsUpdateStatus=='N')}">
                                    <%--<button type="submit" name="isfUpdate" id="isfUpdate"
                                            title="ISF Update" value="ISF"
                                            class="btn btn-success ">ISF Update
                                    </button>--%>
                                </c:if>
                                <c:if test="${userRight.v_modify=='checked' && (claimsDto.claimStatus==0 || claimsDto.claimStatus==1 || claimsDto.claimStatus==31)}">
                                    <button type="submit" name="IntimateClaim" id="cmdIntimateClaim"
                                            title="Intimate Claim" value="PS"
                                            class="btn btn-success ">Intimate Claim
                                    </button>
                                </c:if>
                                <c:if test="${userRight.v_input=='checked' && (claimsDto.claimStatus==0 || claimsDto.claimStatus==1 || claimsDto.claimStatus==31)}">
                                    <button type="submit" name="cmdSaveForward" id="cmdSaveForward"
                                            title="Save & Forward To Assessor Department"
                                            value="FW"
                                            class="btn btn-success ">Save & Forward
                                    </button>
                                </c:if>
                                <c:if test="${userRight.v_input=='checked' && (claimsDto.claimStatus==0 || claimsDto.claimStatus==1 || claimsDto.claimStatus==31)}">
                                    <button type="button" name="cmdSaveDraft" id="cmdSaveDraft" title="Save as Draft"
                                            onclick="saveConfirm('DR')" class="btn btn-primary ">Save as Draft
                                    </button>
                                </c:if>
                                <c:if test="${FORM_TYPE!='HISTORY'}">
                                    <div class="float-right">
                                            <%--<c:if test="${userRight.v_input=='checked' && (claimsDto.claimStatus!=23 && claimsDto.claimStatus==1)}">
                                                <button type="button" name="cmdReject" id="cmdReject" value="Reject"
                                                        onclick="submitPage('REJ')"
                                                        class="btn btn-danger ${claimsDto.claimNo == 0 ? 'd-none':''}">
                                                    Reject
                                                </button>
                                            </c:if>--%>
                                        <button type="button" name="cmdClose" id="cmdClose" value="Close"
                                                onclick="backConfirm('${TYPE}')" class="btn btn-link"><b> Back</b>
                                        </button>
                                    </div>
                                </c:if>
                            </div>
                        </c:if>
                        <c:if test="${TYPE ==3 || TYPE ==4}">
                            <div class="mt-3 float-right">
                                <button type="button" name="cmdClose" id="cmdClose" value="Close"
                                        onclick="backConfirm('${TYPE}')" class="btn btn-link"><b> Back</b>
                                </button>
                            </div>
                        </c:if>
                        <c:if test="${TYPE ==4}">
                            <div class="mt-3">
                                <button type="button" name="IntimateClaim"
                                        title="View Policy" id="viewPolicyModal"
                                        class="btn btn-success " onclick="policyDetailsView()">View Policy
                                </button>
                                <c:if test="${null !=ISMAP && ISMAP == 'Y'}">
                                    <button type="button" name="cmdMapPolicy" id="mapPolicyId"
                                            title="Map Policy"
                                            class="btn btn-success " onclick="mappingConfirm()">Map Policy
                                    </button>
                                </c:if>

                            </div>
                            <!-- End demo -->
                        </c:if>
                    </c:if>
                    <!-- End demo -->
                </div>
                <c:if test="${claimsDto.claimStatus!=0}">
                    <fieldset class="col-md-12">
                        <div id="accordion3">
                            <div class="card mt-2">
                                <div class="card-header p-0" id="heading8">
                                    <h5 class="mb-0">
                                        <a class="btn btn-link" data-toggle="collapse" data-target="#collapse8"
                                           aria-expanded="false" aria-controls="collapse8">
                                            Photo Comparison
                                        </a>
                                    </h5>
                                </div>
                                <div id="collapse8" class="collapse " aria-labelledby="heading8"
                                     data-parent="#accordion3">
                                    <div class="card-body p-0">
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <iframe width="50%" style="height: 100vh;" frameborder="0"
                                                        id="iframePhotoCom1" name="iframePhotoCom1"
                                                        height="90vh"
                                                        class="scroll float-left"
                                                        src="${pageContext.request.contextPath}/PhotoComparisonController/initViewComparisonViewer?vehicleNumber=${claimsDto.vehicleNo}&comparisionTabNo=1&policyNumber=${claimsDto.policyDto.policyNumber}"></iframe>
                                                <iframe width="50%" style="height: 100vh;" frameborder="0"
                                                        id="iframePhotoCom2" name="iframePhotoCom2"
                                                        height="90vh"
                                                        class="scroll float-left"
                                                        src="${pageContext.request.contextPath}/PhotoComparisonController/initViewComparisonViewer?vehicleNumber=${claimsDto.vehicleNo}&comparisionTabNo=2&policyNumber=${claimsDto.policyDto.policyNumber}"></iframe>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                </c:if>
            </div>
            <input type="hidden" name="polRefNumber" id="PolRefNumber" value="${claimsDto.policyDto.policyRefNo}">
            <input type="hidden" name="RefNumber" id="RefNumber" value="${claimsDto.refNo}">
            <input type="hidden" name="claimStatus" id="claimStatus" value="${claimsDto.claimStatus}">
            <input type="hidden" name="claimNo" id="claimNo" value="${claimsDto.claimNo}">
            <input type="hidden" name="formType" id="formType" value="${FORM_TYPE}">
            <input type="hidden" name="coverNoteNo" id="coverNoteNoVal" value="${claimsDto.policyDto.coverNoteNo} ">
            <input type="hidden" name="policyNumberVal" id="policyNumberVal"
                   value="${claimsDto.policyDto.policyNumber}">
        </form>
    </div>
    <div id="dialog" style="display:none;" title="${CompanyTitle} Lanka PLC.">
        <p><span class="ui-icon ui-icon-info" style="float:left; margin:0 7px 0 0;"></span></p>
        <p id="dialog-email" class="textGrey"></p>
    </div>
</div>

<div class="viewDocumentContainer">
    <div class="card ">
        <div class="card-header p-2">
            <h5 class="mb-0 float-left">
                <i class="fa arrow-to-left "></i>Document View
            </h5>
            <a href="#" class="viewDocumentClose float-right p-1" style="width: auto;"><i class="fa fa-times"></i></a>
        </div>
        <div class="card-body p-1">
            <iframe src="" frameborder="0" id="viewDocument" width="100%" height="100vh"></iframe>
        </div>
    </div>
</div>

<script type="text/javascript" src="${pageContext.request.contextPath}/resources/rating/star-rating.js"></script>
<script type="text/javascript">
    var POLICY_REF_NO =  ${claimsDto.policyDto.policyRefNo};
    $(function () {
        var currentDate = '${Current_Date}';

        $("#accidDateTime").datetimepicker({
            sideBySide: true,
            format: 'YYYY-MM-DD HH:mm',
            //  maxDate:new Date(currentDate),
            icons: {
                time: "fa fa-clock-o",
                date: "fa fa-calendar",
                up: "fa fa-arrow-up",
                down: "fa fa-arrow-down"
            }

        });


        $("#dateTimeOfReport").datetimepicker({
            sideBySide: true,
            format: 'YYYY-MM-DD HH:mm',
            icons: {
                time: "fa fa-clock-o",
                date: "fa fa-calendar",
                up: "fa fa-arrow-up",
                down: "fa fa-arrow-down"
            }
        });

        $('#accidDateTime').on('dp.change', function (e) {
            $("#dateTimeOfReport").data("DateTimePicker").minDate(e.date);
            $("#dateTimeOfReport").data("DateTimePicker").maxDate(currentDate);
            validateThirdParty();
            var selDate = $("#accidDateTime").val();
            getAccidentDateResponse(selDate, "${claimsDto.policyDto.policyRefNo}");
        });

        try {
            $("#accidDateTime").data("DateTimePicker").maxDate(currentDate);
            $("#dateTimeOfReport").data("DateTimePicker").minDate('${claimsDto.accidDate}');
            $("#dateTimeOfReport").data("DateTimePicker").maxDate(currentDate);
        } catch (e) {
            console.error(e)
        }


    });

    function starrInit() {
        function getRatingMessage(value) {
            var ratingText = '';
            switch (value) {
                case 1:
                    ratingText = 'Worst';
                    break;
                case 2:
                    ratingText = 'Poor';
                    break;
                case 3:
                    ratingText = 'Fair';
                    break;
                case 4:
                    ratingText = 'Good';
                    break;
                case 5:
                    ratingText = 'Excellent';
                    break;
                default:

            }
            return ratingText;
        }

        var rate1 = $('#followCallAgnetServiceRateDiv').starrr({
            rating: ${claimsDto.followCallAgnetServiceRate},
            readOnly: '${FORM_TYPE}' == 'HISTORY',
            change: function (e, value) {
                $("#followCallAgnetServiceRate").val(value);
                $(this).siblings('.rating').html(getRatingMessage(value));

            }
        });
        var rate2 = $('#followCallAssessorServiceRateDiv').starrr({
            rating:  ${claimsDto.followCallAssessorServiceRate},
            readOnly: '${FORM_TYPE}' == 'HISTORY',
            change: function (e, value) {
                $("#followCallAssessorServiceRate").val(value);
                $(this).siblings('.rating').html(getRatingMessage(value));
            }
        });
        $(rate1).siblings('.rating').html(getRatingMessage(${claimsDto.followCallAgnetServiceRate}));
        $(rate2).siblings('.rating').html(getRatingMessage(${claimsDto.followCallAssessorServiceRate}));
    }

    $(window).load(function () {
        $("#isDoubtRemarkButton").prop('disabled', true);
        getAccidentDateResponse($("#accidDateTime").val(), ${claimsDto.policyDto.policyRefNo});
    });

    $(document).ready(function () {
        //call vehicle parts
        viewDamageBodyParts('${pageContext.request.contextPath}/CallCenter/viewDamagePart?TYPE=0&FORM_TYPE=${FORM_TYPE}', ${claimsDto.vehClsId});
        setURL(${claimsDto.vehClsId});
        viewSpecialRemarks();
        setVisibilityForEmpanelLomo('${claimsDto.vehClsId}');
        loadClaimStampContainer(${claimsDto.claimNo});

        $("#districtCode").on('change', function (evt, params) {
            var districtId = parseInt($(this).val());
            $.ajax({
                url: contextPath + "/CallCenter/getTableValueFromGivenCriteria?tableName=claim_district&valueField=V_DISTRICT_CODE&searchFiled=N_TXN_ID&searchValue=" + districtId
            }).success(function (data) {
                var districtCode = JSON.parse(data);
                $.ajax({
                    url: contextPath + "/AssessorAllocationController/citylist?divisonCode=" + districtCode
                }).success(function (data) {
                    var obj = JSON.parse(data);
                    $("#nearestCity").html("");
                    $("#nearestCity").append($('<option>').val("0").html("Please Select")).trigger("chosen:updated");
                    for (var i = 0; i < obj.length; i++) {
                        $("#nearestCity").append($('<option>').val(obj[i].gramaCode).html(obj[i].gramaName)).trigger("chosen:updated");
                    }
                    $('#frmForm').data('formValidation')
                        .updateStatus('nearestCity', 'NOT_VALIDATED')
                        .validateField('nearestCity');
                });
            });
        });

        if ('${TYPE}' != 1 && ('${claimsDto.claimStatus}' != 1 && '${claimsDto.claimStatus}' != 31)) {
            $('#tabs-1 .form-control,#tabs-1 .custom-control input,#tabs-2 .form-control,#tabs-2 .custom-control input').prop("disabled", true).trigger("chosen:updated");
            $('#basicInformationRemark').prop("disabled", false);
            $('[name="isDoubt"],.isDoubtRemark').prop("disabled", false);
        }

        if ('${FORM_TYPE}' == 'HISTORY') {
            $('.tab-pane .form-control,.tab-pane .custom-control input').prop("disabled", true).trigger("chosen:updated");
            $('#basicInformationRemark').prop("disabled", false);
            $('[name="isDoubt"],.isDoubtRemark').prop("disabled", false);
            starrInit()
        } else {
            starrInit()
        }

        if ('${claimsDto.isNoDamage}' == 'Y') {
            $('.formcontrolhide').hide();
        } else if ('${claimsDto.damageNotGiven}' == 'Y') {
            $('.formcontrolhide').hide();
        } else {
            $('.formcontrolhide').show();
        }

        function handleAccidentDescriptionValidation() {
            var causeOfLossValue = $('#causeOfLoss').val();
            var isOtherSelected = (causeOfLossValue === '9');
            var $formValidation = $('#frmForm').data('formValidation');

            if (isOtherSelected) {
                $('#accidDescMandatory').show();
                $formValidation.addField('accidDesc', {
                    validators: {
                        notEmpty: {
                            message: 'Accident Description is required when "Other" is selected.'
                        }
                    }
                });
            } else {
                $('#accidDescMandatory').hide();
                if ($formValidation.getFieldElements('accidDesc')) {
                    $formValidation.removeField('accidDesc');
                }
            }
        }

        $('#causeOfLoss').on('change', function () {
            handleAccidentDescriptionValidation();
        });

        handleAccidentDescriptionValidation();

    });
</script>
<script type="text/javascript">
    $("#tabs a").click(function (e) {
        e.preventDefault();
        $(this).tab('show');
    });
    $("#Claim-tabs a").click(function (e) {
        e.preventDefault();
        $(this).tab('show');
    });


    function viewDocument(refNo, jobRefNo, previousInspection, sideClass) {
        $('.viewDocumentContainer').addClass(sideClass).resizable({
            handles: "e, w",
            ghost: true,
            helper: "resizable-helper"
        });
        $('.viewDocumentContainer  iframe#viewDocument').attr("src", "${pageContext.request.contextPath}/ClaimHandlerController/documentViewer?refNo=" + refNo + "&jobRefNo=" + jobRefNo + "&PREVIOUS_INSPECTION=" + previousInspection);

        $(".viewDocumentClose").click(function () {
            $('.viewDocumentContainer').removeClass(sideClass + " ui-resizable").removeAttr("style");
        });
    }


    $('input.checkbox_check').click(function () {
        if ($('input.checkbox_check').is(':checked')) {
            $('#frmForm').data('formValidation').enableFieldValidators('catEventCode', true);
            $(".iscat").removeClass("text-mute").prop('disabled', false);

        } else {
            $('#frmForm').data('formValidation').enableFieldValidators('catEventCode', false);
            $(".iscat").addClass("text-mute").prop('disabled', true).val('0').trigger("chosen:updated");

        }
    });

    $('#isHugeDamage').click(function () {
        if ($('#isHugeDamage').is(':checked')) {
            $('#frmForm').data('formValidation').enableFieldValidators('hugeRemark', true);
            $(".hugeRemark").removeClass("text-mute").prop('disabled', false);

        } else {
            $('#frmForm').data('formValidation').enableFieldValidators('hugeRemark', false);
            $(".hugeRemark").addClass("text-mute").prop('disabled', true).val('');

        }
    });
    // $('input[type=radio][name=isDoubt]').change(function () {
    //     if (this.value == 'Y') {
    //         $('#frmForm').data('formValidation').enableFieldValidators('isDoubtRemark', true);
    //         $(".isDoubtRemark , #isDoubtRemarkButton").removeClass("text-mute").prop('disabled', false);
    //     }
    //     else if (this.value == 'N') {
    //         $('#frmForm').data('formValidation').enableFieldValidators('isDoubtRemark', false);
    //         $(".isDoubtRemark, #isDoubtRemarkButton").addClass("text-mute").prop('disabled', true).val('');
    //     }
    // });

    function Intimation_Type_disable_enable(stat, reasonId) {

        if (stat == "Y") {
            $('#frmForm').data('formValidation').enableFieldValidators('lateIntimateReason', true);
            if (reasonId > 0) {
                $(".IntimationType").removeClass("text-mute").prop('disabled', true).trigger("chosen:updated");
            } else {
                $(".IntimationType").removeClass("text-mute").prop('disabled', false).trigger("chosen:updated");
            }

        } else {
            $('#frmForm').data('formValidation').enableFieldValidators('lateIntimateReason', false);
            $(".IntimationType").val('').prop('disabled', true).trigger("chosen:updated").prop('disabled', false);
            // $(".IntimationType").addClass("text-mute").prop('disabled', true).trigger("chosen:updated").val('');
        }
    }

    function Statement_disable_enable(stat) {
        if (stat == "Y") {
            $('#frmForm').data('formValidation').enableFieldValidators('firstStatementReqReason', true);
            $("#firstStatementReqReason").removeClass("text-mute").prop('disabled', false).trigger("chosen:updated");
            // $("#firstStatementReqReason").removeClass("text-mute").prop('disabled', false).trigger("chosen:updated");
        } else {
            $('#frmForm').data('formValidation').enableFieldValidators('firstStatementReqReason', false);
            $("#firstStatementReqReason").val('').prop('disabled', true).trigger("chosen:updated").prop('disabled', false);
            // $("#firstStatementReqReason").addClass("text-mute").prop('disabled', true).trigger("chosen:updated");
        }
    }

    function lomoPromoted(stat) {
        if (stat == "Y") {
            $("#lomoReason").removeClass("text-mute").prop('disabled', false).trigger("chosen:updated");
        } else {
            $("#lomoReason").addClass("text-mute").prop('disabled', true).trigger("chosen:updated");
        }
    }

    function ncbPromoted(stat) {
        if (stat == "Y") {
            $("#ncbReason").removeClass("text-mute").prop('disabled', false).trigger("chosen:updated");
        } else {
            $("#ncbReason").addClass("text-mute").prop('disabled', true).trigger("chosen:updated");
        }
    }

    function Inspection_Type_disable_enable(stat) {
        if (stat == "Y") {
            $(".Inspectiondisable").removeClass("text-mute").prop('disabled', false).trigger("chosen:updated");
        } else {
            $(".Inspectiondisable").addClass("text-mute").prop('disabled', true).trigger("chosen:updated");
        }
    }

    function clearDriverFields() {
        try {
            $("#driverName").val('${claimsDto.driverName}');
            $("#driverTitle").val('${claimsDto.driverTitle}');
            $("#driverReleshipInsurd").val('${claimsDto.driverReleshipInsurd}');
            $("#driverNic").val('${claimsDto.driverNic}');
        } catch (e) {

        }
    }

    function displayVals() {
        var name = $("#reporterName").val();
        var nameTitle = $("#reporterTitle").val();
        var relationship = $("#releshipInsurd").val();
        var nic = $("#reporterId").val();
        try {
            if (($("#radio_button").is(':checked') && $("#radio_button").val() == 'Y')) {
                $("#driverName").val(name).prop('readonly', true);
                $("#driverTitle").val(nameTitle).attr('readonly', true);
                $("#driverReleshipInsurd").val(relationship).prop('disabled', true).trigger("chosen:updated").prop('disabled', false);
                $("#driverNic").val(nic).prop('readonly', true);
                $('#frmForm').data('formValidation').enableFieldValidators('driverName', false);
                $('#frmForm').data('formValidation').enableFieldValidators('driverTitle', false);
                $('#frmForm').data('formValidation').enableFieldValidators('driverReleshipInsurd', false);
                $('#frmForm').data('formValidation').enableFieldValidators('driverNic', false);
            } else {
                $("#driverName").attr('readonly', false);
                $("#driverTitle").attr('readonly', false);
                $("#driverReleshipInsurd").prop('disabled', false).trigger("chosen:updated");
                $("#driverNic").attr('readonly', false);
                $('#frmForm').data('formValidation').enableFieldValidators('driverName', true);
                $('#frmForm').data('formValidation').enableFieldValidators('driverTitle', true);
                $('#frmForm').data('formValidation').enableFieldValidators('driverReleshipInsurd', true);
                $('#frmForm').data('formValidation').enableFieldValidators('driverNic', true);
            }
        } catch (e) {

        }

    }

    $("#radio_button_no").change(clearDriverFields).keydown(clearDriverFields);
    $("#reporterName,#reporterTitle,#releshipInsurd,#reporterId,[name='isSameReportDriver']").change(displayVals).keydown(displayVals);
    displayVals();

    $("#releshipInsurd").change(setInsuredDetails).keydown(setInsuredDetails);

    function setInsuredDetails() {
        var val = $("#releshipInsurd").val();
        if (val == 8) { //if select item is Insured
            $("#reporterName").val('${claimsDto.policyDto.custName}');
            $("#reporterId").val('${claimsDto.policyDto.custNic}');

            $('#frmForm').formValidation('revalidateField', 'reporterName');
            $('#frmForm').formValidation('revalidateField', 'reporterId');
        }
    }

    function viewDamageBodyParts(url, value) {
        setVisibilityForEmpanelLomo(value);
        $("#DamagePartsView").load(url + "&vehClsId=" + value);
    }

    function viewSpecialRemarks() {
        $("#specialRemaksDiv").load(contextPath + "/CallCenter/viewSpecialRemarks?P_N_CLIM_NO=${claimsDto.claimNo}&FORM_TYPE=${FORM_TYPE}");
    }

    function viewClaimFlow() {
        $("#claimWorkflowDiv").load(contextPath + "/CallCenter/viewClaimWorkflow?P_N_CLIM_NO=${claimsDto.claimNo}");
    }

    $(function () {
        if (${claimsDto.intimationType == 2}) {
            Intimation_Type_disable_enable('Y', '${claimsDto.lateIntimateReason}');
        } else {
            Intimation_Type_disable_enable('N', '${claimsDto.lateIntimateReason}');
        }

        $(".iscat").addClass("text-mute").prop('disabled', true).trigger("chosen:updated");
        $("#firstStatementReqReason").addClass("text-mute").prop('disabled', true).trigger("chosen:updated");

        var randomColor = Math.floor(Math.random() * 16777215).toString(16);

        $(".font-bg").addClass(randomColor).css({
            backgroundColor: '#' + randomColor

        });

        if (${claimsDto.policyDto.polStatus != 'LAP'}) {
            $('#status').addClass()
        }

    });

    //alerts
    var title = "<b>Alert !</b><br>";

    function AlertErrorTirdprty(msg, type) {
        $.notify({title: title, message: msg}, {type: type, delay: 10000});
    }

    function notify(msg, type) {
        $.notify({title: title, message: msg}, {type: type, delay: 10000});
    }

    function policyDetailsView() {
        $("#viewPolicyModal").colorbox({

            width: "100%",
            height: "100%",
            iframe: true,
            href: contextPath + "/CallCenter/viewReportedClaim?TYPE=4" + "",
            onCleanup: function () {
            }
        });
    }


    $(document).ready(function () {
        if ('${claimsDto.policyDto.polStatus}' == 'PRP') {
            $('#status').addClass('badge-primary')
        } else if ('${claimsDto.policyDto.polStatus}' == 'CAN') {
            $('#status').addClass('badge-danger')
        } else if ('${claimsDto.policyDto.polStatus}' == 'INF') {
            $('#status').addClass('badge-success')
        } else if ('${claimsDto.policyDto.polStatus}' == 'EXP') {
            $('#status').addClass('badge-primary')
        } else if ('${claimsDto.policyDto.polStatus}' == 'LAP') {
            $('#status').addClass('badge-info')
        } else if ('${claimsDto.policyDto.polStatus}' == 'SUS') {
            $('#status').addClass('badge-dark')
        }
        $('#theftAndFound').prop('checked', ${claimsDto.isTheftAndFound == 'Y'});
        $('#theftAndFound').prop('disabled', ${claimsDto.isTheftAndFound == 'Y'});
    });


    function saveFollowUpCall() {
        var $form = $('#frmForm');
        var title = $('#followCallContactPersonTitle').val();
        var name = $('#followCallContactPersonName').val();
        var number = $('#followCallContactNumber').val();
        var isError = false;

        if (title == 0 || name == "") {
            $("#nameDiv").show();
            isError = true;
        }

        if (number == "") {
            $("#contactDiv").show();
            isError = true;
        }

        if (isError) {
            return;
        }

        $.ajax({
            url: contextPath + "/Claim/followUpCallUpdate",
            type: 'POST',
            data: $form.serialize(),
            success: function (result) {
                if (result.errorCode != 200) {
                    $.notify({title: '<b>Alert !</b><br>', message: result.message}, {type: 'danger'});
                } else {
                    $("#cmdFollowUpCall").prop('disabled', true);
                    $("#followCallContactPersonName").prop('readonly', true);
                    $("#followCallContactNumber").prop('readonly', true);
                    $("#followCallContactPersonTitle").prop('disabled', true);

                    $("#followCallUserId").val("${G_USER.userId}")
                    $.notify({title: '<b>Alert !</b><br>', message: result.message}, {type: 'success'});
                }

            }

        });
    }


    function viewClaimHistory(polRefNo, claimNo) {

        $("#" + claimNo).colorbox({

            width: "100%",
            height: "100%",
            iframe: true,
            href: contextPath + "/CallCenter/viewClaimHistory?P_N_CLIM_NO=" + claimNo,
            onCleanup: function () {
            }
        });
//        document.getElementById('frmForm').action = contextPath + "/CallCenter/viewClaimHistory";
//        document.getElementById('frmForm').submit();
    }

    function removeTitleValidation() {
        var title = $('#followCallContactPersonTitle').val();
        var name = $('#followCallContactPersonName').val();
        if (title > 0 && name != "") {
            $('#nameDiv').hide();
        } else {
            $('#nameDiv').show();
        }
    }

    $("#followCallContactPersonName").keyup(function () {
        var name = $('#followCallContactPersonName').val();
        var title = $('#followCallContactPersonTitle').val();
        if (title > 0 && name != "") {
            $('#nameDiv').hide();
        } else {
            $('#nameDiv').show();
        }

    });

    $("#followCallContactNumber").keyup(function () {
        var contact = $('#followCallContactNumber').val();
        $(this).val($(this).val().replace(/[^0-9\.]/g, ''));
        if (contact != "") {
            $('#contactDiv').hide();
        } else {
            $('#contactDiv').show();
        }

    });
    <c:if test="${claimsDto.isFollowupCallDone=='Y'}">
    $("#followCallContactPersonName").prop('readonly', true);
    $("#followCallContactNumber").prop('readonly', true);
    $("#followCallContactPersonTitle").prop('disabled', true);
    </c:if>

    function selectDamage() {

        $('#isNoDamage').val("N");
        $('#isHugeDamage').val("N");
        $('#damageNotGiven').val("N");

        if ((document.getElementById("chkIsNoDamage").checked)) {
            $('#chkIsNoDamage').prop('checked', true);
            $('#chkIsHugeDamage').prop('checked', false);
            $('#chkDamageNotGiven').prop('checked', false);
            $('#isNoDamage').val("Y");
            $('.formcontrolhide').hide();
            $('#frmForm').data('formValidation').enableFieldValidators('vehClsId', false);
            $('#frmForm').data('formValidation').enableFieldValidators('DamagePartsView', false);
        } else {
            $('.formcontrolhide').show();
            $('#chkIsHugeDamage').prop('checked', false);
            $('#chkDamageNotGiven').prop('checked', false);

        }
    }

    function serveDamage() {


        $('#isNoDamage').val("N");
        $('#isHugeDamage').val("N");
        $('#damageNotGiven').val("N");
        if ((document.getElementById("chkIsHugeDamage").checked)) {
            $('#chkIsHugeDamage').prop('checked', true);
            $('#chkIsNoDamage').prop('checked', false);
            $('#chkDamageNotGiven').prop('checked', false);
            $('#isHugeDamage').val("Y");
            $('.formcontrolhide').show();
            $('#frmForm').data('formValidation').enableFieldValidators('vehClsId', true);
            $('#frmForm').data('formValidation').enableFieldValidators('DamagePartsView', true);
        } else {
            $('.formcontrolhide').show();
            //   $('#chkIsHugeDamage').prop('checked', false);
            //  $('#chkDamageNotGiven').prop('checked', false);
            //  $('#chkIsNoDamage').prop('checked', false);
        }
    }

    function selectDamageNotGiven() {

        $('#isNoDamage').val("N");
        $('#isHugeDamage').val("N");
        $('#damageNotGiven').val("N");
        if ((document.getElementById("chkDamageNotGiven").checked)) {
            $('#chkIsHugeDamage').prop('checked', false);
            $('#chkIsNoDamage').prop('checked', false);
            $('#chkDamageNotGiven').prop('checked', true);
            $('#damageNotGiven').val("Y");
            $('.formcontrolhide').hide();
            $('#frmForm').data('formValidation').enableFieldValidators('vehClsId', false);
            $('#frmForm').data('formValidation').enableFieldValidators('DamagePartsView', false);
        } else {
            $('#chkIsHugeDamage').prop('checked', false);
            //  $('#chkDamageNotGiven').prop('checked', false);
            $('#chkIsNoDamage').prop('checked', false);
            $('.formcontrolhide').show();
        }
    }

    function validateThirdParty() {
        var accidentDate = $('#accidDateTime').val();
        $.ajax({
            url: contextPath + "/Claim/thirdPartyVehicle?accidentDate=" + accidentDate

        }).success(function (data) {
            var obj = JSON.parse(data);
            if (obj == true) {
                notify('Third party intimation already exist for this vehicle', "danger");
            }
        });

    }


</script>
</body>
<script>
    hideLoader();
    <c:if test="${isLock}">
    notify('Currently in used by ${claimLockDto.invokeUserId}', "danger");
    </c:if>
</script>
<script type="text/javascript">

    function updateDeboutClaimButton() {
        if ($("#isDoubtRemark").val() == '') {
            $(".errormessageremark").removeClass("d-none");
            // $("#isDoubtRemarkButton").prop('disabled', true);
        } else {
            $(".errormessageremark").addClass("d-none");
            // $("#isDoubtRemarkButton").prop('disabled', false);
            updateDeboutClaim(1, 'Doubt Claim');
        }
    }

    $("#isDoubtRemark").keyup(function () {
        if ($("#isDoubtRemark").val() == '') {
            $(".errormessageremark").removeClass("d-none");
            // $("#isDoubtRemarkButton").prop('disabled', true);
        } else {
            $(".errormessageremark").addClass("d-none");
            // $("#isDoubtRemarkButton").prop('disabled', false);
        }
    });

    function updateDeboutClaim(departmentId, sectionName) {
        var dataobj = {
            jobRefNo: $('#RefNumber').val(),
            status: $('#stat').val(),
            remark: $('#isDoubtRemark').val(),
            departmentId: departmentId,
            sectionName: sectionName,
            claimNo: $('#claimNo').val()
        }
        $.ajax({
            url: contextPath + "/Claim/updateDoubtClaim",
            type: 'POST',
            data: dataobj,
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj != null) {
                    if (obj == "SUCCESS") {
                        notify("Saved Successfully", "success");
                    } else {
                        notify("Can not be saved", "danger");
                    }

                }
            }
        });
    }

    function changeStat(val) {
        $('#stat').val(val);
        if (${null != claimsDto.claimNo && claimsDto.claimNo!=0}) {
            $("#isDoubtRemarkButton").prop('disabled', false);
            $('#errormessage').show();
        }
    }

    function loadClaimStampContainer(claimNo) {
        $("#claimStampContainer").load(contextPath + "/ClaimHandlerController/loadClaimStampPage?claimNo=" + claimNo);
    }

    function markTheftAndFound(isChecked) {
        var isTheftAndFound = ${claimsDto.isTheftAndFound=='Y'};
        if (isTheftAndFound) {
            notify("Already Marked as Theft and Found", "danger");
            return;
        }
        if (isChecked) {
            bootbox.dialog({
                title: 'Do you want to mark as Vehicle Found?',
                message: "<textarea class='form-control' placeholder='Please Enter Remark' id='txtTheftFoundRemark'></textarea>",
                size: 'medium',
                buttons: {
                    cancel: {
                        label: "No",
                        className: 'btn-secondary',
                        callback: function () {
                            $('#theftAndFound').prop('checked', false);
                        }
                    },
                    ok: {
                        label: "Yes",
                        className: 'btn-primary',
                        callback: function () {
                            var claimNo = ${claimsDto.claimNo};
                            var remark = $('#txtTheftFoundRemark').val();
                            if (remark == '') {
                                $('#txtTheftFoundRemark').addClass('bg-badge-danger text-white');
                                $('#txtTheftFoundRemark').focus();
                                notify('Please Enter Remark', 'danger');
                                return false;
                            } else {
                                $.ajax({
                                    url: contextPath + "/CallCenter/theftAndFound",
                                    type: 'POST',
                                    data: {
                                        N_CLAIM_NO: claimNo,
                                        V_REMARK: remark
                                    },
                                    success: function (result) {
                                        var response = JSON.parse(result);
                                        if (response == 'SUCCESS') {
                                            notify('Successfully Updated', 'success');
                                            $('#divTheftAndFound').hide();
                                            document.getElementById('assessorAllocFrame').src = document.getElementById('assessorAllocFrame').src;
                                            loadLogDetails();
                                            loadSpecialRemarks();
                                        } else if (response == 'FAIL') {
                                            notify('Failed to Update', 'danger');
                                            $('#theftAndFound').prop('checked', false);
                                        } else {
                                            notify('System Error', 'danger');
                                            $('#theftAndFound').prop('checked', false);
                                        }
                                    }
                                })
                            }
                        }
                    }
                }
            })
        } else {
            notify('Cannot Re-Convert as Theft Claim', 'danger');
            return;
        }
    }
</script>
</html>
