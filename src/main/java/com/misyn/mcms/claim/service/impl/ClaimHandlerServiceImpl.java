package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.*;
import com.misyn.mcms.claim.dao.impl.*;
import com.misyn.mcms.claim.dao.impl.motorengineer.DesktopInspectionDetailsMeDaoImpl;
import com.misyn.mcms.claim.dao.impl.motorengineer.MotorEngineerDetailsDaoImpl;
import com.misyn.mcms.claim.dao.impl.motorengineer.OnSiteInspectionDetailsMeDaoImpl;
import com.misyn.mcms.claim.dao.motorengineer.DesktopInspectionDetailsMeDao;
import com.misyn.mcms.claim.dao.motorengineer.MotorEngineerDetailsDao;
import com.misyn.mcms.claim.dao.motorengineer.OnSiteInspectionDetailsMeDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.ARIReasonEnum;
import com.misyn.mcms.claim.enums.ConditionType;
import com.misyn.mcms.claim.enums.InvestigationStatusEnum;
import com.misyn.mcms.claim.enums.NotificationPriority;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.exception.UserNotFoundException;
import com.misyn.mcms.claim.service.*;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
public class ClaimHandlerServiceImpl extends AbstractBaseService<ClaimHandlerServiceImpl> implements ClaimHandlerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimHandlerServiceImpl.class);
    DbRecordCommonFunction dbRecordCommonFunction = new DbRecordCommonFunction();
    private ClaimHandlerDao claimHandlerDao = new ClaimHandlerDaoImpl();
    private CallCenterService callCenterService = new CallCenterServiceImpl();
    private StorageService storageService = new StorageServiceImpl();
    private ClaimDocumentDao claimDocumentDao = new ClaimDocumentDaoImpl();
    private ClaimPanelAssignUserDao claimPanelAssignUserDao = new ClaimPanelAssignUserDaoImpl();
    private ClaimUserAllocationService claimUserAllocationService = new ClaimUserAllocationServiceImpl();
    private ReminderPrintSummaryDao reminderPrintSummaryDao = new ReminderPrintSummaryDaoImpl();
    private RequestAriService requestAriService = new RequestAriServiceImpl();
    private RequestAriDao requestAriDao = new RequestAriDaoImpl();
    private ClaimDocumentTypeDao claimDocumentTypeDao = new ClaimDocumentTypeDaoImpl();
    private ClaimCalculationSheetMainDao claimCalculationSheetMainDao = new ClaimCalculationSheetMainDaoImpl();
    private SupplyOrderSummaryDao supplyOrderSummaryDao = new SupplyOrderSummaryDaoImpl();
    private ClaimInvestigationSelectImageDao claimInvestigationSelectImageDao = new ClaimInvestigationSelectImageDaoImpl();
    private MotorEngineerDetailsDao motorEngineerDetailsDao = new MotorEngineerDetailsDaoImpl();
    private RtePendingClaimDetailDao rtePendingClaimDetailDao = new RtePendingClaimDetailDaoImpl();
    private SupplyOrderRequestDao supplyOrderRequestDao = new SupplyOrderRequestDaoImpl();
    private OnSiteInspectionDetailsMeDao onSiteInspectionDetailsMeDao = new OnSiteInspectionDetailsMeDaoImpl();
    private DesktopInspectionDetailsMeDao desktopInspectionDetailsMeDao = new DesktopInspectionDetailsMeDaoImpl();
    private ClaimClaimPanelUserDao claimClaimPanelUserDao = new ClaimClaimPanelUserDaoImpl();
    private MainPanelAssignHstDao mainPanelAssignHstDao = new MainPanelAssignHstDaoImpl();
    private UserDao userDao = new UserDaoImpl();
    private final BulkCloseDetailDao bulkCloseDetailDao = new BulkCloseDetailDaoImpl();

    @Override
    public ClaimHandlerDto insert(ClaimHandlerDto claimHandlerDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto update(ClaimHandlerDto claimHandlerDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto delete(ClaimHandlerDto claimHandlerDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto updateAuthPending(ClaimHandlerDto claimHandlerDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto deleteAuthPending(ClaimHandlerDto claimHandlerDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto auth(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto reject(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto reject(Object id, UserDto user, String rejectMessage) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto search(Object id) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto searchAuthPending(Object id) throws Exception {
        return null;
    }

    @Override
    public List<ClaimHandlerDto> searchAll() throws Exception {
        return null;
    }

    @Override
    public List<ClaimHandlerDto> searchAllAuthPending() throws Exception {
        return null;
    }

    @Override
    public String getMessage(int messageId) {
        return null;
    }

    @Override
    public DataGridDto getClaimHandlerDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, Integer priority) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = claimHandlerDao.getClaimHandlerDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, priority);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public DataGridDto getLetterPanelList(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = claimHandlerDao.getLetterPanelList(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public DataGridDto getClaimHandlerDataGridDtoByCalsheetStatus(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String calsheetStatus) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = claimHandlerDao.getClaimHandlerDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, calsheetStatus);
            List<ClaimHandlerGridDto> dtos = (ArrayList) dataGridDto.getData();
            List<ClaimHandlerGridDto> list = new ArrayList<>();
            List<Object> newLimitList = new ArrayList<>();
            int recordCount = 0;

            int index = start;
            for (ClaimHandlerGridDto gridDto : dtos) {
                String status = this.getCalsheetStatus(gridDto.getClaimNo());
                if (AppConstant.CALSHEET_STATUS_ADVANCE_PAID.equalsIgnoreCase(calsheetStatus)) {
                    if (status.equalsIgnoreCase(AppConstant.ADVANCE_PAID)) {
                        gridDto.setCalSheetStatus(status);
                        gridDto.setIndex(++index);
                        list.add(gridDto);
                        recordCount++;
                    }
                } else if (AppConstant.CALSHEET_STATUS_REJECTED.equalsIgnoreCase(calsheetStatus)) {
                    if (status.equalsIgnoreCase(AppConstant.CALSHEET_STATUS_REJECTED)) {
                        gridDto.setCalSheetStatus(status);
                        gridDto.setIndex(++index);
                        list.add(gridDto);
                        recordCount++;
                    }
                } else {
                    gridDto.setCalSheetStatus(status);
                    newLimitList.add(gridDto);
                    recordCount = dataGridDto.getRecordsTotal();
                }
            }
            if (AppConstant.CALSHEET_STATUS_ADVANCE_PAID.equalsIgnoreCase(calsheetStatus) || AppConstant.CALSHEET_STATUS_REJECTED.equalsIgnoreCase(calsheetStatus)) {
                for (int i = start; i < (length + start); i++) {
                    if (i < list.size() && i < length) {
                        newLimitList.add(list.get(i));
                    }
                }
            }
            dataGridDto.setData(newLimitList);
            dataGridDto.setRecordsTotal(recordCount);
            dataGridDto.setRecordsFiltered(recordCount);
            dataGridDto.setDraw(drawRandomId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public ClaimHandlerDto searchByClaimNo(Integer id) throws Exception {
        Connection connection = null;
        ClaimHandlerDto claimHandlerDto = null;
        try {
            connection = getJDBCConnection();
            claimHandlerDto = claimHandlerDao.searchMaster(connection, id);
            if (null != claimHandlerDto) {
                ClaimsDto viewAccidentClaimsDto = callCenterService.getViewAccidentClaimsDto(connection, claimHandlerDto.getClaimNo());
                claimHandlerDto.setClaimsDto(viewAccidentClaimsDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimHandlerDto;
    }

    @Override
    public ClaimHandlerDto searchClaimByClaimNo(Integer claimNo) throws Exception {
        Connection connection = null;
        ClaimHandlerDto claimHandlerDto = null;
        try {
            connection = getJDBCConnection();
            claimHandlerDto = this.searchClaimByClaimNo(connection, claimNo);
            String Name = getFullNameDetails(connection, claimHandlerDto.getRepudiatedLetterPrintUserId());
            claimHandlerDto.setRepudiatedLetterPrintUserName(Name);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimHandlerDto;
    }


    private String getFullNameDetails(Connection connection, String userId) {
        PreparedStatement ps;
        ResultSet rs;
        String fullName = AppConstant.STRING_EMPTY;
        try {
            ps = connection.prepareStatement("SELECT CONCAT(v_title,' ',v_firstname,' ',v_lastname) AS full_name FROM usr_mst WHERE v_usrid=?");
            ps.setString(1, userId);
            rs = ps.executeQuery();
            if (rs.next()) {
                fullName = rs.getString("full_name");
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return fullName;
    }

    @Override
    public ClaimHandlerDto searchClaimByClaimNo(Connection connection, Integer claimNo) throws Exception {
        ClaimHandlerDto claimHandlerDto = null;
        try {
            claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);
            if (null != claimHandlerDto) {
                claimHandlerDto.setClaimHandlerMobileNo(claimHandlerDao.getMobileNoByAssignId(connection, claimHandlerDto.getAssignUserId()));
                ClaimsDto viewAccidentClaimsDto = callCenterService.getClaimsDtoByClaimNo(connection, claimHandlerDto.getClaimNo());
                claimHandlerDto.setClaimsDto(viewAccidentClaimsDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimHandlerDto;
    }

    @Override
    public List<LeasingCompanyDto> getLeasingCompanyDetails() throws Exception {
        Connection connection = null;
        List<LeasingCompanyDto> list = null;
        try {
            connection = getJDBCConnection();
            list = claimHandlerDao.getLeasingCompanyDetails(connection);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public boolean updateFinancialInfo(ClaimHandlerDto claimHandlerDto, UserDto user) throws Exception {
        Connection connection = null;
        boolean isUpdated = false;
        try {
            connection = getJDBCConnection();
            isUpdated = claimHandlerDao.updateFinancialInterest(connection, claimHandlerDto);
            if (isUpdated) {
                saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Financial Info", "Update Financial Info");
                saveClaimProcessFlow(connection, claimHandlerDto.getClaimNo(), 0, "Update Financial Info", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return isUpdated;
    }

    @Override
    public boolean updateLiabilityCheck(ClaimHandlerDto claimHandlerDto, UserDto user) throws Exception {
        Connection connection = null;
        boolean isUpdated = false;
        try {
            connection = getJDBCConnection();
            isUpdated = claimHandlerDao.updateLiabilityCheckList(connection, claimHandlerDto);
            if (isUpdated) {
                saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Liability Check", "Update Liability Check");
                saveClaimProcessFlow(connection, claimHandlerDto.getClaimNo(), 0, "Liability has been checked", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return isUpdated;
    }

    @Override
    public boolean saveRemark(SpecialRemarkDto specialRemarkDto, UserDto user, String type) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(specialRemarkDto.getClaimNo())).concat("&P_TAB_INDEX=0");
        StringBuilder sbMessage = new StringBuilder();
        boolean isNotSpecialComment = true;

        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            specialRemarkDto.setInputDateTime(Utility.sysDateTime());
            specialRemarkDto.setInputUserId(user.getUserId());
            specialRemarkDto.setDepartmentId(AppConstant.CLAIM_HANDLER_DEPARTMENT);
            String header = AppConstant.STRING_EMPTY;
            if (AppConstant.IR.equals(type)) {
                header = AppConstant.INTIMATION_REMARK;
                specialRemarkDto.setSectionName(AppConstant.INTIMATION_REMARK);

            } else if (AppConstant.LR.equals(type)) {
                header = AppConstant.LA_REMARK;
                specialRemarkDto.setSectionName(AppConstant.LA_REMARK);

            } else if (AppConstant.OR.equals(type)) {
                header = AppConstant.OTHER_REMARK;
                specialRemarkDto.setSectionName(AppConstant.OTHER_REMARK);
            } else if (AppConstant.SC.equals(type)) {
                ClaimHandlerDto searchedClaimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, specialRemarkDto.getClaimNo());
                if (null != searchedClaimHandlerDto && AppConstant.ZERO_INT != searchedClaimHandlerDto.getOldClaimStatus()) {
                    header = AppConstant.SPECIAL_APPROVAL_COMMENT;
                    specialRemarkDto.setSectionName(AppConstant.SPECIAL_COMMENT);
                    sbMessage.append("You have received a Special Approval Comment");
                    saveClaimProcessFlow(connection, specialRemarkDto.getClaimNo(), 0, "received a Special Approval Comment", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
                    saveNotification(connection, specialRemarkDto.getClaimNo(), user.getUserId(), searchedClaimHandlerDto.getSpecialApprovalInputUserId(), sbMessage.toString(), URL, NotificationPriority.HIGH);
                    if (isClaimHandlerDepartment(user)) {
                        shiftNotificationOnAction(connection, specialRemarkDto.getClaimNo(), user);
                    }
                    claimHandlerDao.updateSwapClaimStatus(connection, specialRemarkDto.getClaimNo());
                } else {
                    isNotSpecialComment = false;
                }
            } else if (AppConstant.BR.equals(type)) {
                //  ClaimHandlerDto searchedClaimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, specialRemarkDto.getClaimNo());
                header = AppConstant.BRANCH_REMARK;
                specialRemarkDto.setSectionName(AppConstant.BRANCH_REMARK);
                specialRemarkDto.setDepartmentId(AppConstant.BRANCH_DEPARTMENT_ID);
            } else if (AppConstant.SR.equals(type)) {
                header = AppConstant.SPECIAL_REMARK;
                specialRemarkDto.setSectionName(AppConstant.SPECIAL_REMARK);
            }
            specialRemarkDto = specialRemarkDao.insertMaster(connection, specialRemarkDto);
            if (null != specialRemarkDto && isNotSpecialComment) {
                saveClaimsLogs(connection, specialRemarkDto.getClaimNo(), user, header, "Insert remark to ".concat(header));
                commitTransaction(connection);
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
        } finally {
            releaseJDBCConnection(connection);
        }
        return false;
    }

    @Override
    public List<ClaimLogsDto> getLogList(Integer claimNo) throws Exception {
        Connection connection = null;
        List<ClaimLogsDto> list = null;
        try {
            connection = getJDBCConnection();
            list = claimLogsDao.searchByKeyValueAndType(connection, AppConstant.CLAIM_HANDLER_DEPARTMENT, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public List<SpecialRemarkDto> searchRemarksByClaimNo(Integer claimNo) throws Exception {
        List<SpecialRemarkDto> list = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = specialRemarkDao.searchRemarksByClaimNo(connection, claimNo, AppConstant.CLAIM_HANDLER_DEPARTMENT);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;

    }

    @Override
    public List<SpecialRemarkDto> searchRemarksByClaimNoAndDepartmentId(Integer claimNo, Integer departmentId) throws Exception {
        List<SpecialRemarkDto> list = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = specialRemarkDao.searchRemarksByClaimNo(connection, claimNo, departmentId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public List<SpecialRemarkDto> searchAllRemarksByClaimNo(Integer claimNo) throws Exception {
        List<SpecialRemarkDto> list = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = specialRemarkDao.searchRemarksByClaimNo(connection, claimNo);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public List<SpecialRemarkDto> searchDepartmentRemarksByClaimNo(Integer claimNo) throws Exception {
        List<SpecialRemarkDto> list = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = specialRemarkDao.searchDepartmentRemarksByClaimNo(connection, claimNo);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public ClaimDocumentDto checkedDocument(ClaimDocumentDto claimDocumentDto, UserDto user) throws MisynJDBCException {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            if (claimDocumentDao.updateDocumentCheckStatus(connection, claimDocumentDto) != null) {
                ClaimDocumentTypeDto claimDocumentTypeDto = claimDocumentTypeDao.searchByDocumentTypeId(connection, claimDocumentDto.getDocumentTypeId());
                storageService.updatedDocumentCheckAndMandatoryDocumentStatus(connection, claimDocumentDto.getClaimNo());
                saveClaimsLogs(connection, claimDocumentDto.getClaimNo(), user, "Document Check", claimDocumentTypeDto.getDocumentTypeName().concat(" has been checked"));
               /* if (claimDocumentTypeDto.getDocumentTypeId() == AppConstant.GARAGE_BILL_DOCUMENT_TYPE_ID) {
                    StringBuilder sbMessage = new StringBuilder();
                    sbMessage.append("You have received checked Garage bill.Please generate payment voucher");
                    String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimDocumentDto.getClaimNo())).concat("&P_TAB_INDEX=15");
                    String userId = claimCalculationSheetMainDao.getSpecialTeamAssignUserId(connection, claimDocumentDto.getClaimNo(), 65, 5);
                    if (null != userId && !userId.isEmpty()) {
                        saveNotfication(connection, claimDocumentDto.getClaimNo(), user.getUserId(), userId, sbMessage.toString(), URL);
                    }
                }*/
                return claimDocumentDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return null;
    }

    @Override
    public ClaimDocumentDto holdDocument(ClaimDocumentDto claimDocumentDto, UserDto user) throws MisynJDBCException {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            if (claimDocumentDao.updateDocumentHoldStatus(connection, claimDocumentDto) != null) {
                ClaimDocumentTypeDto claimDocumentTypeDto = claimDocumentTypeDao.searchByDocumentTypeId(connection, claimDocumentDto.getDocumentTypeId());
                storageService.updatedDocumentCheckAndMandatoryDocumentStatus(connection, claimDocumentDto.getClaimNo());
                saveClaimsLogs(connection, claimDocumentDto.getClaimNo(), user, "Hold Document", claimDocumentTypeDto.getDocumentTypeName().concat(" has been held"));
                saveClaimProcessFlow(connection, claimDocumentDto.getClaimNo(), 0, claimDocumentTypeDto.getDocumentTypeName().concat(" has been hold"), user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
                return claimDocumentDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return null;
    }

    @Override
    public ClaimDocumentDto rejectDocument(ClaimDocumentDto claimDocumentDto, UserDto user) throws MisynJDBCException {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            if (claimDocumentDao.updateDocumentRejectStatus(connection, claimDocumentDto) != null) {
                ClaimDocumentTypeDto claimDocumentTypeDto = claimDocumentTypeDao.searchByDocumentTypeId(connection, claimDocumentDto.getDocumentTypeId());
                storageService.updatedDocumentCheckAndMandatoryDocumentStatus(connection, claimDocumentDto.getClaimNo());
                // if 30 or 65 update call

                if (AppConstant.NO_OBJECTION_LETTER_DOCUMENT_TYPE_ID == claimDocumentDto.getDocumentTypeId()) {
                    claimCalculationSheetMainDao.updateNoObjectionUploadReject(connection, claimDocumentDto.getClaimNo(), claimDocumentDto.getRefNo());
                }
                if (AppConstant.PREMIUM_OUTSTANDING_CONFIRMATION_DOCUMENT_TYPE_ID == claimDocumentTypeDto.getDocumentTypeId()) {
                    claimCalculationSheetMainDao.updatePremiumOutstandingUploadReject(connection, claimDocumentDto.getClaimNo(), claimDocumentDto.getRefNo());
                }
                saveClaimsLogs(connection, claimDocumentDto.getClaimNo(), user, "Reject Document", claimDocumentTypeDto.getDocumentTypeName().concat(" has been rejected"));
                if (!claimDocumentDto.getRemark().isEmpty()) {
                    saveDocumentRejectRemark(connection, user, claimDocumentDto.getClaimNo(), claimDocumentDto.getRemark());
                }
                saveClaimProcessFlow(connection, claimDocumentDto.getClaimNo(), 0, claimDocumentTypeDto.getDocumentTypeName().concat(" has been rejected"), user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
                return claimDocumentDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return null;
    }

    private void saveDocumentRejectRemark(Connection connection, UserDto user, Integer claimNo, String remark) throws Exception {
        try {
            SpecialRemarkDto specialRemarkDto = new SpecialRemarkDto();
            specialRemarkDto.setClaimNo(claimNo);
            specialRemarkDto.setRemark(remark);
            specialRemarkDto.setInputDateTime(Utility.sysDateTime());
            specialRemarkDto.setInputUserId(user.getUserId());
            specialRemarkDto.setDepartmentId(AppConstant.MOTOR_ENGINEER_DEPARTMENT_ID);
            specialRemarkDto.setSectionName("Document Rejection");
            specialRemarkDao.insertMaster(connection, specialRemarkDto);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public ClaimDocumentDto getClaimDocumentDto(Integer refId) {
        ClaimDocumentDto claimDocumentDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            claimDocumentDto = claimDocumentDao.searchMaster(connection, refId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimDocumentDto;
    }


    @Override
    public void storeFile(Integer claimNo, UserDto user, String remark) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);
            boolean isOffer = claimHandlerDto.getIsProvideOffer().equalsIgnoreCase(AppConstant.YES);
            claimHandlerDao.updateStoreStatusAndUserID(connection, claimNo, "Y",user);
            saveClaimSpecialRemark(connection, user, claimNo, "Store File", remark);
            saveClaimsLogs(connection, claimNo, user, "Store File", "Store File");
            saveClaimProcessFlow(connection, claimNo, 0, "Store File", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
            shiftNotificationOnAction(connection, claimNo, user);
            commitTransaction(connection);
            DesktopInspectionDetailsDto desktopOfferDetails = desktopInspectionDetailsMeDao.getDesktopOfferDetails(connection, claimNo);
            OnSiteInspectionDetailsDto onsiteOfferDetails = onSiteInspectionDetailsMeDao.getOnsiteOfferDetails(connection, claimNo);
            ClaimsDto claim = callCenterService.getClaimsDtoByClaimNo(connection, claimNo);
            if (user.getAccessUserType() == AppConstant.ACCESS_LEVEL_DECISION_MAKER && claimHandlerDto.getLiabilityAprvStatus().equals(AppConstant.APPROVE) && isOffer) {
                if (desktopOfferDetails != null && desktopOfferDetails.getDesktopOffer() == ConditionType.Yes) {
                    sendOfferSms(connection, claim, user);
                } else if (onsiteOfferDetails !=null && onsiteOfferDetails.getProvideOffer()==ConditionType.Yes && onsiteOfferDetails.getOfferType()== AppConstant.ON_SITE_INSPECTION) {
                    sendOfferSms(connection, claim, user);
                }
            }
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void restoreFile(Integer claimNo, UserDto user, String remark) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            claimHandlerDao.updateStoreStatus(connection, claimNo, "N");
            saveClaimSpecialRemark(connection, user, claimNo, "Restore File", remark);
            saveClaimsLogs(connection, claimNo, user, "Restore File", "Restore File");
            saveClaimProcessFlow(connection, claimNo, 0, "Restore File", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void approveInitialLiability(Integer claimNo, UserDto user, String remark, BigDecimal outstandingPremium, boolean isCancelledPolicy) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
        StringBuilder sbMessage = new StringBuilder();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDto.setInitLiabilityAprvDateTime(Utility.sysDateTime());
            claimHandlerDto.setInitLiabilityAprvUserId(user.getUserId());
            claimHandlerDto.setInitLiabilityAprvStatus("A");
            claimHandlerDao.updateInitialLiabilityAprvStatus(connection, claimHandlerDto);
            //36 --> INITIAL LIABILITY APPROVED
            claimHandlerDao.updateClaimStatus(connection, claimNo, 36);

            //get Claim Handler User to Assign | 41 --> Claim Handler
            ClaimHandlerDto searchedClaimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);
            String userId = (null == searchedClaimHandlerDto.getAssignUserId()) ? AppConstant.STRING_EMPTY : searchedClaimHandlerDto.getAssignUserId();
            if (userId.isEmpty()) {
                if (searchedClaimHandlerDto.getIsProvideOffer().equalsIgnoreCase(AppConstant.YES)) {
                    userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_OFFER_TEAM_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
                } else {
                    userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
                }

            }
            userId=user.getUserId();// HNB Update
            saveClaimProcessFlow(connection, claimNo, 36, "Initial Liability Approved & Forward To Payment", user.getUserId(), Utility.sysDateTime(), userId);
            claimHandlerDto.setAssignDateTime(Utility.sysDateTime());
            claimHandlerDto.setAssignStatus("P");
            claimHandlerDto.setAssignUserId(userId);
            claimHandlerDao.updateAssignUser(connection, claimHandlerDto);

            claimHandlerDto.setLiabilityAprvAssignDateTime(Utility.sysDateTime());
            claimHandlerDto.setLiabilityAprvAssignUser(userId);
            claimHandlerDao.updateLiabilityAprvAssignUser(connection, claimHandlerDto);

            //TODO - NOTIFICATION TO --> claimHandlerDto.getAssignUserId() | MSG - Approve Initial Liability
            sbMessage.append("You have received initial liability checked claim file");
            saveNotification(connection, claimNo, user.getUserId(), userId, sbMessage.toString(), URL);
            saveClaimSpecialRemark(connection, user, claimNo, "Approve Initial Liability", remark);
            List<ReminderPrintSummaryDto> reminderPrintSummaryDtos = reminderPrintSummaryDao.searchAllByClaimNo(connection, claimNo);

            if (null != reminderPrintSummaryDtos && !reminderPrintSummaryDtos.isEmpty()) {
                claimHandlerDao.updateStoreStatus(connection, claimNo, "Y");
            }

            saveConfirmationLogs(connection, claimHandlerDto.getClaimNo(), user, outstandingPremium, isCancelledPolicy);
            saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Approve Initial liability", "Approve Initial liability [" + userId + "]");
            shiftNotificationOnAction(connection, claimNo, user);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void updateClaimAssignUser(Integer claimNo, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            this.updateClaimAssignUser(connection, claimNo, user);
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void updateClaimAssignUser(Connection connection, Integer claimNo, UserDto user) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
        StringBuilder sbMessage = new StringBuilder();
        try {
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            ClaimHandlerDto searchedClaimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);
            if ((null == searchedClaimHandlerDto.getAssignUserId()
                    || searchedClaimHandlerDto.getAssignUserId().isEmpty())
                    && (searchedClaimHandlerDto.getInitLiabilityAprvUserId() != null
                    && !searchedClaimHandlerDto.getInitLiabilityAprvUserId().isEmpty())) {

                //get Claim Handler User to Assign | 41 --> Claim Handler

                String userId;
                if (searchedClaimHandlerDto.getIsProvideOffer().equalsIgnoreCase(AppConstant.YES)) {
                    userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_OFFER_TEAM_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
                } else {
                    userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
                }
                claimHandlerDto.setAssignDateTime(Utility.sysDateTime());
                claimHandlerDto.setAssignStatus("P");
                claimHandlerDto.setAssignUserId(userId);
                claimHandlerDao.updateAssignUser(connection, claimHandlerDto);

                //TODO - NOTIFICATION TO --> claimHandlerDto.getAssignUserId() | MSG - Liability Approved
                sbMessage.append("You have received a  Liability approval request claim file");
                saveNotification(connection, claimNo, user.getUserId(), userId, sbMessage.toString(), URL);
                claimHandlerDto.setLiabilityAprvAssignDateTime(Utility.sysDateTime());
                claimHandlerDto.setLiabilityAprvAssignUser(userId);
                claimHandlerDao.updateLiabilityAprvAssignUser(connection, claimHandlerDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public void referClaimPanel(Integer claimNo, UserDto user, String remark, Integer repudiatedReason) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
        StringBuilder sbMessage = new StringBuilder();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            // DM -> CH Changed for HNB
            //38 --> INITIAL LIABILITY DECISION MAKING

            ClaimHandlerDto searchedClaimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);

//            String decisionMakingUserId = (null == searchedClaimHandlerDto.getDecisionMakingAssignUserId()) ? AppConstant.STRING_EMPTY : searchedClaimHandlerDto.getDecisionMakingAssignUserId();
//            if (decisionMakingUserId.isEmpty()) {
//                decisionMakingUserId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_DECISION_MAKER, AppConstant.CLAIM_HANDLER_INVESTIGATION_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
//            }

            String decisionMakingUserId = user.getUserId();

            claimHandlerDao.updateClaimStatus(connection, claimNo, 38);
            saveClaimProcessFlow(connection, claimNo, 38, "Claim File has been forward to decision maker", user.getUserId(), Utility.sysDateTime(), decisionMakingUserId);

            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDto.setDecisionMakingAssignDateTime(Utility.sysDateTime());
            claimHandlerDto.setDecisionMakingAssignUserId(decisionMakingUserId);
            claimHandlerDao.updateDecisionMakingUser(connection, claimHandlerDto);
            claimHandlerDao.updateRepudiatedReason(connection, claimNo, repudiatedReason);
            String Reason = dbRecordCommonFunction.getValue("claim_repudiated_reason", "V_REPUDIATED_REASON_DESC", "N_REF_NO", String.valueOf(repudiatedReason));

            saveClaimsLogs(connection, claimNo, user, "Repudiated Reason ", "=".concat(Reason));

            if (searchedClaimHandlerDto.getInitLiabilityAprvStatus().equals(AppConstant.STRING_PENDING)) {
                claimHandlerDto.setInitLiabilityAprvDateTime(Utility.sysDateTime());
                claimHandlerDto.setInitLiabilityAprvUserId(user.getUserId());
                claimHandlerDto.setInitLiabilityAprvStatus("A");
                claimHandlerDao.updateInitialLiabilityAprvStatus(connection, claimHandlerDto);
            }

            //TODO - NOTIFICATION TO --> decisionMakingUserId | MSG - Forward To Decision Maker
            ClaimHandlerDto claims = new ClaimHandlerDto();
            claims.setClaimNo(claimNo);
            claims.setAssignUserId(decisionMakingUserId);

            sbMessage.append("You have received file to make a decision");
            saveNotification(connection, claimNo, user.getUserId(), decisionMakingUserId, sbMessage.toString(), URL);
            saveClaimSpecialRemark(connection, user, claimNo, "Forward To Decision Maker", remark);
            saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Forward To Decision Maker", "Forward To Decision Maker [".concat(decisionMakingUserId).concat("]"));
            shiftNotificationOnAction(connection, claimNo, user);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void forwardToPanel(Integer claimNo, UserDto user, String panelId, String remark, Integer repudiatedReason) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
        StringBuilder sbMessage = new StringBuilder();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            //39 --> INITIAL LIABILITY 2 MEMBER PANEL
            //40 --> INITIAL LIABILITY 4 MEMBER PANEL

            int status = 0;
            String panelName;
            List<String> userIdList;

            switch (panelId) {
                case "1":
                    status = 39;
                    panelName = "SUB PANEL";
                    userIdList = claimHandlerDao.getPanelUserIdList(connection, 1);
                    if (userIdList == null || userIdList.isEmpty()) {
                        throw new UserNotFoundException();
                    }
                    break;
                case "2":
                    status = 40;
                    panelName = "MAIN PANEL";
                    userIdList = claimHandlerDao.getPanelUserIdList(connection, 2);
                    break;
                default:
                    throw new AssertionError();
            }

            Integer currentClaimStatus = claimHandlerDao.getClaimStatusByClaimNo(connection, claimNo);
            claimHandlerDao.updateClaimStatus(connection, claimNo, status);
            claimHandlerDao.updateRepudiatedReason(connection, claimNo, repudiatedReason);

            List<ClaimPanelAssignUserDto> claimPanelAssignUserDtoList = new ArrayList<>();
            for (String userId : userIdList) {
                ClaimPanelAssignUserDto claimPanelAssignUserDto = new ClaimPanelAssignUserDto();
                claimPanelAssignUserDto.setClaimNo(claimNo);
                claimPanelAssignUserDto.setInputDateTime(Utility.sysDateTime());
                claimPanelAssignUserDto.setInputUser(user.getUserId());
                claimPanelAssignUserDto.setUserId(userId);
                claimPanelAssignUserDto.setStatus(AppConstant.STRING_PENDING);
                claimPanelAssignUserDto.setDmRemark(panelId.equals(String.valueOf(AppConstant.MAIN_PANEL)) ? remark : AppConstant.STRING_EMPTY);
                claimPanelAssignUserDto.setPanelId(Integer.parseInt(panelId));
                claimPanelAssignUserDtoList.add(claimPanelAssignUserDto);
            }
            if (AppConstant.FORWARD_TO_2_MEMBER_STATUS.equals(currentClaimStatus)) {
                List<ClaimPanelAssignUserDto> assignedUsersByClaimNo = claimPanelAssignUserDao.getAssignedUSersByClaimNo(connection, claimNo);
                for (ClaimPanelAssignUserDto userDetails : assignedUsersByClaimNo) {
                    UserDto panelUser = new UserDto();
                    panelUser.setUserId(userDetails.getUserId());
                    shiftNotificationOnAction(connection, claimNo, panelUser);
                }
            } else {
                shiftNotificationOnAction(connection, claimNo, user);
            }

            ClaimPanelAssignUserDto claimPanelDetails = claimPanelAssignUserDao.searchByClaimNo(connection, claimNo);
            if (null != claimPanelDetails && claimPanelDetails.getPanelId().equals(AppConstant.MAIN_PANEL)) {
                mainPanelAssignHstDao.shiftFromPanel(connection, claimNo);
            }
            claimPanelAssignUserDao.deleteByClaimNo(connection, claimNo);

            sbMessage.append("You have received file to make a panel decision");
            for (ClaimPanelAssignUserDto claimPanelAssignUserDto : claimPanelAssignUserDtoList) {
                claimPanelAssignUserDao.insertMaster(connection, claimPanelAssignUserDto);
                ClaimHandlerDto calClaimHandlerDto = new ClaimHandlerDto();
                calClaimHandlerDto.setAssignUserId(claimPanelAssignUserDto.getUserId());
                calClaimHandlerDto.setClaimNo(claimPanelAssignUserDto.getClaimNo());
                saveNotification(connection, claimNo, user.getUserId(), claimPanelAssignUserDto.getUserId(), sbMessage.toString(), URL);
            }

            //TODO - NOTIFICATION TO --> userIdList | MSG - "Forward To " + panelName
            saveClaimSpecialRemark(connection, user, claimNo, "Forward To " + panelName, remark);
            saveClaimProcessFlow(connection, claimNo, status, "Claim File has been forward to ".concat(panelName), user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void approveByPanel(Integer claimNo, UserDto user, String panelId, String remark, Integer repudiatedReason) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
        StringBuilder sbMessage = new StringBuilder();
        StringBuilder sbMessagetodmaker = new StringBuilder();
        Connection connection = null;
        String userId;
        boolean isRejected = false;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            //41 --> 2 MEMBER PANEL APPROVED
            //44 --> 4 MEMBER PANEL APPROVED

            int status;
            String panelName;
            switch (panelId) {
                case "1":
                    status = 41;
                    panelName = "SUB PANEL";
                    isRejected = true;
                    break;
                case "2":
                    status = 44;
                    panelName = "MAIN PANEL";
                    isRejected = claimPanelAssignUserDao.isPanelApprovedOrRejected(connection, claimNo, user.getUserId());
                    break;
                default:
                    throw new AssertionError();
            }

            claimHandlerDao.updateRepudiatedReason(connection, claimNo, repudiatedReason);
            claimPanelAssignUserDao.rejectClaimByPanel(connection, claimNo, user, remark);

            if (isRejected) {
                claimHandlerDao.updateClaimStatus(connection, claimNo, status);
                userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.LETTER_PANEL, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
                if (null != userId && !userId.isEmpty()) {
                    claimHandlerDao.updateLetterPanelUser(connection, claimNo, userId);
                }
                claimHandlerDao.updateClaimPanel(connection, claimNo, panelId, AppConstant.REJECT);
                String reason = dbRecordCommonFunction.getValue("claim_repudiated_reason", "V_REPUDIATED_REASON_DESC", "N_REF_NO", String.valueOf(repudiatedReason));

                ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);
                //TODO - NOTIFICATION TO --> claimHandlerDto.getDecisionMakingAssignUserId() | MSG - "Rejection Approved By " + panelName
                sbMessage.append("Claim File rejected by ").append(panelName).append(AppConstant.ZERO_INT == repudiatedReason ? "" : " Under ").append(AppConstant.ZERO_INT == repudiatedReason ? "" : reason).append(" & Forwarded to Prepare the Rejection Letter");
                sbMessagetodmaker.append("Claim File rejected by ").append(panelName).append(AppConstant.ZERO_INT == repudiatedReason ? "" : " Under ").append(AppConstant.ZERO_INT == repudiatedReason ? "" : reason).append(" & Forwarded to Letter Panel for Rejection Letter");
                saveNotification(connection, claimNo, user.getUserId(), claimHandlerDto.getLetterPanelUserId(), sbMessage.toString(), URL);
                saveNotification(connection, claimNo, user.getUserId(), claimHandlerDto.getDecisionMakingAssignUserId(), sbMessagetodmaker.toString(), URL);
                saveClaimProcessFlow(connection, claimNo, status, "Claim File rejected by ".concat(panelName), user.getUserId(), Utility.sysDateTime(), claimHandlerDto.getDecisionMakingAssignUserId());
                saveClaimProcessFlow(connection, claimNo, status, "Claim File Forwarded to Letter Panel", user.getUserId(), Utility.sysDateTime(), claimHandlerDto.getLetterPanelUserId());
                saveClaimSpecialRemark(connection, user, claimNo, "Claim Rejected By " + panelName, remark);
                saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Claim Rejected", "Rejected By " + panelName.concat(AppConstant.ZERO_INT == repudiatedReason ? "" : " Under ").concat(AppConstant.ZERO_INT == repudiatedReason ? "" : reason));
                saveBranchRemark(connection, "Claim File rejected by ".concat(panelName).concat(AppConstant.ZERO_INT == repudiatedReason ? "" : " Under ".concat(reason)), claimNo, AppConstant.STRING_EMPTY);
                List<ClaimPanelAssignUserDto> assignedUSersByClaimNo = claimPanelAssignUserDao.getAssignedUSersByClaimNo(connection, claimNo);
                for (ClaimPanelAssignUserDto userDetails : assignedUSersByClaimNo) {
                    UserDto panelUser = new UserDto();
                    panelUser.setUserId(userDetails.getUserId());
                    shiftNotificationOnAction(connection, claimNo, panelUser);
                }
            } else {
                saveClaimSpecialRemark(connection, user, claimNo, "Claim Rejected By Main Panel Member", remark);
                saveClaimsLogs(connection, claimNo, user, "Claim File Rejected by Main Panel Member", "Rejected By " + user.getUserId());
            }
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void rejectByPanel(Integer claimNo, UserDto user, String panelId, String remark) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
        StringBuilder sbMessage = new StringBuilder();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            //42 --> 2 MEMBER PANEL REJECTED
            //43 --> 4 MEMBER PANEL REJECTED

            int status;
            String panelName;
            boolean isApproved = false;
            switch (panelId) {
                case "1":
                    status = 42;
                    panelName = "SUB PANEL";
                    isApproved = true;
                    break;
                case "2":
                    status = 45;
                    panelName = "MAIN PANEL";
                    isApproved = claimPanelAssignUserDao.isPanelApprovedOrRejected(connection, claimNo, user.getUserId());
                    claimPanelAssignUserDao.approveClaimByPanelUser(connection, claimNo, user, remark);
                    break;
                default:
                    throw new AssertionError();
            }

            if (isApproved) {
                claimHandlerDao.updateClaimStatus(connection, claimNo, status);
                claimHandlerDao.updateClaimPanel(connection, claimNo, panelId, AppConstant.APPROVE);
                ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);
                //TODO - NOTIFICATION TO --> claimHandlerDto.getDecisionMakingAssignUserId() | MSG - "Rejection Reject By " + panelName
                sbMessage.append("You have received approved claim from ").append(panelName);
                saveNotification(connection, claimNo, user.getUserId(), claimHandlerDto.getDecisionMakingAssignUserId(), sbMessage.toString(), URL);
                saveClaimProcessFlow(connection, claimNo, status, "Claim File approved by ".concat(panelName), user.getUserId(), Utility.sysDateTime(), claimHandlerDto.getDecisionMakingAssignUserId());
                saveClaimSpecialRemark(connection, user, claimNo, "Claim Approved By " + panelName, remark);
                saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Claim Approved", "Approved By " + panelName);
                List<ClaimPanelAssignUserDto> assignedUSersByClaimNo = claimPanelAssignUserDao.getAssignedUSersByClaimNo(connection, claimNo);
                for (ClaimPanelAssignUserDto userDetails : assignedUSersByClaimNo) {
                    UserDto panelUser = new UserDto();
                    panelUser.setUserId(userDetails.getUserId());
                    shiftNotificationOnAction(connection, claimNo, panelUser);
                }
            } else {
                saveClaimSpecialRemark(connection, user, claimNo, "Claim Approved By Main Panel Member", remark);
                saveClaimsLogs(connection, claimNo, user, "Claim File Approved by Main Panel Member", "Approved By " + user.getUserId());
            }
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void requestForInvestigationByPanel(Integer claimNo, UserDto user, String panelId, String remark) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            //42 --> 2 MEMBER PANEL REQUEST INVESTIGATION
            //43 --> 4 MEMBER PANEL REQUEST INVESTIGATION

            int status;
            String panelName;
            switch (panelId) {
                case "1":
                    status = 43;
                    panelName = "SUB PANEL";
                    break;
                case "2":
                    status = 46;
                    panelName = "MAIN PANEL";
                    break;
                default:
                    throw new AssertionError();
            }

            claimHandlerDao.updateClaimStatus(connection, claimNo, status);
            saveClaimSpecialRemark(connection, user, claimNo, "Request For Investigation By " + panelName, remark);
            saveClaimsLogs(connection, claimNo, user, "Request For Investigation", "Request For Investigation By " + panelName);
            saveClaimProcessFlow(connection, claimNo, 0, "Request For Investigation", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.NO);

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<String> getDecisionMakingUserIdList() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimHandlerDao.getPanelUserIdList(connection, 3);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void approveLiability(Integer claimNo, UserDto user, String remark, Integer liabilityApproveType, BigDecimal outstandingPremium, boolean isCancelledPolicy) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
        StringBuilder sbMessage = new StringBuilder();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDto.setLiabilityAprvDateTime(Utility.sysDateTime());
            claimHandlerDto.setLiabilityAprvUser(user.getUserId());
            claimHandlerDto.setLiabilityAprvStatus("A");
            claimHandlerDao.updateLiabilityAprvStatus(connection, claimHandlerDto);
            //50 --> LIABILITY APPROVED
            claimHandlerDao.updateClaimStatus(connection, claimNo, 50);
            if (liabilityApproveType == 1) {
                sbMessage.append("You have received a  Liability approved claim file");
            } else {
                sbMessage.append("You have received a  Liability approved claim file.Now you can process the claim");
            }

            ClaimHandlerDto searchedClaimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);
            String notificationMsg;
            if (searchedClaimHandlerDto.getLossType().equals(AppConstant.TOTAL_LOSS_TYPE)) {
                notificationMsg = "You have received Total loss claim converted as Offer Team Total loss";
            } else {
                notificationMsg = "You have received partial loss claim converted as Offer Team Partial loss";
            }
            boolean isOffer = searchedClaimHandlerDto.getIsProvideOffer().equalsIgnoreCase(AppConstant.YES);
            if ((null == searchedClaimHandlerDto.getAssignUserId()
                    || searchedClaimHandlerDto.getAssignUserId().isEmpty())
                    && (searchedClaimHandlerDto.getInitLiabilityAprvUserId() != null
                    && !searchedClaimHandlerDto.getInitLiabilityAprvUserId().isEmpty())) {

                //get Claim Handler User to Assign | 41 --> Claim Handler


                String userId;
                if (isOffer) {
                    userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_OFFER_TEAM_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
                } else if (searchedClaimHandlerDto.getClaimStatus().equals(AppConstant.FORWARD_TO_D_MAKER_STATUS) && searchedClaimHandlerDto.getLossType().equals(AppConstant.TOTAL_LOSS_TYPE)) {
                    userId = searchedClaimHandlerDto.getAssignUserId();
                } else {
                    userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
                }
                claimHandlerDto.setAssignDateTime(Utility.sysDateTime());
                claimHandlerDto.setAssignStatus("A");
                claimHandlerDto.setAssignUserId(userId);
                claimHandlerDao.updateAssignUser(connection, claimHandlerDto);
                //TODO - NOTIFICATION TO --> claimHandlerDto.getAssignUserId() | MSG - Approve Liability
                saveClaimSpecialRemark(connection, user, claimNo, "Approve Liability", remark);

                if (liabilityApproveType == 1) {
                    saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Approve Liability", "Liability Approved".concat(" Assign User :").concat(claimHandlerDto.getAssignUserId()));
                } else {
                    saveNotification(connection, claimNo, user.getUserId(), claimHandlerDto.getAssignUserId(), isOffer ? notificationMsg : sbMessage.toString(), URL);
                    saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Approve Liability", "Liability in order,forward to Processing".concat(" Assign User :").concat(claimHandlerDto.getAssignUserId()));
                }
                saveClaimProcessFlow(connection, claimNo, 50, "Liability Approved", user.getUserId(), Utility.sysDateTime(), claimHandlerDto.getAssignUserId());

            } else {
                claimHandlerDao.updateAssignStatus(connection, claimNo, "A");
                //TODO - NOTIFICATION TO --> searchedClaimHandlerDto.getAssignUserId() | MSG - Approve Liability
                if (!user.getUserId().equalsIgnoreCase(searchedClaimHandlerDto.getAssignUserId())) {
                    saveNotification(connection, claimNo, user.getUserId(), searchedClaimHandlerDto.getAssignUserId(), isOffer ? notificationMsg : sbMessage.toString(), URL);
                }
                saveClaimProcessFlow(connection, claimNo, 50, "Liability Approved", user.getUserId(), Utility.sysDateTime(), searchedClaimHandlerDto.getAssignUserId());

                saveClaimSpecialRemark(connection, user, claimNo, "Approve Liability", remark);
                saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Approve Liability", "Approve Liability");
            }
//            if (user.getN_accessusrtype() == AppConstant.ACCESS_LEVEL_DECISION_MAKER && isOffer) {
//                ClaimsDto claim = callCenterService.getClaimsDtoByClaimNo(connection, claimNo);
//                sendOfferSms(connection, claim);
//            }
            saveConfirmationLogs(connection, claimHandlerDto.getClaimNo(), user, outstandingPremium, isCancelledPolicy);
            // shiftNotificationOnAction(connection, claimNo, user);

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void pendingLiability(Integer claimNo, UserDto user, String remark) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDto.setLiabilityAprvDateTime(Utility.sysDateTime());
            claimHandlerDto.setLiabilityAprvUser(user.getUserId());
            claimHandlerDto.setLiabilityAprvStatus("P");
            claimHandlerDao.updateLiabilityAprvStatus(connection, claimHandlerDto);
//49	LP	LIABILITY PENDING	4
            claimHandlerDao.updateClaimStatus(connection, claimNo, 49);
            saveClaimProcessFlow(connection, claimNo, 49, "Claim File has been updated to pending", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY);

            saveClaimSpecialRemark(connection, user, claimNo, "Mark As Pending Liability", remark);
            saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Pending Liability", "Mark As Pending Liability");

            shiftNotificationOnAction(connection, claimNo, user);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void requestForInvestigationByDecisionMaker(Integer claimNo, UserDto user, String remark) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_INVESTIGATION));
        StringBuilder sbMessage = new StringBuilder();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
//            52	DECISION MAKER REQUEST INVESTIGATION
            claimHandlerDao.updateClaimStatus(connection, claimNo, 52);

            List<String> userIdList = claimHandlerDao.getPanelUserIdList(connection, 1);

            ClaimPanelAssignUserDto claimPanelDetails = claimPanelAssignUserDao.searchByClaimNo(connection, claimNo);
            if (null != claimPanelDetails) {
                claimPanelAssignUserDao.deleteByClaimNo(connection, claimNo);
            }

            List<ClaimPanelAssignUserDto> claimPanelAssignUserDtoList = new ArrayList<>();
            for (String userId : userIdList) {
                ClaimPanelAssignUserDto claimPanelAssignUserDto = new ClaimPanelAssignUserDto();
                claimPanelAssignUserDto.setClaimNo(claimNo);
                claimPanelAssignUserDto.setInputDateTime(Utility.sysDateTime());
                claimPanelAssignUserDto.setInputUser(user.getUserId());
                claimPanelAssignUserDto.setUserId(userId);
                claimPanelAssignUserDtoList.add(claimPanelAssignUserDto);
            }
            sbMessage.append("You have received file to make a investigation decision");
            for (ClaimPanelAssignUserDto claimPanelAssignUserDto : claimPanelAssignUserDtoList) {
                claimPanelAssignUserDao.insertMaster(connection, claimPanelAssignUserDto);
                ClaimHandlerDto calClaimHandlerDto = new ClaimHandlerDto();
                calClaimHandlerDto.setAssignUserId(claimPanelAssignUserDto.getUserId());
                calClaimHandlerDto.setClaimNo(claimPanelAssignUserDto.getClaimNo());
                saveNotification(connection, claimNo, user.getUserId(), claimPanelAssignUserDto.getUserId(), sbMessage.toString(), URL);
            }
            //TODO - NOTIFICATION TO --> userIdList | MSG - Investigation Request By Decision Maker
            saveClaimSpecialRemark(connection, user, claimNo, "Investigation Request By Decision Maker", remark);
            saveClaimsLogs(connection, claimNo, user, "Investigation Request", "Investigation Request By Decision Maker");
            saveClaimProcessFlow(connection, claimNo, 0, "Decision Maker has been request a investigation", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.NO);

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void requestForInvestigationByClaimHandler(Integer claimNo, UserDto user, String remark) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_INVESTIGATION));
        StringBuilder sbMessage = new StringBuilder();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimHandlerDto searchedClaimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);

            String decisionMakingUserId = (null == searchedClaimHandlerDto.getInvestigationAssignUserId()) ? AppConstant.STRING_EMPTY : searchedClaimHandlerDto.getInvestigationAssignUserId();
            if (decisionMakingUserId.isEmpty()) {
                decisionMakingUserId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_DECISION_MAKER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
            }

            //56	CHRI	CLAIM HANDLER REQUEST INVESTIGATION	4
            claimHandlerDao.updateClaimStatus(connection, claimNo, 56);

            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDto.setInvestigationAssignDateTime(Utility.sysDateTime());
            claimHandlerDto.setInvestigationAssignUserId(decisionMakingUserId);
            claimHandlerDto.setInvestigationStatus("P");
            claimHandlerDao.updateInvestigationAssignUser(connection, claimHandlerDto);

            sbMessage.append("You have received a Investigation Request");
            saveNotification(connection, claimNo, user.getUserId(), decisionMakingUserId, sbMessage.toString(), URL);

            //TODO - NOTIFICATION TO --> decisionMakingUserId | MSG - Investigation Request By Claim Handler
            saveClaimSpecialRemark(connection, user, claimNo, "Investigation Request By Claim Handler", remark);
            saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Investigation Request", "Investigation Request By Claim Handler [" + decisionMakingUserId + "]");
            saveClaimProcessFlow(connection, claimHandlerDto.getClaimNo(), 0, "Investigation Request By Claim Handler [" + decisionMakingUserId + "]", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.NO);

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<ClaimClaimPanelUserDto> getPanelUser(String userId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();

            return claimHandlerDao.searchByUserId(connection, userId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void arrangeInvestigation(Integer claimNo, UserDto user, String panelId, String remark) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_INVESTIGATION));
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDto.setInvestigationArrangeDateTime(Utility.sysDateTime());
            claimHandlerDto.setInvestigationArrangeUserId(user.getUserId());
            claimHandlerDto.setInvestigationStatus(InvestigationStatusEnum.ARRANGE.getInvestigationStatus());
            claimHandlerDao.updateInvestigationArrangedUser(connection, claimHandlerDto);
            //48	2 MEMBER PANEL INVESTIGATION ARRANGED
            claimHandlerDao.updateClaimStatus(connection, claimNo, 48);
            saveClaimSpecialRemark(connection, user, claimNo, "Investigation Arranged By Sub Panel", remark);

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void arrangeInvestigationByDecisionMaker(Integer claimNo, UserDto user, String remark) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDto.setInvestigationArrangeDateTime(Utility.sysDateTime());
            claimHandlerDto.setInvestigationArrangeUserId(user.getUserId());
            claimHandlerDto.setInvestigationStatus(InvestigationStatusEnum.ARRANGE.getInvestigationStatus());
            claimHandlerDao.updateInvestigationArrangedUser(connection, claimHandlerDto);
            //53	DECISION MAKER INVESTIGATION ARRANGED
            claimHandlerDao.updateClaimStatus(connection, claimNo, 53);
            saveClaimSpecialRemark(connection, user, claimNo, "Investigation Arranged By Decision Maker", remark);
            saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Investigation Arranged", "Investigation Arranged By Decision Maker");
            saveClaimProcessFlow(connection, claimHandlerDto.getClaimNo(), 0, "Investigation Arranged By Decision Maker to claim file", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.NO);

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void generateRejectionLetter(Integer claimNo, UserDto user, String rejectReasonId, String remark, Integer rejectionLatterType) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_INVESTIGATION));
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDto.setRejectionLatterType(rejectionLatterType);
            claimHandlerDto.setRepudiatedLetterPrintDateTime(Utility.sysDateTime());
            claimHandlerDto.setRepudiatedLetterPrintUserId(user.getUserId());
            claimHandlerDto.setRepudiatedType(Integer.valueOf(rejectReasonId));
            claimHandlerDto.setIsPrintRepudiatedLetter("Y");
            claimHandlerDao.updateRejectionUser(connection, claimHandlerDto);

//            SpecialRemarkDto specialRemarkDto = new SpecialRemarkDto();
//            specialRemarkDto.setClaimNo(claimNo);
//            specialRemarkDto.setDepartmentId(AppConstant.BRANCH_DEPARTMENT_ID);
//            specialRemarkDto.setSectionName(AppConstant.BRANCH_REMARK);
//            specialRemarkDto.setRemark(remark);
//            specialRemarkDto.setInputUserId(user.getUserId());
//            specialRemarkDto.setInputDateTime(Utility.sysDateTime());
//            specialRemarkDto = specialRemarkDao.insertMaster(connection, specialRemarkDto);
//            saveClaimsLogs(connection, specialRemarkDto.getClaimNo(), user, AppConstant.BRANCH_REMARK, "Insert remark to ".concat(AppConstant.BRANCH_REMARK));

            //47	CLAIM REPUDIATED
            claimHandlerDao.updateClaimStatus(connection, claimNo, 47);
            saveClaimProcessFlow(connection, claimNo, 47, "Generate Rejection Letter", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY);
            updateClaimClose(connection, claimNo, user);
            saveClaimSpecialRemark(connection, user, claimNo, "Generate Rejection Letter", remark);
            saveClaimsLogs(connection, claimNo, user, "Generate Rejection Letter", "Rejection Latter Generated");
            shiftNotificationOnAction(connection, claimNo, user);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }


    @Override
    public List<ClaimRepudiatedReasonDto> getRepudiatedList() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimHandlerDao.getRepudiatedList(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public DataGridDto getClaimHandlerPanelDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, Integer type) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = claimHandlerDao.getClaimHandlerPanelDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, type);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public List<String> getUserListByAccessUserType() throws Exception {
        Connection connection = null;
        List<String> userListByAccessUserType = null;
        try {
            connection = getJDBCConnection();
            userListByAccessUserType = claimHandlerDao.getUserListByAccessUserType(connection);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return userListByAccessUserType;
    }

    @Override
    public List<String> getUserListByAccessUserType(String accessUserTypeList,UserDto sessionUser) throws Exception {
        Connection connection = null;
        List<String> userListByAccessUserType = null;
        try {
            connection = getJDBCConnection();
            userListByAccessUserType = claimHandlerDao.searchAllUserByInAccessUserType(connection, accessUserTypeList, sessionUser);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return userListByAccessUserType;
    }

    @Override
    public List<String> getUserListByAccessUserType(String accessUserTypeList) throws Exception {
        Connection connection = null;
        List<String> userListByAccessUserType = null;
        try {
            connection = getJDBCConnection();
            userListByAccessUserType = claimHandlerDao.searchAllUserByInAccessUserType(connection, accessUserTypeList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return userListByAccessUserType;
    }


    @Override
    public boolean updateSpecialComment(Integer claimNo, String userId, UserDto user, String remark) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
        StringBuilder sbMessage = new StringBuilder();
        Connection connection = null;
        boolean isUpdated;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            isUpdated = claimHandlerDao.updateSpecialComment(connection, claimNo, userId, user.getUserId());
            saveClaimsLogs(connection, claimNo, user, "Forward to obtain  special comment/Approval", "Forward to obtain  special comment/Approval - [".concat(userId).concat("]"));
            sbMessage.append("You have received a special comment or Approval Request");
            saveSpecialRemark(connection, user, claimNo, "Special Comment or Approval Request", remark);
            saveNotification(connection, claimNo, user.getUserId(), userId, sbMessage.toString(), URL, NotificationPriority.HIGH);
            saveClaimProcessFlow(connection, claimNo, 57, "Claim file has been Forward to obtain  special comment/Approval", user.getUserId(), Utility.sysDateTime(), userId, AppConstant.YES);
            if (isClaimHandlerDepartment(user)) {
                shiftNotificationOnAction(connection, claimNo, user);
            }
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return isUpdated;
    }

    @Override
    public boolean requestForSupplierOrder(Integer claimNo, UserDto user, String remark) throws Exception {
        //  String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_SUPPLY_ORDER));

        String URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo))
                .concat("&TYPE=4")
                .concat("&P_TAB_INDEX=").concat(String.valueOf(6));

        StringBuilder sbMessage = new StringBuilder();
        Connection connection = null;
        boolean isUpdated = false;
        String userId;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimHandlerDto searchedClaimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);
            Integer existingDo = supplyOrderRequestDao.getPendingDO(connection, claimNo);
            if (null == existingDo || existingDo.equals(AppConstant.ZERO_INT)) {
                if (null == existingDo) {
                    supplyOrderRequestDao.newDoRequest(connection, claimNo, AppConstant.ZERO_INT);
                }
                URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo))
                        .concat("&TYPE=4")
                        .concat("&P_TAB_INDEX=").concat(String.valueOf(6));
            } else {
                URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&N_REF_NO=").concat(Integer.toString(existingDo))
                        .concat("&TYPE=4")
                        .concat("&P_TAB_INDEX=").concat(String.valueOf(6));
            }
            userId = supplyOrderSummaryDao.getSupplyOrderInputUser(connection, claimNo);

            if (null == userId || AppConstant.STRING_EMPTY.equals(userId)) {
                userId = (null == searchedClaimHandlerDto.getSupplyOrderAssignUser()) ? AppConstant.STRING_EMPTY : searchedClaimHandlerDto.getSupplyOrderAssignUser();
                if (userId.isEmpty()) {
                    userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
                }
            }

            isUpdated = claimHandlerDao.updateSupplierOrderAssignDetails(connection, claimNo, userId, AppConstant.YES, Utility.sysDateTime());
            saveClaimSpecialRemark(connection, user, claimNo, "Forward to spare parts coordinator", remark);
            saveClaimsLogs(connection, claimNo, user, "Forward to spare parts coordinator", "Forward to spare parts coordinator [" + userId + "]");
            sbMessage.append("You have received a Supply Order creation Request");
            saveClaimProcessFlow(connection, claimNo, 0, "Claim File has been Forward to spare parts coordinator [" + userId + "]", user.getUserId(), Utility.sysDateTime(), userId, AppConstant.NO);

            if (motorEngineerDetailsDao.isInspectionPending(connection, claimNo, AppConstant.ZERO_INT)) {
                if (motorEngineerDetailsDao.isAriSalvagePending(connection, claimNo)) {
                    claimHandlerDao.updateStoreStatus(connection, claimNo, "Y");
                    saveClaimsLogs(connection, claimNo, user, "Auto Store File", "Claim File Auto Stored due to Pending ARI/ Salvage Inspection");
                    saveClaimProcessFlow(connection, claimNo, 0, "Auto Store File", userId, Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
                }
                UserDto user1 = new UserDto();
                user1.setUserId(userId);
                user1.setAccessUserType(AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR);
                sendNotificationForJobPendingRte(claimNo, user1, connection, true);
                rtePendingClaimDetailDao.savePendingJobs(connection, claimNo, AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR, userId);
                saveNotification(connection, claimNo, user.getUserId(), userId, sbMessage.toString(), URL, NotificationPriority.HIGH, "#FFFFBF");
            } else {
                saveNotification(connection, claimNo, user.getUserId(), userId, sbMessage.toString(), URL, NotificationPriority.HIGH);
            }

            shiftNotificationOnAction(connection, claimNo, user);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return isUpdated;
    }

    @Override
    public void approveLiabilityAndStoreFile(Integer claimNo, UserDto user, String remark, BigDecimal outstandingPremium, boolean isCancelledPolicy) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
        StringBuilder sbMessage = new StringBuilder();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDto.setInitLiabilityAprvDateTime(Utility.sysDateTime());
            claimHandlerDto.setInitLiabilityAprvUserId(user.getUserId());
            claimHandlerDto.setInitLiabilityAprvStatus("A");
            claimHandlerDao.updateInitialLiabilityAprvStatus(connection, claimHandlerDto);
            //36 --> INITIAL LIABILITY APPROVED
            claimHandlerDao.updateClaimStatus(connection, claimNo, 36);

            //get Claim Handler User to Assign | 41 --> Claim Handler
            ClaimHandlerDto searchedClaimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);
            String userId = (null == searchedClaimHandlerDto.getAssignUserId()) ? AppConstant.STRING_EMPTY : searchedClaimHandlerDto.getAssignUserId();
            if (userId.isEmpty()) {
                if (searchedClaimHandlerDto.getIsProvideOffer().equalsIgnoreCase(AppConstant.YES)) {
                    userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_OFFER_TEAM_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
                } else {
                    userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
                }
            }

            saveClaimProcessFlow(connection, claimNo, 36, "Liablity Approved and Store File", user.getUserId(), Utility.sysDateTime(), userId);

            claimHandlerDto.setAssignDateTime(Utility.sysDateTime());
            claimHandlerDto.setAssignStatus("P");
            claimHandlerDto.setAssignUserId(userId);
            claimHandlerDao.updateAssignUser(connection, claimHandlerDto);

            claimHandlerDto.setLiabilityAprvAssignDateTime(Utility.sysDateTime());
            claimHandlerDto.setLiabilityAprvAssignUser(userId);
            claimHandlerDao.updateLiabilityAprvAssignUser(connection, claimHandlerDto);

            //TODO - NOTIFICATION TO --> claimHandlerDto.getAssignUserId() | MSG - Approve Initial Liability
            sbMessage.append("You have received initial liability checked claim file");
            // saveNotfication(connection, claimNo, user.getUserId(), userId, sbMessage.toString(), URL);
            saveClaimSpecialRemark(connection, user, claimNo, "Approve Initial Liability", remark);
            List<ReminderPrintSummaryDto> reminderPrintSummaryDtos = reminderPrintSummaryDao.searchAllByClaimNo(connection, claimNo);

            if (null != reminderPrintSummaryDtos && !reminderPrintSummaryDtos.isEmpty()) {
                claimHandlerDao.updateStoreStatus(connection, claimNo, "Y");
            }
            claimHandlerDao.updateStoreStatusAndUserID(connection, claimNo, "Y",user);
            saveClaimSpecialRemark(connection, user, claimNo, "Store File", remark);
            saveConfirmationLogs(connection, claimHandlerDto.getClaimNo(), user, outstandingPremium, isCancelledPolicy);
            saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Liability in order,forward to store", "Liability in order,forward to store");

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public DataGridDto getClaimHandlerScrutinizingDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, Integer type) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = claimHandlerDao.getClaimHandlerScrutinizingDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, type);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public void approveInitialLiabilityAndStoreFile(Integer claimNo, UserDto user, String remark, BigDecimal outstandingPremium, boolean isCancelledPolicy) throws Exception {
//        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
//        StringBuilder sbMessage = new StringBuilder();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDto.setInitLiabilityAprvDateTime(Utility.sysDateTime());
            claimHandlerDto.setInitLiabilityAprvUserId(user.getUserId());
            claimHandlerDto.setInitLiabilityAprvStatus("A");
            claimHandlerDao.updateInitialLiabilityAprvStatus(connection, claimHandlerDto);
            //36 --> INITIAL LIABILITY APPROVED
            claimHandlerDao.updateClaimStatus(connection, claimNo, 36);

            //get Claim Handler User to Assign | 41 --> Claim Handler
            ClaimHandlerDto searchedClaimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);
            boolean isOffer = searchedClaimHandlerDto.getIsProvideOffer().equalsIgnoreCase(AppConstant.YES);
            String userId = (null == searchedClaimHandlerDto.getAssignUserId()) ? AppConstant.STRING_EMPTY : searchedClaimHandlerDto.getAssignUserId();
            if (userId.isEmpty()) {
                if (isOffer) {
                    userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_OFFER_TEAM_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
                } else {
                    userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
                }
            }

            saveClaimProcessFlow(connection, claimNo, 36, "Initial Liability Approved & Store ", user.getUserId(), Utility.sysDateTime(), userId);
            claimHandlerDto.setAssignDateTime(Utility.sysDateTime());
            claimHandlerDto.setAssignStatus("P");
            claimHandlerDto.setAssignUserId(userId);
            claimHandlerDao.updateAssignUser(connection, claimHandlerDto);

            claimHandlerDto.setLiabilityAprvAssignDateTime(Utility.sysDateTime());
            claimHandlerDto.setLiabilityAprvAssignUser(userId);
            claimHandlerDao.updateLiabilityAprvAssignUser(connection, claimHandlerDto);

            //TODO - NOTIFICATION TO --> claimHandlerDto.getAssignUserId() | MSG - Approve Initial Liability
//            sbMessage.append("You have received initial liability check claim file");
//            saveNotfication(connection, claimNo, user.getUserId(), userId, sbMessage.toString(), URL);
            saveClaimSpecialRemark(connection, user, claimNo, "Approve Initial Liability", remark);
            List<ReminderPrintSummaryDto> reminderPrintSummaryDtos = reminderPrintSummaryDao.searchAllByClaimNo(connection, claimNo);

//            if (null != reminderPrintSummaryDtos && !reminderPrintSummaryDtos.isEmpty()) {
//                claimHandlerDao.updateStoreStatus(connection, claimNo, "Y");
//            }
            claimHandlerDao.updateStoreStatusAndUserID(connection, claimNo, "Y",user);
            saveClaimSpecialRemark(connection, user, claimNo, "Store File", remark);
            shiftNotificationOnAction(connection, claimHandlerDto.getClaimNo(), user);
            saveConfirmationLogs(connection, claimHandlerDto.getClaimNo(), user, outstandingPremium, isCancelledPolicy);
            saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Approve Initial liability And Store File", "Approve Initial liability And Store File");

            commitTransaction(connection);

            DesktopInspectionDetailsDto desktopOfferDetails = desktopInspectionDetailsMeDao.getDesktopOfferDetails(connection, claimNo);
            OnSiteInspectionDetailsDto onsiteOfferDetails = onSiteInspectionDetailsMeDao.getOnsiteOfferDetails(connection, claimNo);
            ClaimsDto claim = callCenterService.getClaimsDtoByClaimNo(connection, claimNo);
            if (user.getAccessUserType() == AppConstant.ACCESS_LEVEL_OFFER_TEAM_INITIAL_LIABILITY && claimHandlerDto.getInitLiabilityAprvStatus().equals(AppConstant.APPROVE) && isOffer) {
                if (desktopOfferDetails != null && desktopOfferDetails.getDesktopOffer() == ConditionType.Yes) {
                    sendOfferSms(connection, claim, user);
                } else if (onsiteOfferDetails !=null && onsiteOfferDetails.getProvideOffer()==ConditionType.Yes && onsiteOfferDetails.getOfferType()== AppConstant.ON_SITE_INSPECTION) {
                    sendOfferSms(connection, claim, user);
                }
            }
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public boolean isLiablityChecked(Integer claimNo) throws Exception {
        Connection connection = null;
        boolean isChecked = false;
        try {
            connection = getJDBCConnection();
            isChecked = claimHandlerDao.isLiablityChecked(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return isChecked;
    }

    @Override
    public boolean requestedAri(Integer claimNo, Integer reason, String remark, UserDto user) throws Exception {
        boolean isRequested = false;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            RequestAriDto requestAri = requestAriDao.getLatestAriRequest(connection, claimNo);

            RequestAriDto requestAriDto = new RequestAriDto();
            if (null == requestAri) {
                requestAriDto.setClaimNo(claimNo);
                ClaimsDto polClaim = callCenterService.getReportAccidentClaimsDtoByClaimNo(claimNo);
                requestAriDto.setCustomerName(polClaim.getPolicyDto().getCustName());
                requestAriDto.setAddress1(polClaim.getPolicyDto().getCustAddressLine1());
                requestAriDto.setAddress2(polClaim.getPolicyDto().getCustAddressLine2());
                requestAriDto.setAddress3(polClaim.getPolicyDto().getCustAddressLine3());
                requestAriDto.setContactNo(polClaim.getPolicyDto().getCustMobileNo());
                requestAriDto.setRemark(remark);
                requestAriDto.setRequestAriReason(reason);
                RequestAriDto insert = requestAriService.insert(requestAriDto, user, connection);
                claimHandlerDao.updateStoreStatus(connection, claimNo, "Y");
                saveClaimsLogs(connection, claimNo, user, "Auto Store File", "Claim File Auto Stored due to ARI Request");
                saveClaimProcessFlow(connection, claimNo, 0, "Auto Store File", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
                rtePendingClaimDetailDao.savePendingJobs(connection, claimNo, user.getAccessUserType(), user.getUserId());
                if (null != insert) {
                    isRequested = true;
                    String ariReason;
                    if (reason.equals(ARIReasonEnum.SALVAGE.getAriReason())) {
                        ariReason = "for Salvage";
                    } else if (reason.equals(ARIReasonEnum.ARI_SALVAGE.getAriReason())) {
                        ariReason = "for ARI Salvage";
                    } else if (reason.equals(ARIReasonEnum.COLLECT_SALVAGE.getAriReason())) {
                        ariReason = "to Collect Salvage";
                    } else {
                        ariReason = "for ARI";
                    }
                    String assignedClaimHandler = claimHandlerDao.getAssignedClaimHandler(connection, claimNo);
                    saveNotification(connection, claimNo, user.getUserId(), assignedClaimHandler, "ARI Requested for the Claim", "/ClaimHandlerController/viewEdit?P_N_CLIM_NO=".concat(String.valueOf(requestAriDto.getClaimNo())).concat("&P_TAB_INDEX=0"));
                    List<UserDto> userList = requestAriDao.getUserListByAccessUserType(connection, AppConstant.TECHNICAL_CORDINATOR_ACCESSUSRTYPE);
                    List<UserDto> userListAriOnly = requestAriDao.getUserListByAccessUserType(connection, AppConstant.TECHNICAL_CORDINATOR_ARI_ACCESSUSRTYPE);
                    userList.addAll(userListAriOnly);
                    for (UserDto userDto : userList) {
                        if (!user.getUserId().equals(userDto.getUserId())) {
                            saveNotification(connection, requestAriDto.getClaimNo(), user.getUserId(), userDto.getUserId(), "ARI Requested for the Claim", "/RequestAriController/ariListView?TYPE="
                                    .concat(user.getAccessUserType() == AppConstant.ACCESS_LEVEL_SCRUTINIZING_TEAM || user.getAccessUserType() == AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR ? "6" : "5").concat("&P_N_CLIM_NO=").concat(Integer.toString(requestAriDto.getClaimNo())));
                        }
                    }
                    saveClaimsLogs(connection, claimNo, user, "ARI Request", "Requested " + ariReason + " by " + user.getUserId());
                    saveClaimProcessFlow(connection, claimNo, 0, "ARI Requested", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.NO);
                    saveSpecialRemark(connection, user, claimNo, "ARI Requested", remark);
                }
            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return isRequested;
    }

    @Override
    public void updateClaimClose(Integer claimNo, UserDto user, String remark) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setCloseStatus(AppConstant.CLOSE_STATUS_CLOSE);
            claimHandlerDto.setCloseUser(user.getUserId());
            claimHandlerDto.setCloseDateTime(Utility.sysDateTime());
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDao.updateClaimClose(connection, claimHandlerDto);
            claimHandlerDao.updateClaimStatus(connection, claimNo, AppConstant.CLAIM_STATUS_CLOSE);
            saveClaimsLogs(connection, claimNo, user, "Claim File Closed", "Claim File Closed");
            saveClaimProcessFlow(connection, claimNo, 0, "Claim File has been closed", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
            saveSpecialRemark(connection, user, claimNo, "Claim File Closed", remark);

        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new Exception("Can not Saved");
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void updateClaimClose(Integer claimNo, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setCloseStatus(AppConstant.CLOSE_STATUS_CLOSE);
            claimHandlerDto.setCloseUser(user.getUserId());
            claimHandlerDto.setCloseDateTime(Utility.sysDateTime());
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDao.updateClaimClose(connection, claimHandlerDto);
            saveClaimsLogs(connection, claimNo, user, "Claim File Closed", "Claim File Closed");
            saveClaimProcessFlow(connection, claimNo, 0, "Claim File has been closed", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);

        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new Exception("Can not Saved");
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void updateClaimReopen(Integer claimNo, String reOpenType, UserDto user, String remark) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setCloseStatus(AppConstant.CLOSE_STATUS_REOPEN);
            claimHandlerDto.setReOpenType(reOpenType);
            claimHandlerDto.setReopenDateTime(Utility.sysDateTime());
            claimHandlerDto.setReopenUserId(user.getUserId());
            claimHandlerDto.setClaimNo(claimNo);


            ClaimHandlerDto claimHandler = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);
            processRepudiatedClaim(connection, claimHandler, reOpenType, user);
            String repudatedType = AppConstant.STRING_EMPTY;
            if (null != claimHandler) {
                if (AppConstant.CLAIM_REPUDIATED == claimHandler.getClaimStatus()) {
                    repudatedType = "REPUDIATED ";
                }
            }
            claimHandlerDao.updateClaimReopen(connection, claimHandlerDto);
            claimHandlerDao.updateClaimStatus(connection, claimNo, AppConstant.CLAIM_STATUS_REOPEN);
            saveClaimsLogs(connection, claimNo, user, "Claim File Reopened", "Claim File Reopened ".concat(repudatedType)
                    .concat("EX".equals(reOpenType) ? "EXGRATIA" : reOpenType));
            saveSpecialRemark(connection, user, claimNo, "Claim File Reopened", remark);
            saveClaimProcessFlow(connection, claimNo, 0, "Claim File has been reopened", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void forwardToClaimHandler(Integer claimNo, UserDto user, String remark) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
        StringBuilder sbMessage = new StringBuilder();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);

            sbMessage.append("You have received a file ");
            saveNotification(connection, claimNo, user.getUserId(), claimHandlerDto.getAssignUserId(), sbMessage.toString(), URL);
            saveClaimSpecialRemark(connection, user, claimNo, "Forward To Claim Handler", remark);
            saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Forward To Claim Handler", "Forward To Claim Handler");
            saveClaimProcessFlow(connection, claimHandlerDto.getClaimNo(), 0, "Forward To Claim Handler", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void addNotificationToBranchUsers(Integer claimNo, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            List<UserDto> branchUserList = getBranchUserList(connection, claimNo);

            for (UserDto userDto : branchUserList) {
                saveNotification(connection, claimNo, user.getUserId(), userDto.getUserId(), "You have received new reminder letter", "#");
            }
            commitTransaction(connection);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public Map<Integer, Integer> getUploadedDocumentsType(Integer claimNo) {
        Connection connection = null;
        boolean isChecked = false;
        Map<Integer, Integer> uploadedDocumentsType = null;
        try {
            connection = getJDBCConnection();
            uploadedDocumentsType = claimHandlerDao.getUploadedDocumentsType(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return uploadedDocumentsType;
    }

    @Override
    public void updateClaimClose(Connection connection, Integer claimNo, UserDto user) throws Exception {

        try {
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setCloseStatus(AppConstant.CLOSE_STATUS_CLOSE);
            claimHandlerDto.setCloseUser(user.getUserId());
            claimHandlerDto.setCloseDateTime(Utility.sysDateTime());
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDao.updateClaimClose(connection, claimHandlerDto);
            saveClaimsLogs(connection, claimNo, user, "Claim File Closed", "Claim File Closed");
            saveClaimProcessFlow(connection, claimHandlerDto.getClaimNo(), 0, "Claim File has been closed", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("Can not Saved");
        }
    }

    @Override
    public void updateClaimSettle(Connection connection, Integer claimNo, UserDto user) throws Exception {
        try {
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setCloseStatus(AppConstant.CLOSE_STATUS_SETTLE_PENDING);
            claimHandlerDto.setCloseUser(user.getUserId());
            claimHandlerDto.setCloseDateTime(Utility.sysDateTime());
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDao.updateClaimClose(connection, claimHandlerDto);
            saveClaimsLogs(connection, claimNo, user, "CMS CLAIM SETTLED PENDING VOUCHER GENARATION", "CMS CLAIM SETTLED PENDING VOUCHER GENARATION");
            saveClaimProcessFlow(connection, claimHandlerDto.getClaimNo(), 0, "CMS CLAIM SETTLED PENDING VOUCHER GENARATION", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("Can not Saved");
        }
    }

    @Override
    public List<ClaimsDto> getTheftClaimsList() throws Exception {
        List<ClaimsDto> list = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = claimHandlerDao.getTheftClaimsList(connection, Utility.sysDate(), getTheftClaimPeriod());
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("Can not Saved");
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public List<UserDto> getTotalUserList() throws Exception {
        Connection connection = null;
        List<UserDto> userList = null;
        try {
            connection = getJDBCConnection();
            userList = requestAriDao.getUserListByAccessUserType(connection, 46);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return userList;
    }

    @Override
    public void setUnderWritingDetails(ClaimsDto claimsDto) {
        try {
            callCenterService.setUnderWritingDetails(claimsDto);
            ClaimsDto viewAccidentClaimsForCallCenter = callCenterService.getViewAccidentClaimsForCallCenter(claimsDto.getClaimNo());
            claimsDto.setDrivenLicenseDocumentList(viewAccidentClaimsForCallCenter.getDrivenLicenseDocumentList());
            claimsDto.setEstimateDocumentList(viewAccidentClaimsForCallCenter.getEstimateDocumentList());
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    @Override
    public DataGridDto getClaimHandlerSupplierDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, Integer type) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = claimHandlerDao.getClaimHandlerSupplierDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, type);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public List<Integer> getSelectedList(String array) {
        List<Integer> list = null;
        if (!array.isEmpty()) {
            array = array.replaceAll("CCB", "");
            list = Arrays.stream(array.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        } else {
            list = new ArrayList<>();
        }

        return list;
    }

    @Override
    public boolean recallDO(Integer claimNo, UserDto user, String remark) throws Exception {

        Connection connection = null;
        boolean isUpdated = false;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);

            isUpdated = claimHandlerDao.updateSupplierOrderAssignDetails(connection, claimNo, null, AppConstant.NO, AppConstant.DEFAULT_DATE_TIME);

            String URL = "/MotorEngineerController/viewSupplyOrderCheck?P_N_CLIM_NO=".concat(String.valueOf(claimNo)) + "&TYPE=4&P_TAB_INDEX=6";
            String msg = "Supply Order has been Recalled by " + user.getUserId();
            saveNotification(connection, claimNo, user.getUserId(), null == claimHandlerDto.getSupplyOrderAssignUser() ? "" : claimHandlerDto.getSupplyOrderAssignUser(), msg, URL);
            saveClaimSpecialRemark(connection, user, claimNo, "Supply Order recall", remark);
            saveClaimsLogs(connection, claimNo, user, "Recall DO", "Supply Order recall");
            saveClaimProcessFlow(connection, claimNo, 0, "Claim File has been Recall (DO)", user.getUserId(), Utility.sysDateTime(), user.getUserId(), AppConstant.NO);

            if (rtePendingClaimDetailDao.checkIfRteJobsPending(connection, claimNo)) {
                UserDto user1 = new UserDto();
                user1.setUserId(null == claimHandlerDto.getSupplyOrderAssignUser() ? "" : claimHandlerDto.getSupplyOrderAssignUser());
                sendNotificationForJobPendingRte(claimNo, user1, connection, false);
                rtePendingClaimDetailDao.removePendingJobs(connection, claimNo);
            }
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return isUpdated;
    }

    @Override
    public void coverNoteApprove(Integer claimNo, UserDto user) {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            String type;
            String message;
            if (AppConstant.ACCESS_LEVEL_INITIAL_LIABILITY.equals(user.getAccessUserType())) {
                type = "initial Liability Approved";
                message = "Approved Initial Liability for a Policy not mapped claim";
            } else {
                type = "Decision Maker Approved";
                message = "Approved Decision Maker for a Policy not mapped claim";
            }
            saveClaimsLogs(connection, claimNo, user, type, message);
            shiftNotificationOnAction(connection, claimNo, user);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void saveInvestigationSelectedImages(String selectedeImagesRefNo, Integer claimNo, UserDto user) throws Exception {
        Connection connection = null;
        InvestigationSelectImageDto investigationSelectImageDto = new InvestigationSelectImageDto();
        List<Integer> list = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            if (!selectedeImagesRefNo.isEmpty()) {
                list = Arrays.stream(selectedeImagesRefNo.split(",")).map(Integer::parseInt).collect(Collectors.toList());
            }
            if (!list.isEmpty()) {
                investigationSelectImageDto.setClaimNo(claimNo);
                investigationSelectImageDto.setSelectUser(user.getUserId());
                investigationSelectImageDto.setSelectDatetime(Utility.sysDateTime());
                for (Integer refNo : list) {
                    investigationSelectImageDto.setImageRefNo(refNo);
                    claimInvestigationSelectImageDao.insertMaster(connection, investigationSelectImageDto);
                }
            }
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void returnToDecisionMaker(Integer claimNo, String remark, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);
            claimHandlerDao.updateClaimStatus(connection, claimNo, AppConstant.CLAIM_STATUS_RETURN_TO_DECISION_MAKER);
            List<ClaimPanelAssignUserDto> assignedUSersByClaimNo = claimPanelAssignUserDao.getAssignedUSersByClaimNo(connection, claimNo);
            for (ClaimPanelAssignUserDto userDetails : assignedUSersByClaimNo) {
                UserDto panelUser = new UserDto();
                panelUser.setUserId(userDetails.getUserId());
                shiftNotificationOnAction(connection, claimNo, panelUser);
            }
            claimPanelAssignUserDao.returnToDmaker(connection, claimNo, user.getUserId(), remark);
            String URL = "/ClaimHandlerController/viewEdit?P_N_CLIM_NO=" + claimNo + "&P_TAB_INDEX=0";
            String returnedUser = claimHandlerDto.getClaimStatus().equals(AppConstant.CLAIM_STATUS_FORWARD_TO_FOUR_MEMBER_PANEL) ? "MAIN PANEL" : "SUB PANEL";
            String message = "File returned by " + returnedUser;
            saveNotification(connection, claimNo, user.getUserId(), claimHandlerDto.getDecisionMakingAssignUserId(), message, URL);
            saveClaimSpecialRemark(connection, user, claimNo, "Return to Decision Maker", remark);
            saveClaimProcessFlow(connection, claimNo, 0, "File Returned to Decision Maker", user.getUserId(), Utility.sysDateTime(), claimHandlerDto.getDecisionMakingAssignUserId());
            saveClaimsLogs(connection, claimNo, user, "File Returned by " + returnedUser, "Return to Decision Maker [".concat(claimHandlerDto.getDecisionMakingAssignUserId()).concat("]"));
            shiftNotificationOnAction(connection, claimNo, user);
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<ClaimsDto> viewPreviousClaim(String vehicleNo, Integer claimNo) throws Exception {
        List<ClaimsDto> claims = new ArrayList<>();
        try {
            List<ClaimsDto> claimsDtos = callCenterService.getClaimHistoryForPolicyRefNo(vehicleNo);
            for (ClaimsDto claimsDto : claimsDtos) {
                if (!claimNo.equals(claimsDto.getClaimNo())) {
                    claims.add(claimsDto);
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return claims;
    }

    @Override
    public BigDecimal getEngineerApprovedAmount(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimHandlerDao.getEngineerApprovedAmount(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public boolean checkClaimStatusByClaimNO(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimHandlerDao.checkClaimStatusByClaimNO(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void updateBulkClose(Integer claimNo, Integer days, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            bulkCloseDetailDao.deleteByClaimNo(connection, claimNo);
            BulkCloseDetailDto bulkCloseDetailDto = new BulkCloseDetailDto();
            bulkCloseDetailDto.setClaimNo(claimNo);
            bulkCloseDetailDto.setUserId(user.getUserId());
            bulkCloseDetailDto.setDays(days);
            bulkCloseDetailDto.setDateTime(Utility.sysDateTime());
            bulkCloseDetailDao.insert(connection, bulkCloseDetailDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public BulkCloseDetailDto getBulkCloseDetails(Integer claimNoToSearch) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return bulkCloseDetailDao.getBulkCloseDetails(connection, claimNoToSearch);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public String isAllMainPanelApproved(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimPanelAssignUserDao.checkAllMainPanelUserRejected(connection, claimNo) ? AppConstant.SUCCESS : AppConstant.FAIL;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }


    @Override
    public String getCalsheetStatus(Integer claimNo) throws Exception {
        Connection connection = null;
        String status = AppConstant.STRING_EMPTY;
        List<ClaimCalculationSheetMainDto> list = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            list = claimCalculationSheetMainDao.searchByClaimNoOrderByDesc(connection, claimNo);

            for (ClaimCalculationSheetMainDto dto : list) {
                if (AppConstant.CAL_SHEET_TYPE_ADVANCED.equals(dto.getCalSheetType()) && AppConstant.CAL_SHEET_VOUCHER_GENERATE.equals(dto.getStatus())) {
                    return AppConstant.ADVANCE_PAID;
                } else if (AppConstant.CAL_SHEET_PAYMENT_REJECTED.equals(dto.getStatus())) {
                    return AppConstant.CALSHEET_STATUS_REJECTED;
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return status;
    }

    @Override
    public void requestForAdvance(Integer claimNo, String remark, UserDto assignUser, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            if (null == assignUser) {
                assignUser = claimHandlerDao.getAdvanceApprovedUser(connection, claimNo);
            }
            claimHandlerDao.updateAdvanceRequest(connection, claimNo, assignUser, user, BigDecimal.ZERO);
            String URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(claimNo.toString()).concat("&P_TAB_INDEX=16");
            saveNotification(connection, claimNo, user.getUserId(), assignUser.getUserId(), "Claim File received for Advance Request", URL);
            saveClaimsLogs(connection, claimNo, user, "Request For Advance Amount", "Claim file Forwarded to " + assignUser.getUserId() + " for Advance amount Approval");
            saveClaimSpecialRemark(connection, user, claimNo, "Advance Approval Request", remark);
            saveClaimProcessFlow(connection, claimNo, 0, "Claim file Forwarded for Advance Amount Approval", user.getUserId(), Utility.sysDateTime(), assignUser.getUserId());
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void forwardForAdvance(Integer claimNo, Integer type, String remark, UserDto assignUser, UserDto user, BigDecimal advanceAmount) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            claimHandlerDao.updateAdvanceRequest(connection, claimNo, assignUser, user, advanceAmount);
            String message = "Claim File received for Advance Request";
            String URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(claimNo.toString()).concat("&P_TAB_INDEX=16");
            saveNotification(connection, claimNo, user.getUserId(), assignUser.getUserId(), message, URL);
            saveClaimsLogs(connection, claimNo, user, "Forward For Advance Amount", user.getUserId() + " has Forwarded file for " + assignUser.getUserId() + " For Advance Approval");
            saveClaimsLogs(connection, claimNo, user, "Advance Amount Changed", "Advance Amount - ".concat(String.valueOf(advanceAmount)));
            saveClaimProcessFlow(connection, claimNo, 0, "Claim File Forwarded for Advance Amount Approval", user.getUserId(), Utility.sysDateTime(), assignUser.getUserId());
            saveClaimSpecialRemark(connection, user, claimNo, "Advance Approval", remark);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void returnToClaimHandlerAdvance(Integer claimNo, String remark, UserDto sessionUser) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            String assignUser = claimHandlerDao.getAssignedClaimHandler(connection, claimNo);
            if (assignUser == null || assignUser.isEmpty()) {
                throw new Exception("Assigned User not Found");
            }
            claimHandlerDao.updateAdvanceRequest(connection, claimNo, null, sessionUser, BigDecimal.ZERO);
            String message = sessionUser.getUserId() + " has Returned the Advance Request";
            String URL = "/ClaimHandlerController/viewEdit?P_N_CLIM_NO=".concat(claimNo.toString()).concat("&P_TAB_INDEX=16");
            saveNotification(connection, claimNo, sessionUser.getUserId(), assignUser, message, URL);
            saveClaimsLogs(connection, claimNo, sessionUser, "Advance Request Returned To Claim Handler", "Request For Advance, Claim No:[" + claimNo + "] has been return to Claim Handler");
            saveClaimsLogs(connection, claimNo, sessionUser, "Advance Amount Changed", "Advance Amount - ".concat(String.valueOf(BigDecimal.ZERO)));
            saveClaimProcessFlow(connection, claimNo, 0, "Advance Request Returned to Claim Handler", sessionUser.getUserId(), Utility.sysDateTime(), assignUser);
            saveClaimSpecialRemark(connection, sessionUser, claimNo, "Advance Approval Return", remark);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void ApproveAdvance(Integer claimNo, BigDecimal advance, String remark, UserDto sessionUser) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            String assignedClaimHandler = claimHandlerDao.getAssignedClaimHandler(connection, claimNo);
            if (null == assignedClaimHandler || assignedClaimHandler.isEmpty()) {
                throw new Exception("Assigned User Not Found");
            }
            claimHandlerDao.approveAdvance(connection, claimNo, advance, sessionUser);
            String message = "Advance Amount Approved by " + sessionUser.getUserId();
            String URL = "/ClaimHandlerController/viewEdit?P_N_CLIM_NO=".concat(claimNo.toString()).concat("&P_TAB_INDEX=16");
            saveNotification(connection, claimNo, sessionUser.getUserId(), assignedClaimHandler, message, URL);
            saveClaimsLogs(connection, claimNo, sessionUser, "Advance Amount Approved", "Advance Amount has been Approved by " + sessionUser.getUserId());
            saveClaimsLogs(connection, claimNo, sessionUser, "Advance Amount Approved", "Approved Advance Amount - ".concat(String.valueOf(advance)));
            saveClaimProcessFlow(connection, claimNo, 0, "Advance Amount Approved", sessionUser.getUserId(), Utility.sysDateTime(), assignedClaimHandler);
            saveClaimSpecialRemark(connection, sessionUser, claimNo, "Advance Amount Approved", remark);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void recallAdvance(Integer claimNo, String remark, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            String assignUser = claimHandlerDao.getAdvanceApprovalAssignUser(connection, claimNo);
            claimHandlerDao.recallAdvance(connection, claimNo, user);
            String message = "Advance Request for Claim " + claimNo + " has been Recalled";
            String URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(claimNo.toString()).concat("&P_TAB_INDEX=16");
            if (null != assignUser && !assignUser.isEmpty()) {
                saveNotification(connection, claimNo, user.getUserId(), assignUser, message, URL);
            }
            saveClaimsLogs(connection, claimNo, user, "Advance Request Recalled", "Advance Request assigned for " + assignUser + " for the Claim File " + claimNo + " has been Recalled by " + user.getUserId());
            saveClaimProcessFlow(connection, claimNo, 0, "Advance Request has been Recalled", user.getUserId(), Utility.sysDateTime(), assignUser);
            saveSpecialRemark(connection, user, claimNo, "Advance request Recalled", remark);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public DataGridDto getAdvanceForwardedList(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String user) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = claimHandlerDao.getAdvanceForwardList(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, user);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public void returnAdvance(Integer claimNo, String remark, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            UserDto assignUser = claimHandlerDao.getAdvanceForwardedUser(connection, claimNo);
            claimHandlerDao.recallAdvance(connection, claimNo, assignUser);
            String message = "Advance Request for Claim " + claimNo + " has been Returned";
            String URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(claimNo.toString()).concat("&P_TAB_INDEX=16");
            if (null != assignUser && !assignUser.getUserId().isEmpty()) {
                saveNotification(connection, claimNo, user.getUserId(), assignUser.getUserId(), message, URL);
                saveClaimProcessFlow(connection, claimNo, 0, "Advance Request has been Returned", user.getUserId(), Utility.sysDateTime(), assignUser.getUserId());
            }
            saveClaimsLogs(connection, claimNo, user, "Advance Request Returned", "Advance Request assigned for " + user.getUserId() + " for the Claim File " + claimNo + " has been Returned");
            saveSpecialRemark(connection, user, claimNo, "Advance request Returned", remark);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public boolean forwardToEngineer(Integer claimNo, String rteUser, UserDto user, String remark) throws Exception {
        Connection connection = null;
        boolean isForward = false;
        try {
            String URL = AppConstant.BILL_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(String.valueOf(claimNo));
            String forwardedDepartment = AppConstant.STRING_EMPTY;
            connection = getJDBCConnection();
            isForward = claimHandlerDao.forwardToEngineer(connection, claimNo, rteUser);
            if (isForward) {
                rtePendingClaimDetailDao.savePendingJobs(connection, claimNo, user.getAccessUserType(), user.getUserId());
                switch (user.getAccessUserType()) {
                    case 27:
                        forwardedDepartment = "Spare Parts Coordinator";
                        break;
                    case 28:
                        forwardedDepartment = "Bill Checking Team";
                        break;
                }
                saveClaimsLogs(connection, claimNo, user, "Claim File Forwarded", "Claim File : " + claimNo + " has been Forwarded to Engineer : " + rteUser);
                saveSpecialRemark(connection, user, claimNo, "Claim File Forwarded to Engineer", remark);
                saveNotification(connection, claimNo, user.getUserId(), rteUser, "Claim File Received From " + forwardedDepartment + " For the Special Comment Approval", URL, "#FFFFBF");
                saveClaimProcessFlow(connection, claimNo, 0, "Forwarded to Engineer", user.getUserId(), Utility.sysDateTime(), rteUser, AppConstant.YES);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return isForward;
    }

    @Override
    public boolean recallFromForwardedEngineer(Integer claimNo, UserDto user, String remark) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            String URL = AppConstant.BILL_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(String.valueOf(claimNo));
            ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);
            if (!claimHandlerDto.getForwardedEngineer().isEmpty()) {
                if (!motorEngineerDetailsDao.isInspectionPending(connection, claimNo, AppConstant.ZERO_INT)) {
                    if (claimHandlerDao.recallFromForwardedEngineer(connection, claimNo, claimHandlerDto.getOldEngineerClaimStatus())) {
                        rtePendingClaimDetailDao.removePendingJobs(connection, claimNo);
                    }
                }
                saveNotification(connection, claimNo, user.getUserId(), claimHandlerDto.getForwardedEngineer(), "Claim File : " + claimNo + " has been Recalled by " + user.getUserId(), URL, "#FFFFBF");
                saveSpecialRemark(connection, user, claimNo, "Recall Claim File From Engineer", remark);
                saveClaimsLogs(connection, claimNo, user, "Claim File Recalled", "Claim File : " + claimNo + " has been Recalled from Engineer : " + claimHandlerDto.getOldEngineerClaimStatus());
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return false;
    }

    @Override
    public RtePendingClaimsDto checkRtePendingDetails(Integer claimNoToSearch) throws Exception {
        Connection connection = null;
        RtePendingClaimsDto rtePendingClaimsDto = null;
        try {
            connection = getJDBCConnection();
            if (rtePendingClaimDetailDao.checkIfRteJobsPending(connection, claimNoToSearch)) {
                rtePendingClaimsDto = rtePendingClaimDetailDao.checkRtePendingJobs(connection, claimNoToSearch);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return rtePendingClaimsDto;
    }

    @Override
    public boolean returnClaimByEngineer(Integer claimNo, String assignUser, UserDto user, String remark) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            String URL = AppConstant.BILL_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(String.valueOf(claimNo));
            ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);
            if (!motorEngineerDetailsDao.isInspectionPending(connection, claimNo, AppConstant.ZERO_INT)) {
                if (claimHandlerDao.recallFromForwardedEngineer(connection, claimNo, claimHandlerDto.getOldEngineerClaimStatus())) {
                    rtePendingClaimDetailDao.removePendingJobs(connection, claimNo);
                    saveNotification(connection, claimNo, user.getUserId(), assignUser, "Claim File Received From Engineering User With Special Comment", URL, "#FFFFBF");
                    saveSpecialRemark(connection, user, claimNo, "Return Claim File by Engineer", remark);
                    saveClaimsLogs(connection, claimNo, user, "Claim File Returned By Engineer", "Claim File : " + claimNo + " has been Returned by Engineer : " + user.getUserId());
                    saveClaimProcessFlow(connection, claimNo, 0, "Returned By Engineer", user.getUserId(), Utility.sysDateTime(), assignUser, AppConstant.YES);
                    return true;
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return false;
    }

    @Override
    public DataGridDto getClaimsForEngineerDataGrid(List<FieldParameterDto> parameterList, int drawRendomId, int start, int length, String columnOrder, String orderColumnName, String fromDate, String toDate, boolean equals) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = claimHandlerDao.getClaimsForEngineerDataGrid(connection, parameterList, drawRendomId, start, length, columnOrder, orderColumnName, fromDate, toDate, equals);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public boolean isForwardedToEngineer(Integer claimNo, String v_usrid) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimHandlerDao.isClaimForwardedToEngineer(connection, claimNo, v_usrid);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public DataGridDto getMainPanelUsersGrid(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String columnOrder, String orderColumnName) throws Exception {
        DataGridDto dataGridDto;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = claimClaimPanelUserDao.getMainPanelDataGridDto(connection, parameterList, drawRandomId, start, length, columnOrder, orderColumnName);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public ClaimPanelDto searchPanelDecision(Integer claimNo) throws Exception {
        Connection connection = null;
        List<PanelDecisionDto> panelDecisionDtos;
        List<PanelDecisionDto> currentPanelDecision;
        ClaimPanelDto claimPanelDto;
        try {
            connection = getJDBCConnection();
            claimPanelDto = claimHandlerDao.populatePanelStatus(connection, claimNo);
            currentPanelDecision = claimPanelAssignUserDao.getCurrentPanelDecision(connection, claimNo);
            panelDecisionDtos = mainPanelAssignHstDao.getPanelDecisionHistory(connection, claimNo);
            panelDecisionDtos.addAll(currentPanelDecision);
            claimPanelDto.setUserDtos(panelDecisionDtos);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimPanelDto;
    }

    @Override
    public String getClaimRejectedUser(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimPanelAssignUserDao.getRejectedUser(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public boolean isApprovelPending(Integer claimNoToSearch, UserDto sessionUser) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimPanelAssignUserDao.isPendingApproval(connection, claimNoToSearch, sessionUser.getUserId());
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public boolean isForwardedToMainPanel(Integer claimNoToSearch) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimPanelAssignUserDao.isMainPanelAssigned(connection, claimNoToSearch);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public String getDecisionMakingAssignUser(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimHandlerDao.getAssignedDecisionMaker(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<PanelMemberListDto> getPanelMembers(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimPanelAssignUserDao.getPanelMembersForClaim(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void updatePanelUsers(Integer claimNo, String userList, String removedUserList, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            String[] splitUserList = userList.split(",");
            String[] splitRemovedUserList = removedUserList.split(",");
            String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
            if (!userList.isEmpty()) {
                for (String name : splitUserList) {
                    claimPanelAssignUserDao.updatePanelMembers(connection, claimNo, name, user.getUserId(), true);
                    saveNotification(connection, claimNo, user.getUserId(), name, "You have received file to make a panel decision", URL);
                    saveClaimsLogs(connection, claimNo, user, "Main Panel User Update", name + " was Assigned to Main Panel");
                }
            }
            if (!removedUserList.isEmpty()) {
                for (String name : splitRemovedUserList) {
                    claimPanelAssignUserDao.updatePanelMembers(connection, claimNo, name, user.getUserId(), false);
                    saveClaimsLogs(connection, claimNo, user, "Main Panel User Update", name + " was Removed from Main Panel");
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<UserDto> getUSersByAccessUsrType(Integer accessUsrType) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return getUsersByAccessUserType(connection, accessUsrType);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }


    private void processRepudiatedClaim(Connection connection, ClaimHandlerDto claimHandlerDto, String reopenType, UserDto user) throws Exception {
        try {
            String assingUser;
            if (claimHandlerDto.getIsProvideOffer().equalsIgnoreCase(AppConstant.YES)) {
                assingUser = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_OFFER_TEAM_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimHandlerDto.getClaimNo());
            } else {
                assingUser = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimHandlerDto.getClaimNo());
            }
            if (null != claimHandlerDto.getAssignUserId() && !claimHandlerDto.getAssignUserId().isEmpty()) {
                assingUser = claimHandlerDto.getAssignUserId();
            } else {
                ClaimHandlerDto claimUpdate = new ClaimHandlerDto();
                claimUpdate.setAssignUserId(assingUser);
                claimUpdate.setLiabilityAprvAssignUser(assingUser);
                claimUpdate.setAssignStatus(AppConstant.LIABILITY_VALUE);
                claimUpdate.setLiabilityAprvStatus(AppConstant.LIABILITY_VALUE);
                claimUpdate.setAssignDateTime(Utility.sysDateTime());
                claimUpdate.setLiabilityAprvAssignDateTime(Utility.sysDateTime());
                claimUpdate.setClaimNo(claimHandlerDto.getClaimNo());
                claimHandlerDao.updateClaimHandlerReopen(connection, claimUpdate);
            }

            if (AppConstant.REOPEN_TYPE_NORMAL.equals(reopenType) && AppConstant.CLAIM_REPUDIATED == claimHandlerDto.getClaimStatus()) {
                claimHandlerDao.updateClaimStatus(connection, claimHandlerDto.getClaimNo(), AppConstant.LIABILITY_PENDING);
            }

            if (AppConstant.CLAIM_REPUDIATED == claimHandlerDto.getClaimStatus()) {
                saveNotification(connection, claimHandlerDto.getClaimNo(), user.getUserId(), assingUser, "Repudiated Claim No."
                        .concat(claimHandlerDto.getClaimNo().toString()).concat(" is re-opened as ").concat("EX".equals(reopenType) ? "an Ex-gratia !" : "a normal"), AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimHandlerDto.getClaimNo())).concat("&P_TAB_INDEX=").concat(String.valueOf(15)));
            } else {
                saveNotification(connection, claimHandlerDto.getClaimNo(), user.getUserId(), assingUser, "Claim No."
                        .concat(claimHandlerDto.getClaimNo().toString()).concat(" is re-opened as ").concat("EX".equals(reopenType) ? "an Ex-gratia !" : "a normal"), AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimHandlerDto.getClaimNo())).concat("&P_TAB_INDEX=").concat(String.valueOf(15)));
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    public void saveConfirmationLogs(Connection connection, Integer claimNo, UserDto user, BigDecimal outstandingPremium, boolean isCancelledPolicy) throws Exception {
        boolean hasOutstandingPremium = outstandingPremium.compareTo(BigDecimal.ZERO) > 0;
        try {
            if (isCancelledPolicy && hasOutstandingPremium) {
                saveClaimsLogs(connection, claimNo, user, "Cancelled Policy and Premium Outstanding Confirmation Alert", "Marked As Ok to \"This is a cancelled policy and has Rs." + outstandingPremium + " of outstanding premium amount\" alert");
            } else if (isCancelledPolicy) {
                saveClaimsLogs(connection, claimNo, user, "Cancelled Policy Confirmation Alert", "Marked As Ok to \"This is a cancelled policy\" alert");
            } else if (hasOutstandingPremium) {
                saveClaimsLogs(connection, claimNo, user, "Premium Outstanding Confirmation Alert", "Marked As Ok to \"This policy has Rs." + outstandingPremium + " of outstanding premium amount\" alert");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    public boolean isClaimHandlerDepartment(UserDto user) {
        boolean isClaimHandlerDepartment = false;
        try {
            isClaimHandlerDepartment = switch (user.getAccessUserType()) {
                case 38, 40, 41, 42, 43, 45, 46, 47, 48, 49, 55, 56, 60, 62, 63, 68 -> true;
                default -> false;
            };
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return isClaimHandlerDepartment;
    }

    private void saveBranchRemark(Connection connection, String remark, int claimNo, String inpUser) {
        try {
            SpecialRemarkDto specialRemarkDto = new SpecialRemarkDto();
            specialRemarkDto.setClaimNo(claimNo);
            specialRemarkDto.setDepartmentId(AppConstant.BRANCH_DEPARTMENT_ID);
            specialRemarkDto.setSectionName(AppConstant.BRANCH_REMARK);
            specialRemarkDto.setInputUserId(inpUser);
            specialRemarkDto.setInputDateTime(Utility.sysDateTime());
            specialRemarkDto.setRemark(remark);
            specialRemarkDao.insertMaster(connection, specialRemarkDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    @Override
    public List<ClaimRepudiatedLetterTypeDto> getActiveRejectionReasons(String activeStatus) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimHandlerDao.getActiveRejectionReasons(connection, activeStatus);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void attachRejectionFileOnOtherSelect(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            claimHandlerDao.attachRejectionFileOnOtherSelect(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public int getRejectionRefNo(Integer claimNo, Integer rejectoinDocTypeId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimHandlerDao.getRejectionRefNo(connection, claimNo, rejectoinDocTypeId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private void sendOfferSms(Connection connection, ClaimsDto claim, UserDto user) throws Exception {
        OnSiteInspectionDetailsDto onsiteOfferDetails = onSiteInspectionDetailsMeDao.getOnsiteOfferDetails(connection, claim.getClaimNo());
        DesktopInspectionDetailsDto desktopOfferDetails = desktopInspectionDetailsMeDao.getDesktopOfferDetails(connection, claim.getClaimNo());
        if (null != onsiteOfferDetails || null != desktopOfferDetails) {
            BigDecimal offerAmount = null != onsiteOfferDetails ? onsiteOfferDetails.getPayableAmount() : desktopOfferDetails.getPayableAmount();

            List<String> smsParameterList = new ArrayList<>();
            smsParameterList.add(claim.getPolicyDto().getVehicleNumber());
            smsParameterList.add(String.valueOf(claim.getPolicyDto().getCustName()));
            smsParameterList.add(String.valueOf(claim.getAccidDate()));
            smsParameterList.add(String.valueOf(offerAmount));
            sendSmsMessage(connection, AppConstant.OFFER_SMS_TO_AGENT, smsParameterList, claim.getPolicyDto().getPolicySellingAgentDetailsDto().getContactNo(), claim.getPolicyDto().getPolicyChannelType(), user, claim.getClaimNo(), AppConstant.SEND_TO_AGENT);
            sendSmsMessage(connection, AppConstant.OFFER_SMS_TO_CUSTOMER, smsParameterList, claim.getPolicyDto().getCustMobileNo(), claim.getPolicyDto().getPolicyChannelType(), user, claim.getClaimNo(), AppConstant.SEND_TO_CUSTOMER);
        }
    }

    @Override
    public BigDecimal getReserveAmount(Integer claimNo) throws Exception {
        Connection connection = null;
        BigDecimal reserveAmount = BigDecimal.ZERO;
        try {
            connection = getJDBCConnection();
            reserveAmount = claimHandlerDao.getReserveAmountByClaimNo(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return reserveAmount;
    }
}
