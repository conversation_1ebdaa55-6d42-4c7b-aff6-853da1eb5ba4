package com.misyn.mcms.claim.dao.impl;


import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.ReserveAdjustmentDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;


public class ReserveAdjustmentDaoImpl extends AbstractBaseDao<ReserveAdjustmentDaoImpl> implements ReserveAdjustmentDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReserveAdjustmentDaoImpl.class);

    @Override
    public boolean isAlreadySaved(Connection conn, Integer claimReserveAdjustmentId) throws SQLException {
        try (PreparedStatement ps = conn.prepareStatement(IS_ALREADY_SAVED)) {
            ps.setInt(1, claimReserveAdjustmentId);
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next();
            }
        } catch (SQLException e) {
            LOGGER.error("Error checking existing reserve adjustment", e);
            throw e;
        }
    }

    @Override
    public void insert(Connection conn, ClaimReserveAdjustmentTypeDto dto) throws SQLException {
        try {
            ClaimReserveAdjustmentTypeDto result = insertMaster(conn, dto);
            if (result == null) {
                throw new SQLException("Insert failed, no rows affected.");
            }
        } catch (Exception e) {
            LOGGER.error("Error inserting reserve adjustment via insert()", e);
            throw new SQLException(e);
        }
    }

    @Override
    public void update(Connection conn, ClaimReserveAdjustmentTypeDto dto) throws SQLException {
        try {
            ClaimReserveAdjustmentTypeDto result = updateMaster(conn, dto);
            if (result == null) {
                throw new SQLException("Update failed, no rows affected.");
            }
        } catch (Exception e) {
            LOGGER.error("Error updating reserve adjustment via update()", e);
            throw new SQLException(e);
        }
    }

    public List<ReservePeriodDto> getAllPeriods(Connection conn) throws Exception {
        List<ReservePeriodDto> periods = new ArrayList<>();

        try (PreparedStatement ps = conn.prepareStatement(GET_ALL_PERIOD_TYPE);
             ResultSet rs = ps.executeQuery()) {
            while (rs.next()) {
                ReservePeriodDto dto = new ReservePeriodDto();
                dto.setPeriodId(rs.getInt("period_id"));
                dto.setMonthsCount(rs.getInt("months_count"));
                dto.setLabel(rs.getString("label"));
                dto.setRecordStatus(rs.getString("record_status"));
                periods.add(dto);
            }
        }
        return periods;
    }

    @Override
    public DataGridDto getReserveAdjustmentDataGridDto(
            Connection conn,
            List<FieldParameterDto> parameterList,
            int drawRandomId,
            int start,
            int length,
            String orderType,
            String orderField,
            String fromDate,
            String toDate) throws Exception {

        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List<ClaimReserveAdjustmentTypeDto> scheduleList = new ArrayList<>(200);

        PreparedStatement ps = null;

        String SQL_SEARCH = formatSQL(parameterList).toString();
        String[] orderFields = {orderField};
        Object[] instances = {AppConstant.PRIORITY_HIGH, AppConstant.PRIORITY_NORMAL};
        final String SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t1.input_date_time BETWEEN '" + fromDate + "' AND '" + toDate + "'");
        }

        final String SEL_SQL = SQL_SELECT_BASE.concat(SQL_SEARCH);
        final String COUNT_SQL = SQL_COUNT_BASE.concat(SQL_SEARCH);

        List<ReservePeriodDto> allPeriods = getAllPeriods(conn);
        List<Integer> allPeriodIds = new ArrayList<>();
        Map<Integer, String> periodIdToLabel = new HashMap<>();
        for (ReservePeriodDto p : allPeriods) {
            allPeriodIds.add(p.getPeriodId());
            periodIdToLabel.put(p.getPeriodId(), p.getLabel());
        }

        try {
            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
            }

            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    ClaimReserveAdjustmentTypeDto dto = new ClaimReserveAdjustmentTypeDto();
                    dto.setClaimReserveAdjustmentId(rs.getInt("claim_reserve_adjustment_id"));
                    dto.setPeriodId(rs.getInt("period_id"));
                    dto.setCategoryId(rs.getInt("category_id"));
                    dto.setAmountMin(rs.getBigDecimal("amount_min"));
                    dto.setAmountMax(rs.getBigDecimal("amount_max"));
                    dto.setAmountCurrency(rs.getString("amount_currency"));
                    dto.setAmount(rs.getString("amount"));
                    dto.setRecordStatus(rs.getString("record_status"));
                    dto.setInputDateTime(rs.getString("input_date_time"));
                    dto.setInputUser(rs.getString("input_user"));
                    dto.setLastModifiedDateTime(rs.getString("last_modified_date_time"));
                    dto.setLastModifiedUser(rs.getString("last_modified_user"));
                    dto.setPeriodLabel(rs.getString("period_label"));
                    dto.setCategoryLabel(rs.getString("category_label"));
                    dto.setIndex(++index);
                    scheduleList.add(dto);
                }
            }

            Map<Integer, Map<Integer, ClaimReserveAdjustmentTypeDto>> catPeriodMap = new LinkedHashMap<>();
            Map<Integer, String> categoryIdToLabel = new LinkedHashMap<>();
            for (ClaimReserveAdjustmentTypeDto dto : scheduleList) {
                catPeriodMap
                        .computeIfAbsent(dto.getCategoryId(), k -> new HashMap<>())
                        .put(dto.getPeriodId(), dto);
                categoryIdToLabel.put(dto.getCategoryId(), dto.getCategoryLabel());
            }

            List<ClaimReserveCategoryDto> categoryList = new ArrayList<>();
            for (Map.Entry<Integer, String> catEntry : categoryIdToLabel.entrySet()) {
                int catId = catEntry.getKey();
                String catLabel = catEntry.getValue();

                ClaimReserveCategoryDto catDto = new ClaimReserveCategoryDto();

                ClaimReserveAdjustmentTypeDto catSource = scheduleList.stream()
                        .filter(x -> x.getCategoryId() == catId)
                        .findFirst()
                        .orElse(null);

                if (catSource != null) {
                    catDto.setAmountMin(catSource.getAmountMin());
                    catDto.setAmountMax(catSource.getAmountMax());
                    catDto.setCategoryId(catSource.getCategoryId());
                    catDto.setCategoryLabel(catSource.getCategoryLabel());
                }

                List<ClaimReserveAdjustmentTypeDto> periodDtos = new ArrayList<>();
                Map<Integer, ClaimReserveAdjustmentTypeDto> periodMap = catPeriodMap.getOrDefault(catId, Collections.emptyMap());

                for (Integer periodId : allPeriodIds) {
                    ClaimReserveAdjustmentTypeDto periodDto = periodMap.get(periodId);
                    if (periodDto != null) {
                        periodDtos.add(periodDto);
                    } else {
                        ClaimReserveAdjustmentTypeDto emptyDto = new ClaimReserveAdjustmentTypeDto();
                        emptyDto.setPeriodId(periodId);
                        emptyDto.setCategoryId(catId);
                        emptyDto.setPeriodLabel(periodIdToLabel.get(periodId));
                        emptyDto.setCategoryLabel(catLabel);
                        emptyDto.setAmount(AppConstant.EMPTY_STRING);
                        periodDtos.add(emptyDto);
                    }
                }
                catDto.setPeriods(periodDtos);
                categoryList.add(catDto);
            }

            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(new ArrayList<Object>(categoryList));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (ps != null) ps.close();
        }
        return dataGridDTO;
    }

    @Override
    public boolean isActiveRecordExists(Connection conn, int periodId, int categoryId) {
        try (
             PreparedStatement ps = conn.prepareStatement(IS_RESERVE_ADJUSTMENT_ACTIVE)) {
            ps.setInt(1, periodId);
            ps.setInt(2, categoryId);
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next();
            }
        } catch (SQLException e) {
            LOGGER.error("Error checking exists for periodId={} categoryId={}", periodId, categoryId, e);
            return false;
        }
    }

    @Override
    public List<ReserveCategoryDto> getReserveCategories(Connection conn) {
        List<ReserveCategoryDto> list = new ArrayList<>();
        try (
             PreparedStatement ps = conn.prepareStatement(RESERVE_CATEGORY_TYPE);
             ResultSet rs = ps.executeQuery()) {
            while (rs.next()) {
                ReserveCategoryDto dto = new ReserveCategoryDto();
                dto.setCategoryId(rs.getInt("category_id"));
                dto.setDescription(rs.getString("description"));
                list.add(dto);
            }
        } catch (SQLException e) {
            LOGGER.error("Error fetching reserve categories", e);
        }
        return list;
    }

    @Override
    public List<ReservePeriodDto> getReservePeriods(Connection conn) {
        List<ReservePeriodDto> list = new ArrayList<>();
        try (
             PreparedStatement ps = conn.prepareStatement(RESERVE_PERIOD_TYPE);
             ResultSet rs = ps.executeQuery()) {
            while (rs.next()) {
                ReservePeriodDto dto = new ReservePeriodDto();
                dto.setPeriodId(rs.getInt("period_id"));
                dto.setMonthsCount(rs.getInt("months_count"));
                dto.setLabel(rs.getString("label"));
                list.add(dto);
            }
        } catch (SQLException e) {
            LOGGER.error("Error fetching reserve periods", e);
        }
        return list;
    }

    @Override
    public ClaimReserveAdjustmentTypeDto insertMaster(Connection conn, ClaimReserveAdjustmentTypeDto dto) throws Exception {
        try (PreparedStatement ps = conn.prepareStatement(RESERVE_ADJUSTMENT_INSERT)) {
            int idx = 0;
            ps.setInt(++idx, dto.getPeriodId());
            ps.setInt(++idx, dto.getCategoryId());
            ps.setString(++idx, dto.getAmount());
            ps.setBigDecimal(++idx, dto.getAmountMin());
            ps.setBigDecimal(++idx, dto.getAmountMax());
            ps.setString(++idx, dto.getRecordStatus());
            ps.setString(++idx, Utility.sysDateTime());
            ps.setString(++idx, dto.getInputUser());
            ps.setString(++idx, Utility.sysDateTime());
            ps.setString(++idx, dto.getLastModifiedUser());

            if (ps.executeUpdate() > 0) {
                return dto;
            }
        } catch (SQLException e) {
            LOGGER.error("Error inserting reserve adjustment", e);
            throw e;
        }
        return null;
    }

    @Override
    public ClaimReserveAdjustmentTypeDto updateMaster(Connection conn, ClaimReserveAdjustmentTypeDto dto) throws Exception {
        try (PreparedStatement ps = conn.prepareStatement(RESERVE_ADJUSTMENT_UPDATE)) {
            int idx = 0;
            ps.setInt(++idx, dto.getPeriodId());
            ps.setInt(++idx, dto.getCategoryId());
//            ps.setBigDecimal(++idx, dto.getAmountMin());
            ps.setString(++idx, dto.getAmount());
//            if (dto.getAmountMax() != null) {
//                ps.setBigDecimal(++idx, dto.getAmountMax());
//            } else {
//                ps.setNull(++idx, java.sql.Types.DECIMAL);
//            }
            ps.setString(++idx, dto.getAmountCurrency());
            ps.setBigDecimal(++idx, dto.getMovingAcrAbove());
            ps.setBigDecimal(++idx, dto.getMovingAdvanceOrAcrAbove());
            ps.setBigDecimal(++idx, dto.getMovingAcrLessThan());
            ps.setBigDecimal(++idx, dto.getNonMoving());
            ps.setString(++idx, dto.getRecordStatus());
            ps.setString(++idx, Utility.sysDateTime());
            ps.setString(++idx, dto.getLastModifiedUser());
            ps.setInt(++idx, dto.getClaimReserveAdjustmentId());

            if (ps.executeUpdate() > 0) {
                return dto;
            }
        } catch (SQLException e) {
            LOGGER.error("Error updating reserve adjustment", e);
            throw e;
        }
        return null;
    }

    @Override
    public ClaimReserveAdjustmentTypeDto insertTemporary(Connection connection, ClaimReserveAdjustmentTypeDto dto) throws Exception {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public ClaimReserveAdjustmentTypeDto updateTemporary(Connection connection, ClaimReserveAdjustmentTypeDto dto) throws Exception {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public ClaimReserveAdjustmentTypeDto insertHistory(Connection connection, ClaimReserveAdjustmentTypeDto dto) throws Exception {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public ClaimReserveAdjustmentTypeDto searchMaster(Connection connection, Object id) throws Exception {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public List<ClaimReserveAdjustmentTypeDto> searchAll(Connection connection) throws Exception {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public ClaimReserveAdjustmentTypeDto searchTemporary(Connection connection, Object id) throws Exception {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        throw new UnsupportedOperationException("Not supported yet.");
    }
}
