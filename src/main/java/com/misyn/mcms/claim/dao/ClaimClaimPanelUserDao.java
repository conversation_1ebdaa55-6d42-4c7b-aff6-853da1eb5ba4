/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ClaimClaimPanelUserDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;

import java.sql.Connection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ClaimClaimPanelUserDao extends BaseDao<ClaimClaimPanelUserDto> {
    String CLAIM_PANEL_USER_INSERT = "INSERT INTO claim_claim_panel_user VALUES(0,?,?,?,?,?,?,?)";
    String CLAIM_PANEL_USER_UPDATE = "UPDATE claim_claim_panel_user SET V_USER_STATUS=?,D_INPUT_DATETIME=? WHERE N_ID=?";
    String CLAIM_PANEL_USER_SEARCH_ONE = "SELECT\n" +
            "t1.N_ID,\n" +
            "t1.V_USER_ID,\n" +
            "t1.N_PANEL_ID,\n" +
            "t1.V_USER_STATUS,\n" +
            "t1.V_INPUT_USER,\n" +
            "t1.D_INPUT_DATETIME,\n" +
            "t2.N_ID,\n" +
            "t2.V_PANEL_NAME\n" +
            "FROM\n" +
            "claim_claim_panel_user AS t1\n" +
            "INNER JOIN claim_claim_panel AS t2 ON t1.N_PANEL_ID = t2.N_ID\n" +
            "WHERE t1.N_ID = ?";
    String CLAIM_PANEL_USER_SEARCH_ALL = "SELECT * FROM claim_claim_panel_user";

    String GET_MAIN_PANEL_USERS_DATA_GRID = "SELECT\n" +
            "N_CLAIM_NO,\n" +
            "V_INPUT_USER,\n" +
            "MIN(D_INPUT_DATETIME) AS INPUT_DATETIME,\n" +
            "MAX(D_UPDATED_DATE_TIME) AS UPDATED_DATETIME,\n" +
            "COUNT(N_CLAIM_NO) AS PANEL_MEMBERS,\n" +
            "CASE \n" +
            "WHEN sum(CASE WHEN V_STATUS = 'D' THEN 2 ELSE 0 END) > 1 THEN 'D'\n" +
            "WHEN sum(CASE WHEN V_STATUS = 'P' THEN 1 ELSE 0 END) > 0 THEN 'P'\n" +
            "ELSE max(V_STATUS)\n" +
            "END AS V_STATUS\n" +
            "FROM\n" +
            "claim_panel_assign_user";

    String COUNT_MAIN_PANEL_USERS_DATA_GRID = "SELECT\n" +
            "COUNT(DISTINCT(N_CLAIM_NO)) AS cnt\n" +
            "FROM claim_panel_assign_user";

    String IS_ALREADY_IN_THE_PANEL = "SELECT 1 FROM claim_claim_panel_user WHERE V_USER_ID = ? AND N_PANEL_ID = ?";

    DataGridDto getDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField);

    DataGridDto getMainPanelDataGridDto(Connection connection, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String columnOrder, String orderColumnName) throws Exception;

    boolean isAlreadySavedInPanel(Connection connection, Integer panelId, String userId) throws Exception;
}
