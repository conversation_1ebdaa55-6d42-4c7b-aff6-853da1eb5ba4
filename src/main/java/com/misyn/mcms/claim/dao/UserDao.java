package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ListItemDto;
import com.misyn.mcms.claim.dto.UserDto;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.List;

public interface UserDao {

    String SELECT_FROM_USR_MST_WHERE_ACCESSUSRTYPE = "SELECT * FROM usr_mst WHERE n_accessusrtype=?";

    String SELECT_ALL_FROM_USR_MST_BY_USRID = "SELECT * FROM usr_mst WHERE v_usrid = ?";

    String SELCT_ASSESSOR_TYPE_BY_ASSESSOR_CODE = "SELECT assessor_type FROM usr_mst WHERE v_emp_no = ?";

    String SELECT_EMAIL_BY_USER_ID = "SELECT v_email FROM usr_mst WHERE v_usrid = ?";

    String SELECT_MEMBERS_FROM_TEAM_ID = "SELECT" +
            " n_usrcode," +
            " v_usrid," +
            " v_firstname," +
            " v_lastname" +
            " FROM usr_mst\n" +
            " WHERE N_TEAM_ID = ?";

    String SELECT_BRANCHUSERS_FROM_BRANCH_CODE = "SELECT v_usrid FROM usr_mst WHERE n_accessusrtype = 100 AND branch_code=?";

    String SELECT_NEED_TO_SEND_EMAIL_BY_USER_CODE = "SELECT need_to_send_email FROM usr_mst WHERE v_usrid = ?";

    String SELECT_AUTH_LEVEL_BY_USER_ID = "SELECT n_auth_level FROM usr_mst WHERE v_usrid = ?";

    String SELECT_PAYMENT_AUTH_LIMIT_BY_USER_NAME = "SELECT N_PAYMENT_AUTH_LIMIT FROM usr_mst WHERE v_usrid = ?";

    String SELECT_ACCESS_USER_TYPE_FROM_USER_ID = "SELECT n_accessusrtype FROM usr_mst WHERE v_usrid = ?";

    String GET_ENGINEERS_WITH_AUTH_LEVEL = "SELECT GROUP_CONCAT(DISTINCT v_usrid) as user_name, n_auth_level FROM usr_mst WHERE n_accessusrtype IN (?) AND v_usrstatus <> 'C'  GROUP BY n_auth_level";

    String GET_ENGINEERS_FOR_REASSIGN = "SELECT\n" +
            "GROUP_CONCAT(DISTINCT CONCAT(v_usrid, CONCAT(' - Level ', n_auth_level)) ORDER BY n_auth_level ASC) AS user_name_text,\n" +
            "GROUP_CONCAT(DISTINCT v_usrid ORDER BY n_auth_level ASC) AS user_name_value,\n" +
            "n_auth_level\n" +
            "FROM\n" +
            "usr_mst\n" +
            "WHERE\n" +
            "n_accessusrtype IN (accessUsrTypes)\n" +
            "AND\n" +
            "n_auth_level IN (auth_levels) AND v_usrstatus <> 'C'";

    String GET_ASSIGN_ENGINEER_LEVEL = "SELECT n_auth_level FROM usr_mst WHERE v_usrid = ?";

    String GET_TECHNICAL_CORDINATORS_BY_USER_STATUS = "SELECT v_usrid FROM usr_mst WHERE n_accessusrtype IN ( 104, 25 ) and v_usrstatus = 'A'";

    String SELECT_ALL_SPECIAL_TEAM_USERS = "SELECT v_usrid, CONCAT(v_firstname, ' ', v_lastname) AS full_name FROM usr_mst WHERE n_accessusrtype = 43";

    String SELECT_ALL_APPROVAL_USERS = "SELECT u.v_usrid, CONCAT(u.v_firstname, ' ', u.v_lastname) AS full_name " +
            "FROM usr_mst u " +
            "JOIN claim_assign_claim_handler c ON c.N_CLAIM_NO = ? " +
            "WHERE u.v_usrstatus = 'A' " +
            "AND c.N_RESERVE_AMOUNT BETWEEN u.approval_from_limit AND u.approval_to_limit AND n_accessusrtype = 42";

    List<UserDto> getUSersByAccessUsrType(Connection connection, Integer accessUsrType) throws Exception;

    UserDto getUserByUsrid(Connection connection, String usrid) throws Exception;

    String getAssessorType(Connection connection, String assessorCode);

    List<UserDto> getMembersForTeam(Connection connection, Integer teamId) throws Exception;

    List<UserDto> findBranchUsersByBranchCode(Connection connection, String branchCode) throws Exception;

    String getEmailByUserId(Connection connection, String assignUserId);

    boolean isNeedToSendEmailByUserId(Connection connection, String assignUserId);

    List<ListItemDto> fetchSpecialTeam(Connection connection) throws Exception;

    List<ListItemDto> fetchApprovalUserList(Connection connection, Integer claimNo) throws Exception;

    int getAuthLevelByUserId(Connection connection, String assignUserId);

    BigDecimal getPaymentAuthLevelByUserName(Connection connection, String assignUser) throws Exception;

    int getAccessUserTypeByUserId(Connection connection, String assignUser) throws Exception;

    List<String> getMotorEngineersByAuthLevels(Connection connection) throws Exception;

    List<String> getRteForReassign(Connection connection, String userName) throws Exception;

    List<String> getRteForReassign(Connection connection) throws Exception;

    int getAccessUsertype(Connection connection,String userId) throws Exception;

    List<String> getTechnicalCodinators(Connection connection) throws Exception;

    UserDto getUser(Connection connection, String username);

    UserDto userLoginValidate(Connection connection,String username);
}
