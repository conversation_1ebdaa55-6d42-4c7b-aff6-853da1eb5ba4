package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.BaseAbstract;
import com.misyn.mcms.claim.dao.InspectionDetailsDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.dto.motorengineer.MotorEngineerDetailsDto;
import com.misyn.mcms.claim.enums.AccidentStatus;
import com.misyn.mcms.claim.enums.ConditionType;
import com.misyn.mcms.claim.enums.SelectionType;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class InspectionDetailsDaoImpl extends BaseAbstract<InspectionDetailsDaoImpl> implements InspectionDetailsDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(InspectionDetailsDaoImpl.class);

    @Override
    public InspectionDetailsDto insertMaster(Connection connection, InspectionDetailsDto inspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_CLAIM_INSPECTION_INFO_MAIN);
            ps.setInt(++index, inspectionDetailsDto.getAssessorAllocationDto().getRefNo());
            ps.setString(++index, inspectionDetailsDto.getAssessorAllocationDto().getJobId());
            ps.setInt(++index, inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo());
            ps.setString(++index, inspectionDetailsDto.getMakeConfirm().getSelectionType());
            ps.setString(++index, inspectionDetailsDto.getModelConfirm().getSelectionType());
            ps.setString(++index, inspectionDetailsDto.getEngNoConfirm().getSelectionType());
            ps.setString(++index, inspectionDetailsDto.getChassisNo());
            ps.setString(++index, inspectionDetailsDto.getYearMakeConfirm().getSelectionType());
            ps.setString(++index, inspectionDetailsDto.getInspectDateTime());
            ps.setBigDecimal(++index, inspectionDetailsDto.getPav());
            ps.setString(++index, inspectionDetailsDto.getDamageDetails());
            ps.setString(++index, inspectionDetailsDto.getPad());
            ps.setString(++index, inspectionDetailsDto.getGenuineOfAccident().getAccidentStatus());
            ps.setString(++index, inspectionDetailsDto.getFirstStatementReq().getCondtionType());
            ps.setString(++index, inspectionDetailsDto.getFirstStatementReqReason());
            ps.setString(++index, inspectionDetailsDto.getInvestReq().getCondtionType());
            ps.setString(++index, inspectionDetailsDto.getAssessorSpecialRemark());
            ps.setInt(++index, inspectionDetailsDto.getInspectionDto().getInspectionId());
            ps.setInt(++index, inspectionDetailsDto.getJobType());
            ps.setString(++index, inspectionDetailsDto.getAssignedLocation());
            ps.setString(++index, inspectionDetailsDto.getCurrentLocation());
            ps.setString(++index, inspectionDetailsDto.getPlaceOfInspection());
            ps.setInt(++index, inspectionDetailsDto.getMileage());
            ps.setBigDecimal(++index, inspectionDetailsDto.getCostOfCall());
            ps.setBigDecimal(++index, inspectionDetailsDto.getOtherFee());
            ps.setBigDecimal(++index, inspectionDetailsDto.getTotalAssessorFee());
            ps.setString(++index, inspectionDetailsDto.getFeeDesc());
            ps.setInt(++index, inspectionDetailsDto.getRecordStatus());
            ps.setString(++index, inspectionDetailsDto.getInputUserId());
            ps.setString(++index, inspectionDetailsDto.getInputDatetime());
            ps.setString(++index, inspectionDetailsDto.getAssignRteUser());
            ps.setString(++index, inspectionDetailsDto.getAssignRteDatetime());
            ps.setString(++index, inspectionDetailsDto.getChassisNoConfirm().getSelectionType());
            ps.setInt(++index, inspectionDetailsDto.getNotCheckedReason());
            ps.setString(++index, inspectionDetailsDto.getIsVehicleAvailable());
            ps.setString(++index, inspectionDetailsDto.getApproveAssignRteUser());
            ps.setString(++index, inspectionDetailsDto.getApproverAssignRteDateTime());
            ps.setInt(++index, inspectionDetailsDto.getAssessorFeeDetailId());


            if (ps.executeUpdate() > 0) {
                return inspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public InspectionDetailsDto updateMaster(Connection connection, InspectionDetailsDto inspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_INSPECTION_DEATIL_MASTER);
            ps.setString(++index, inspectionDetailsDto.getAssessorAllocationDto().getJobId());
            ps.setInt(++index, inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo());
            ps.setString(++index, inspectionDetailsDto.getMakeConfirm().getSelectionType());
            ps.setString(++index, inspectionDetailsDto.getModelConfirm().getSelectionType());
            ps.setString(++index, inspectionDetailsDto.getEngNoConfirm().getSelectionType());
            ps.setString(++index, inspectionDetailsDto.getChassisNo());
            ps.setString(++index, inspectionDetailsDto.getYearMakeConfirm().getSelectionType());
            ps.setString(++index, inspectionDetailsDto.getInspectDateTime());
            ps.setBigDecimal(++index, inspectionDetailsDto.getPav());
            ps.setString(++index, inspectionDetailsDto.getDamageDetails());
            ps.setString(++index, inspectionDetailsDto.getPad());
            ps.setString(++index, inspectionDetailsDto.getGenuineOfAccident().getAccidentStatus());
            ps.setString(++index, inspectionDetailsDto.getFirstStatementReq().getCondtionType());
            ps.setString(++index, inspectionDetailsDto.getFirstStatementReqReason());
            ps.setString(++index, inspectionDetailsDto.getInvestReq().getCondtionType());
            ps.setString(++index, inspectionDetailsDto.getAssessorSpecialRemark());
            ps.setInt(++index, inspectionDetailsDto.getInspectionDto().getInspectionId());
            ps.setInt(++index, inspectionDetailsDto.getJobType());
            ps.setString(++index, inspectionDetailsDto.getAssignedLocation());
            ps.setString(++index, inspectionDetailsDto.getCurrentLocation());
            ps.setString(++index, inspectionDetailsDto.getPlaceOfInspection());
            ps.setInt(++index, inspectionDetailsDto.getMileage());
            ps.setBigDecimal(++index, inspectionDetailsDto.getCostOfCall());
            ps.setBigDecimal(++index, inspectionDetailsDto.getOtherFee());
            ps.setBigDecimal(++index, inspectionDetailsDto.getTotalAssessorFee());
            ps.setString(++index, inspectionDetailsDto.getFeeDesc());
            ps.setInt(++index, inspectionDetailsDto.getRecordStatus());
            ps.setString(++index, inspectionDetailsDto.getInputUserId());
            ps.setString(++index, inspectionDetailsDto.getInputDatetime());
            ps.setString(++index, inspectionDetailsDto.getAssignRteUser());
            ps.setString(++index, inspectionDetailsDto.getAssignRteDatetime());
            ps.setInt(++index, inspectionDetailsDto.getAssessorFeeDetailId());
            ps.setInt(++index, inspectionDetailsDto.getAssessorAllocationDto().getRefNo());

            if (ps.executeUpdate() > 0) {
                return inspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public InspectionDetailsDto insertTemporary(Connection connection, InspectionDetailsDto inspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public InspectionDetailsDto updateTemporary(Connection connection, InspectionDetailsDto inspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public InspectionDetailsDto insertHistory(Connection connection, InspectionDetailsDto inspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public InspectionDetailsDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_BY_REF_NO);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getInspctionDetalsDto(rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public InspectionDetailsDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<InspectionDetailsDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public DataGridDto getJobDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List jobList = new ArrayList(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();

        final String SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();


        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t3.assign_datetime BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");

        }

        final String SEL_SQL = SQL_SELECT_ALL_TO_GRID.concat(SQL_SEARCH).concat(SQL_ORDER);


        final String COUNT_SQL = SQL_SELECT_COUNT_TO_GRID.concat(SQL_SEARCH);
        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    InspectionDetailsGridDto gridDto = new InspectionDetailsGridDto();
                    gridDto.setRefNo(rs.getInt("t3.ref_no"));
                    gridDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
                    gridDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NO"));
                    gridDto.setJobNo(rs.getString("t3.job_id"));
                    gridDto.setIsOnlineInspection(rs.getString("t3.is_online_inspection"));
                    gridDto.setInspectionType(rs.getString("t2.inspection_type_desc"));
                    gridDto.setStatusId(rs.getInt("t3.record_status"));
                    gridDto.setPolRefNo(rs.getInt("t1.N_POL_REF_NO"));
                    gridDto.setAssignDateTime(Utility.getDate(rs.getString("t3.assign_datetime"), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
                    gridDto.setIntimationType(rs.getInt("t1.N_INTIMATION_TYPE"));
                    gridDto.setJobStatus(rs.getString("t4.v_status_desc"));

                    gridDto.setAssignRteUser(null == rs.getString("v_assign_rte_user") ? AppConstant.STRING_EMPTY : rs.getString("v_assign_rte_user"));
                    gridDto.setAssignRteDateTime(null == rs.getString("d_assign_rte_datetime") ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("d_assign_rte_datetime"), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));


                    long[] hours = Utility.getDayHoursMinSecondDifference(gridDto.getAssignDateTime(), Utility.sysDateTime(), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT);
                    long diffHours = hours[1];
                    long days = hours[0];
                    diffHours = (days * 24) + diffHours;
                    if (diffHours > 24 && diffHours <= 48) {
                        gridDto.setExcessType(1);
                    } else if (diffHours > 48) {
                        gridDto.setExcessType(2);
                    } else {
                        gridDto.setExcessType(0);
                    }
                    gridDto.setAssignUser(rs.getString("assign_assessor") != null ? rs.getString("assign_assessor") : AppConstant.STRING_EMPTY);
                    gridDto.setCcAssignedBy(rs.getString("t3.inp_userid"));
                    gridDto.setIndex(++index);
                    jobList.add(gridDto);
                }
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(jobList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;

    }

    @Override
    public DataGridDto getSubmittedInspectionDetailsGridDto(Connection conn, List<FieldParameterDto> parameterList,
                                                            int drawRandomId, int start, int length, String orderType,
                                                            String orderField, String fromDate, String toDate, UserDto user, String assignUser, String status) {

        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List jobList = new ArrayList(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        String[] orderFields = {orderField};
        Object[] instances = {AppConstant.PRIORITY_HIGH, AppConstant.PRIORITY_NORMAL};
        final String SQL_ORDER = orderByCases(start, length, orderType, "t2.V_PRIORITY", orderFields, instances).toString();

        //Record Status --> 23 REJECTED | 4 REASSIGNED | 29 ASSIGNED
        //Inspection Id --> 8 Desktop Inspection
        if (null != assignUser && !assignUser.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat("AND ((t0.v_assign_rte_user = '" + assignUser + "'AND t0.n_record_status <> 80) OR (t0.v_approve_assign_rte_user = '" + assignUser + "'  AND t0.n_record_status = 80))");
        }
        SQL_SEARCH = SQL_SEARCH.concat(" AND (t1.record_status NOT IN (23, 4, 29) OR (t1.record_status = 29 AND t1.insepction_id = 8))");
        if (!status.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t1.record_status=" + status);
        }
        SQL_SEARCH = SQL_SEARCH.concat(" AND t0.is_vehicle_available <> 'N'");

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t1.assign_datetime BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

        final String SEL_SQL = SQL_SELECT_ALL_SUBMITTED_TO_GRID.concat(SQL_SEARCH).concat(SQL_ORDER);

        final String COUNT_SQL = SQL_SELECT_SUBMITTED_COUNT_TO_GRID.concat(SQL_SEARCH);
        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    InspectionDetailsGridDto gridDto = new InspectionDetailsGridDto();
                    gridDto.setRefNo(rs.getInt("t1.ref_no"));
                    gridDto.setClaimNo(rs.getInt("t2.N_CLIM_NO"));
                    gridDto.setVehicleNo(rs.getString("t2.V_VEHICLE_NO"));
                    gridDto.setJobNo(rs.getString("t1.job_id"));
                    gridDto.setInspectionType(rs.getString("t3.inspection_type_desc"));
                    gridDto.setInspectionTypeId(rs.getString("t1.insepction_id"));
                    gridDto.setStatusId(rs.getInt("t1.record_status"));
                    gridDto.setPolRefNo(rs.getInt("t2.N_POL_REF_NO"));
                    gridDto.setPriority(null == rs.getString("t2.V_PRIORITY") || rs.getString("t2.V_PRIORITY").isEmpty() ? AppConstant.PRIORITY_NORMAL : rs.getString("t2.V_PRIORITY"));
                    gridDto.setAssignUser(rs.getString("t0.v_assign_rte_user") == null ? AppConstant.STRING_EMPTY : rs.getString("t0.v_assign_rte_user"));
                    gridDto.setAssignDateTime(rs.getString("t1.assign_datetime") == null
                            ? ""
                            : Utility.getDate(rs.getString("t1.assign_datetime"), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
                    gridDto.setIntimationType(rs.getInt("t2.N_INTIMATION_TYPE"));
                    gridDto.setJobStatus(rs.getString("t4.v_status_desc"));
                    gridDto.setEstimationApprStatus(rs.getString("t0.v_ass_esti_apr_status"));
                    gridDto.setAssFeeAprStatus(rs.getString("t0.v_ass_fee_apr_status"));

                    gridDto.setClaimStatus(rs.getInt("t2.N_CLAIM_STATUS"));

                    gridDto.setAssignToRteDateTime(rs.getString("t0.d_assign_rte_datetime") == null
                            ? ""
                            : Utility.getDate(rs.getString("t0.d_assign_rte_datetime"), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));

                    long[] hours;
                    if (rs.getInt("t1.insepction_id") == 8) { //8 --> Desktop Inspection
                        hours = Utility.getDayHoursMinSecondDifference(gridDto.getAssignDateTime(), Utility.sysDateTime(), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT);
                    } else {
                        hours = Utility.getDayHoursMinSecondDifference(gridDto.getAssignToRteDateTime(), Utility.sysDateTime(), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT);
                    }

                    long diffHours = hours[1];
                    long days = hours[0];
                    diffHours = (days * 24) + diffHours;
                    if (diffHours > 24 && diffHours <= 48) {
                        gridDto.setExcessType(1);
                    } else if (diffHours > 48) {
                        gridDto.setExcessType(2);
                    } else {
                        gridDto.setExcessType(0);
                    }
                    gridDto.setIndex(++index);
                    jobList.add(gridDto);
                }
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(jobList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;

    }

    @Override
    public DataGridDto getFwdDesktopInspectionDetailsGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, UserDto user, int recordStatus) {
        String userId = user.getUserId();
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List jobList = new ArrayList(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        String[] orderFields = {orderField};
        Object[] instances = {AppConstant.PRIORITY_HIGH, AppConstant.PRIORITY_NORMAL};
        final String SQL_ORDER = orderByCases(start, length, orderType, "t1.priority", orderFields, instances).toString();

        //Record Status --> 23 REJECTED | 4 REASSIGNED | 29 ASSIGNED || 33 Forward Desktop
        //Inspection Id --> 8 Desktop Inspection
//        SQL_SEARCH = SQL_SEARCH.concat("AND (t1.record_status NOT IN (23, 4, 29) OR (t1.record_status = 29 AND t1.insepction_id = 8))");


        if (recordStatus == 9) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND (t1.record_status IN (9) AND t0.v_fwd_tc_desktop_user = '" + userId + "'");
            SQL_SEARCH = SQL_SEARCH.concat(" OR t1.record_status IN (34) AND t1.inp_userid = '" + userId + "')");
        } else if (recordStatus == 0) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND (t1.record_status IN (33,34,9,29) AND t0.v_fwd_tc_desktop_user = '" + userId + "'");
            SQL_SEARCH = SQL_SEARCH.concat(" OR t1.record_status IN (34) AND t1.inp_userid = '" + userId + "')");
        } else if (recordStatus == 29) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t1.insepction_id=8 ");
        } else {
            SQL_SEARCH = SQL_SEARCH.concat(" AND (t1.record_status IN (33) AND t0.v_fwd_tc_desktop_user = '" + userId + "'");
            SQL_SEARCH = SQL_SEARCH.concat(" OR t1.record_status IN (34) AND t1.inp_userid = '" + userId + "')");
        }

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t0.d_fwd_tc_desktop_datetime BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

        final String SEL_SQL = SQL_SELECT_ALL_FWD_DESKTOP_TO_GRID.concat(SQL_SEARCH).concat(SQL_ORDER);

        final String COUNT_SQL = SQL_SELECT_SUBMITTED_COUNT_TO_GRID.concat(SQL_SEARCH);
        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    InspectionDetailsGridDto gridDto = new InspectionDetailsGridDto();
                    gridDto.setRefNo(rs.getInt("t1.ref_no"));
                    gridDto.setPriority(null == rs.getString("t1.priority") || rs.getString("t1.priority").isEmpty() ? AppConstant.PRIORITY_NORMAL : rs.getString("t1.priority"));
                    gridDto.setClaimNo(rs.getInt("t2.N_CLIM_NO"));
                    gridDto.setVehicleNo(rs.getString("t2.V_VEHICLE_NO"));
                    gridDto.setJobNo(rs.getString("t1.job_id"));
                    gridDto.setInspectionType(rs.getString("t3.inspection_type_desc"));
                    gridDto.setStatusId(rs.getInt("t1.record_status"));
                    gridDto.setPolRefNo(rs.getInt("t2.N_POL_REF_NO"));
                    if (34 == gridDto.getStatusId()) {
                        gridDto.setAssignDateTime(rs.getString("t1.assign_datetime") == null
                                ? ""
                                : Utility.getDate(rs.getString("t1.assign_datetime"), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
                    } else {
                        gridDto.setAssignDateTime(rs.getString("t0.d_fwd_tc_desktop_datetime") == null
                                ? AppConstant.DEFAULT_DATE_TIME
                                : Utility.getDate(rs.getString("t0.d_fwd_tc_desktop_datetime"), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
                    }

                    gridDto.setIntimationType(rs.getInt("t2.N_INTIMATION_TYPE"));
                    gridDto.setJobStatus(rs.getString("t4.v_status_desc"));
                    gridDto.setEstimationApprStatus(rs.getString("t0.v_ass_esti_apr_status"));
                    gridDto.setAssFeeAprStatus(rs.getString("t0.v_ass_fee_apr_status"));
                    gridDto.setAssignToRteDateTime(rs.getString("t0.d_assign_rte_datetime") == null
                            ? ""
                            : Utility.getDate(rs.getString("t0.d_assign_rte_datetime"), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));

                    long[] hours;
                    if (rs.getInt("t1.insepction_id") == 8) { //8 --> Desktop Inspection
                        hours = Utility.getDayHoursMinSecondDifference(gridDto.getAssignDateTime(), Utility.sysDateTime(), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT);
                    } else {
                        hours = Utility.getDayHoursMinSecondDifference(gridDto.getAssignToRteDateTime(), Utility.sysDateTime(), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT);
                    }

                    long diffHours = hours[1];
                    long days = hours[0];
                    diffHours = (days * 24) + diffHours;
                    if (diffHours > 24 && diffHours <= 48) {
                        gridDto.setExcessType(1);
                    } else if (diffHours > 48) {
                        gridDto.setExcessType(2);
                    } else {
                        gridDto.setExcessType(0);
                    }
                    gridDto.setIndex(++index);

                    gridDto.setAssignDateTime(Utility.getDateTimeValue(gridDto.getAssignDateTime()));

                    jobList.add(gridDto);
                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(jobList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;

    }

    @Override
    public List<Integer> getClaimListByPolNo(String vehicleNo, Connection connection) throws Exception {
        PreparedStatement ps = null;
        List<Integer> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_CLAIM_LIST_BY_VEHICLE_NO);
            ps.setString(1, vehicleNo);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                list.add(rs.getInt("N_CLIM_NO"));
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<PreviousClaimsDto> getPreviousClaimList(Integer claimNo, Integer refNo, Connection connection) throws Exception {
        PreparedStatement ps = null;
        List<PreviousClaimsDto> list = new ArrayList<>();
        String sql = SQL_SELECT_INSPECTION_LIST_BY_CLAIM_NO_WITH_OUT_REF;
        if (refNo > 0) {
            sql = SQL_SELECT_INSPECTION_LIST_BY_CLAIM_NO;
        }
        try {
            ps = connection.prepareStatement(sql);
            ps.setInt(1, claimNo);
            if (refNo > 0)
                ps.setInt(2, refNo);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                PreviousClaimsDto previousClaimsDto = new PreviousClaimsDto();
                previousClaimsDto.setJobRefNo(rs.getInt("claim_inspection_info_main.n_ref_no"));
                previousClaimsDto.setInspectionTypeId(rs.getInt("t3.insepction_id"));
                previousClaimsDto.setInspectionDetailsPav(null == rs.getBigDecimal("claim_inspection_info_main.n_pav") ? BigDecimal.ZERO : rs.getBigDecimal("claim_inspection_info_main.n_pav"));
                previousClaimsDto.setRecordStatus(rs.getInt("n_record_status"));
                previousClaimsDto.setAssEstiAprStatus(null == rs.getString("claim_inspection_info_main.v_ass_esti_apr_status") ? AppConstant.STRING_EMPTY : rs.getString("claim_inspection_info_main.v_ass_esti_apr_status"));
                previousClaimsDto.setAssFeeAprStatus(null == rs.getString("claim_inspection_info_main.v_ass_fee_apr_status") ? AppConstant.STRING_EMPTY : rs.getString("claim_inspection_info_main.v_ass_fee_apr_status"));
                previousClaimsDto.setJobNo(rs.getString("t3.job_id"));
                previousClaimsDto.setDateOfAccident(rs.getString("t2.D_ACCID_DATE"));
                previousClaimsDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NUMBER"));
                previousClaimsDto.setPolicyNo(rs.getString("t1.V_POL_NUMBER"));
                previousClaimsDto.setInspectionType(rs.getString("t5.inspection_type_desc"));
                previousClaimsDto.setPolicyRefNo(rs.getInt("t1.N_POL_REF_NO"));
                previousClaimsDto.setStatusDesc(rs.getString("status_desc"));
                previousClaimsDto.setApproveDateTime(rs.getString("approve_date_time") != null
                        ? Utility.getCustomDateFormat(rs.getString("approve_date_time"), AppConstant.DATE_TIME_FORMAT) : AppConstant.STRING_EMPTY);
                previousClaimsDto.setAssignAssessor(rs.getString("assessor_user"));
                previousClaimsDto.setAssignRte(null == rs.getString("rte_user") ? AppConstant.STRING_EMPTY : rs.getString("rte_user"));
                previousClaimsDto.setApproveAssignRte(null == rs.getString("v_approve_assign_rte_user") || rs.getString("v_approve_assign_rte_user").isEmpty() ? AppConstant.NOT_AVAILABLE : rs.getString("v_approve_assign_rte_user"));
                list.add(previousClaimsDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<PreviousClaimsDto> getInspectionList(Integer claimNo, Connection connection) {
        PreparedStatement ps;
        List<PreviousClaimsDto> list = new ArrayList<>();
        String sql = SQL_SELECT_INSPECTION_JOB_LIST_BY_CLAIM_NO;
        try {
            ps = connection.prepareStatement(sql);
            ps.setInt(1, claimNo);
            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                PreviousClaimsDto previousClaimsDto = new PreviousClaimsDto();
                previousClaimsDto.setStatusDesc(rs.getString("t5.v_status_desc"));
                previousClaimsDto.setJobRefNo(rs.getInt("t3.ref_no"));
                previousClaimsDto.setInspectionTypeId(rs.getInt("t3.insepction_id"));
                previousClaimsDto.setJobNo(rs.getString("t3.job_id"));
                previousClaimsDto.setDateOfAccident(rs.getString("t2.D_ACCID_DATE"));
                previousClaimsDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NUMBER"));
                previousClaimsDto.setPolicyNo(rs.getString("t1.V_POL_NUMBER"));
                previousClaimsDto.setInspectionType(rs.getString("t4.inspection_type_desc"));
                previousClaimsDto.setPolicyRefNo(rs.getInt("t1.N_POL_REF_NO"));
                previousClaimsDto.setRecordStatus(rs.getInt("t3.record_status"));
                previousClaimsDto.setAssignRte(null == rs.getString("claim_inspection_info_main.v_assign_rte_user") ? AppConstant.STRING_EMPTY : rs.getString("claim_inspection_info_main.v_assign_rte_user"));
                previousClaimsDto.setAssignAssessor(rs.getString("assessor_user"));
                previousClaimsDto.setApproveDateTime(rs.getString("claim_inspection_info_main.v_ass_esti_apr_datetime") != null
                        ? Utility.getCustomDateFormat(rs.getString("claim_inspection_info_main.v_ass_esti_apr_datetime"), AppConstant.DATE_TIME_FORMAT) : AppConstant.STRING_EMPTY);
                list.add(previousClaimsDto);
                previousClaimsDto.setApproveAssignRte(null == rs.getString("v_approve_assign_rte_user") || rs.getString("v_approve_assign_rte_user").isEmpty() ? AppConstant.NOT_AVAILABLE : rs.getString("v_approve_assign_rte_user"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<PreviousClaimsDto> getPreviousClaimList(Integer claimNo, Connection connection) throws Exception {
        PreparedStatement ps = null;
        List<PreviousClaimsDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_INSPECTION_LIST_BY_CLAIM_NO_WITHOUTREF);
            ps.setInt(1, claimNo);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                PreviousClaimsDto previousClaimsDto = new PreviousClaimsDto();
                previousClaimsDto.setJobNo(rs.getString("t3.job_id"));
                previousClaimsDto.setDateOfAccident(rs.getString("t2.D_ACCID_DATE"));
                previousClaimsDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NUMBER"));
                previousClaimsDto.setPolicyNo(rs.getString("t1.V_POL_NUMBER"));
                previousClaimsDto.setInspectionType(rs.getString("t5.inspection_type_desc"));
                previousClaimsDto.setPolicyRefNo(rs.getInt("t1.N_POL_REF_NO"));
                previousClaimsDto.setRefNo(rs.getInt("t3.ref_no"));
                list.add(previousClaimsDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public boolean updateRecordStatusAndAuthStatusByRefNo(Connection connection, int statusId, int refNo, String authStatus) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_INSPECTION_DETAILS_STATUS_AND_AUTH_STAT_BY_REF_NO);
            ps.setInt(1, statusId);
            ps.setString(2, authStatus);
            ps.setInt(3, refNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
    }

    private InspectionDetailsDto getInspctionDetalMeDto(ResultSet rs) {
        InspectionDetailsDto inspectionDetailsDto = new InspectionDetailsDto();
        try {
            inspectionDetailsDto.setInspectionId(rs.getInt("n_inspection_id"));
            inspectionDetailsDto.setRefNo(rs.getInt("n_ref_no"));
            AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();
            ClaimsDto claimsDto = new ClaimsDto();
            assessorAllocationDto.setJobId(rs.getString("v_job_no"));
            claimsDto.setClaimNo(rs.getInt("n_claim_no"));
            inspectionDetailsDto.setClaimNo(rs.getInt("n_claim_no"));
            inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);

            InspectionDto inspectionDto = new InspectionDto();
            inspectionDto.setInspectionId(rs.getInt("n_inspection_type"));
            inspectionDetailsDto.setInspectionDto(inspectionDto);
            inspectionDetailsDto.setJobType(rs.getInt("n_job_type"));
            inspectionDetailsDto.setAssignedLocation(rs.getString("v_assigned_location"));
            inspectionDetailsDto.setCurrentLocation(rs.getString("v_current_location"));
            inspectionDetailsDto.setPlaceOfInspection(rs.getString("v_place_of_inspection"));
            inspectionDetailsDto.setMileage(rs.getInt("n_mileage"));
            inspectionDetailsDto.setCostOfCall(null == rs.getBigDecimal("n_cost_of_call") ? BigDecimal.ZERO : rs.getBigDecimal("n_cost_of_call"));
            inspectionDetailsDto.setDeduction(null == rs.getBigDecimal("n_deductions") ? BigDecimal.ZERO : rs.getBigDecimal("n_deductions"));
            inspectionDetailsDto.setOtherFee(rs.getBigDecimal("n_other_fee"));
            inspectionDetailsDto.setTotalAssessorFee(rs.getBigDecimal("n_total_assessor_fee"));
            inspectionDetailsDto.setFeeDesc(rs.getString("v_fee_desc"));
            inspectionDetailsDto.setRecordStatus(rs.getInt("n_record_status"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return inspectionDetailsDto;
    }

    private InspectionDetailsDto getInspctionDetalsDto(ResultSet rs) {
        InspectionDetailsDto inspectionDetailsDto = new InspectionDetailsDto();
        try {
            inspectionDetailsDto.setInspectionId(rs.getInt("n_inspection_id"));
            inspectionDetailsDto.setRefNo(rs.getInt("n_ref_no"));
            AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();
            ClaimsDto claimsDto = new ClaimsDto();
            assessorAllocationDto.setJobId(rs.getString("v_job_no"));
            claimsDto.setClaimNo(rs.getInt("n_claim_no"));
            inspectionDetailsDto.setClaimNo(rs.getInt("n_claim_no"));
            inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);

            inspectionDetailsDto.setMakeConfirm(rs.getString("v_make_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_make_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_make_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);


            inspectionDetailsDto.setModelConfirm(rs.getString("v_model_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_model_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_model_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);


            inspectionDetailsDto.setEngNoConfirm(rs.getString("v_eng_no_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_eng_no_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_eng_no_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);

            inspectionDetailsDto.setChassisNo(rs.getString("v_chassis_no"));
            inspectionDetailsDto.setYearMakeConfirm(rs.getString("v_year_make_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_year_make_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_year_make_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);


            inspectionDetailsDto.setInspectDateTime(rs.getString("d_inspect_datetime"));
            inspectionDetailsDto.setPav(rs.getBigDecimal("n_pav"));
            inspectionDetailsDto.setDamageDetails(rs.getString("v_damage_details"));
            inspectionDetailsDto.setPad(rs.getString("v_pad"));

            if (null != rs.getString("v_genun_of_accid")) {
                if (rs.getString("v_genun_of_accid").equals("C")) {
                    inspectionDetailsDto.setGenuineOfAccident(AccidentStatus.Consistent);

                } else if (rs.getString("v_genun_of_accid").equals("D")) {
                    inspectionDetailsDto.setGenuineOfAccident(AccidentStatus.Doubtful);
                } else {
                    inspectionDetailsDto.setGenuineOfAccident(AccidentStatus.Non_Consistence);
                }
            }

            if (null != rs.getString("v_first_statement_rqed")) {
                inspectionDetailsDto.setFirstStatementReq(rs.getString("v_first_statement_rqed").equals("Y") ? ConditionType.Yes : ConditionType.No);
            }

            if (null != rs.getString("v_invest_rqed")) {
                inspectionDetailsDto.setInvestReq(rs.getString("v_invest_rqed").equals("Y") ? ConditionType.Yes : ConditionType.No);
            }

            inspectionDetailsDto.setFirstStatementReqReason(rs.getString("v_first_statement_req_reason"));

            inspectionDetailsDto.setAssessorSpecialRemark(rs.getString("v_assessor_remark"));
            InspectionDto inspectionDto = new InspectionDto();
            inspectionDto.setInspectionId(rs.getInt("n_inspection_type"));
            inspectionDetailsDto.setInspectionDto(inspectionDto);
            inspectionDetailsDto.setJobType(rs.getInt("n_job_type"));
            inspectionDetailsDto.setAssessorFeeDetailId(rs.getInt("assessor_fee_detail_id"));
            inspectionDetailsDto.setAssignedLocation(rs.getString("v_assigned_location"));
            inspectionDetailsDto.setCurrentLocation(rs.getString("v_current_location"));
            inspectionDetailsDto.setPlaceOfInspection(rs.getString("v_place_of_inspection"));
            inspectionDetailsDto.setMileage(rs.getInt("n_mileage"));
            inspectionDetailsDto.setCostOfCall(null == rs.getBigDecimal("n_cost_of_call") ? BigDecimal.ZERO : rs.getBigDecimal("n_cost_of_call"));
            inspectionDetailsDto.setOtherFee(rs.getBigDecimal("n_other_fee"));
            inspectionDetailsDto.setTotalAssessorFee(rs.getBigDecimal("n_total_assessor_fee"));
            inspectionDetailsDto.setFeeDesc(rs.getString("v_fee_desc"));
            inspectionDetailsDto.setRecordStatus(rs.getInt("n_record_status"));
            inspectionDetailsDto.setInputUserId(rs.getString("v_inpuser"));
            inspectionDetailsDto.setInputDatetime(rs.getString("d_inpdatetime"));
            inspectionDetailsDto.setAssignRteUser(rs.getString("v_assign_rte_user"));
            inspectionDetailsDto.setAssignRteDatetime(Utility.getCustomDateFormat(rs.getString("d_assign_rte_datetime"), AppConstant.DATE_TIME_FORMAT, AppConstant.DATE_TIME_FORMAT));
            inspectionDetailsDto.setApproveAssignRteUser(rs.getString("v_approve_assign_rte_user"));
            inspectionDetailsDto.setApproverAssignRteDateTime(Utility.getCustomDateFormat(rs.getString("d_approve_assign_datetime"), AppConstant.DATE_TIME_FORMAT, AppConstant.DATE_TIME_FORMAT));
            inspectionDetailsDto.setFwdTcDesktopUser(rs.getString("v_fwd_tc_desktop_user"));
            inspectionDetailsDto.setFwdTcDesktopDatetime(rs.getString("d_fwd_tc_desktop_datetime"));
            inspectionDetailsDto.setFwdTcUser(rs.getString("v_fwd_tc_user"));
            inspectionDetailsDto.setFwdTcDatetime(rs.getString("d_fwd_tc_datetime"));
            inspectionDetailsDto.setAssignTcUser(rs.getString("v_assign_tc_user"));
            inspectionDetailsDto.setAssignTcDatetime(rs.getString("d_assign_tc_datetime"));
            inspectionDetailsDto.setFwdRteDesktopUser(rs.getString("v_fwd_rte_desktop_user"));
            inspectionDetailsDto.setFwdRteDesktopDatetime(rs.getString("d_fwd_rte_desktop_datetime"));
            inspectionDetailsDto.setFwdRteUser(rs.getString("v_approve_assign_rte_user"));
            inspectionDetailsDto.setFwdRteDatetime(rs.getString("d_approve_assign_datetime"));
            inspectionDetailsDto.setAssessorFeeAuthStatus(rs.getString("v_ass_fee_apr_status"));
            inspectionDetailsDto.setAssessorFeeAuthUserId(rs.getString("v_ass_fee_apr_user"));
            inspectionDetailsDto.setAssessorFeeAuthDatetime(rs.getString("v_ass_fee_apr_datetime"));
            inspectionDetailsDto.setInspectionDetailsAuthStatus(rs.getString("v_ass_esti_apr_status"));
            inspectionDetailsDto.setInspectionDetailsAuthUserId(rs.getString("v_ass_esti_apr_user"));
            inspectionDetailsDto.setInspectionDetailsAuthDatetime(rs.getString("v_ass_esti_apr_datetime"));

            inspectionDetailsDto.setChassisNoConfirm(rs.getString("v_chassis_no_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_chassis_no_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_chassis_no_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);
            inspectionDetailsDto.setNotCheckedReason(rs.getInt("n_not_checked_reason"));

            inspectionDetailsDto.setIsVehicleAvailable(null == rs.getString("is_vehicle_available") ? AppConstant.YES : rs.getString("is_vehicle_available"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return inspectionDetailsDto;
    }

    @Override
    public boolean updateRecordStatusByRefNo(Connection connection, int statusId, int refNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_INSPECTION_DETAILS_STATUS_BY_REF_NO);
            ps.setInt(1, statusId);
            ps.setInt(2, refNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
    }

    @Override
    public boolean updateAssessorFeeAuthByRefNo(Connection connection, String assessorFeeAuthStatus, String assessorFeeAuthUserId, String assessorFeeAuthDatetime, int refNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement("UPDATE claim_inspection_info_main SET\n"
                    + "		v_ass_fee_apr_status=?,\n"
                    + "		v_ass_fee_apr_user=?,\n"
                    + "		v_ass_fee_apr_datetime=?\n"
                    + "WHERE n_ref_no = ?");
            ps.setString(1, assessorFeeAuthStatus);
            ps.setString(2, assessorFeeAuthUserId);
            ps.setString(3, assessorFeeAuthDatetime);
            ps.setInt(4, refNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
    }

    @Override
    public boolean updateInspectionDetailAuthByRefNo(Connection connection, String inspectionDetailsAuthStatus, String inspectionDetailsAuthUserId, String inspectionDetailsAuthDatetime, int refNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement("UPDATE claim_inspection_info_main SET\n"
                    + "		v_ass_esti_apr_status=?,\n"
                    + "		v_ass_esti_apr_user=?,\n"
                    + "		v_ass_esti_apr_datetime= ?\n"
                    + "WHERE n_ref_no = ?");
            ps.setString(1, inspectionDetailsAuthStatus);
            ps.setString(2, inspectionDetailsAuthUserId);
            ps.setString(3, inspectionDetailsAuthDatetime);
            ps.setInt(4, refNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
    }

    @Override
    public List<ClaimInspectionTypeDto> getClaimInspectionTypeDtoList(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        List<ClaimInspectionTypeDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_INSPECTION_TYPE_LIST_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                ClaimInspectionTypeDto claimInspectionTypeDto = new ClaimInspectionTypeDto();
                claimInspectionTypeDto.setInspectionTypeId(rs.getInt("t1.inspection_type_id"));
                claimInspectionTypeDto.setInspectionTypeDesc(rs.getString("t1.inspection_type_desc"));
                claimInspectionTypeDto.setJobNo(rs.getString("t2.job_id"));
                list.add(claimInspectionTypeDto);
            }
            rs.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception e) {

            }
        }
        return list;
    }

    @Override
    public void updateForwardTcDesktopUser(Connection connection, int refNo, String forwardUserName, String forwardDateTime) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement("UPDATE claim_inspection_info_main\n"
                    + "	SET\n"
                    + "		v_fwd_tc_desktop_user=?,\n"
                    + "		d_fwd_tc_desktop_datetime=?\n"
                    + "	WHERE  n_ref_no = ?");
            ps.setString(1, forwardUserName);
            ps.setString(2, forwardDateTime);
            ps.setInt(3, refNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
    }

    @Override
    public void updateDesktopInformDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement("UPDATE desktop_inspection_details SET\n"
                    + "		inform_to_garage_name=?,\n"
                    + "		inform_to_garage_contact=?,\n"
                    + "		is_agree_garage=?,\n"
                    + "		inform_to_customer_name=?,\n"
                    + "		inform_to_customer_contact=?,\n"
                    + "		is_agree_customer=?,\n"
                    + "		reason_for_disagree=?,\n"
                    + "		desktop_comment=?,\n"
                    + "		is_informed=?,\n"
                    + "		v_informed_user=?,\n"
                    + "		d_informed_datetime=?\n"
                    + "WHERE n_ref_no = ?");
            ps.setString(1, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getInformToGarageName());
            ps.setString(2, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getInformToGarageContact());
            ps.setString(3, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getIsAgreeGarage());
            ps.setString(4, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getInformToCustomerName());
            ps.setString(5, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getInformToCustomerContact());
            ps.setString(6, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getIsAgreeCustomer());
            ps.setString(7, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getReasonForDisagree());
            ps.setString(8, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getDesktopComment());
            ps.setString(9, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getIsInformed());
            ps.setString(10, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getInformedUser());
            ps.setString(11, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getInformedDateTime());
            ps.setInt(12, motorEngineerDetailsDto.getRefNo());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
    }

    @Override
    public InspectionDetailsDto searchAndOrderRefNo(Connection connection, Integer claimNo, Integer refNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_LATEST_ACR_VALUE);
            ps.setInt(1, claimNo);
            ps.setInt(2, refNo);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getInspctionDetalsDto(rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public boolean addRteRemarks(Connection connection, int refNo, String rteRemarks) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(CLAIM_INSPECTION_INFO_MAIN_ME_UPDATE_REMARKS);
            ps.setString(1, rteRemarks);
            ps.setInt(2, refNo);
            return ps.executeUpdate() > 0;

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
    }

    @Override
    public boolean updateAssessorFeeAuthByRefNoMe(Connection connection, String assessorFeeAuthStatus, String assessorFeeAuthUserId, String assessorFeeAuthDatetime, int refNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement("UPDATE claim_inspection_info_main_me SET\n"
                    + "		v_ass_fee_apr_status=?,\n"
                    + "		v_ass_fee_apr_user=?,\n"
                    + "		v_ass_fee_apr_datetime=?\n"
                    + "WHERE n_ref_no = ?");
            ps.setString(1, assessorFeeAuthStatus);
            ps.setString(2, assessorFeeAuthUserId);
            ps.setString(3, assessorFeeAuthDatetime);
            ps.setInt(4, refNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
    }

    @Override
    public boolean updateRecordStatusByRefNoMe(Connection connection, int statusId, int refNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_INSPECTION_DETAILS_STATUS_BY_REF_NO_ME);
            ps.setInt(1, statusId);
            ps.setInt(2, refNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (null != ps) {
                ps.close();
            }
        }

    }

    @Override
    public InspectionDetailsDto getLatestInspection(Connection connection, Integer claimNo) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_LATEST_ONSITE_INSPECTION);
            ps.setInt(1, claimNo);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getInspctionDetalsDto(rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public boolean isOnSiteOrOffSIteInspectionApproved(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_APPROVED_ON_OR_OFF_SITE_INSPECTION);
            ps.setInt(1, claimNo);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return true;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public void updateAssFeeAprStatusMe(Connection connection, Integer refNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_V_ASS_FEE_STATUS_BY_N_REF_NO_ME);
            ps.setInt(1, refNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
    }

    @Override
    public void updateAssFeeAprStatus(Connection connection, Integer refNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_V_ASS_FEE_STATUS_BY_N_REF_NO);
            ps.setInt(1, refNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
    }

    @Override
    public DataGridDto getSubmittedInspectionDetailsOfferGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, UserDto user, Integer offerType, String isRteOrAssessorDetails, String type, String inspectionId) {
        String userId = user.getUserId();
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List jobList = new ArrayList(200);

        int recodeStatus = 0;
        for (FieldParameterDto fieldParameterDto : parameterList) {
            if ("t1.record_status".equals(fieldParameterDto.getDbFieldName())) {
                recodeStatus = Integer.valueOf(fieldParameterDto.getFieldValue());
            }
        }

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        String[] orderFields = {orderField};
        Object[] instances = {AppConstant.PRIORITY_HIGH, AppConstant.PRIORITY_NORMAL};
        final String SQL_ORDER = orderByCases(start, length, orderType, inspectionId.equals("8") ? "t1.priority" : "t2.V_PRIORITY", orderFields, instances).toString();

        //Record Status --> 23 REJECTED | 4 REASSIGNED | 29 ASSIGNED
        //Inspection Id --> 8 Desktop Inspection
        SQL_SEARCH = SQL_SEARCH.concat("AND (t1.record_status NOT IN (23, 4, 29) OR (t1.record_status = 29 AND t1.insepction_id = 8))");
        if (user.getAccessUserType() != 1 && user.getAccessUserType() != 23 && user.getAccessUserType() != 24 && user.getAccessUserType() != 25) {
            if ("70".equals(type)) {
                SQL_SEARCH = SQL_SEARCH.concat("AND (t0.v_approve_assign_rte_user = '" + userId + "') ");
            } else {
                SQL_SEARCH = SQL_SEARCH.concat("AND (t0.v_assign_rte_user = '" + userId + "') ");
            }

        }

        if (8 == recodeStatus) {
            SQL_SEARCH = SQL_SEARCH.concat("AND (((t0.is_vehicle_available = 'Y' OR t0.is_vehicle_available IS NULL) AND t0.v_ass_fee_apr_status IN ('A', 'P', 'H')) OR ((t0.is_vehicle_available = 'N' OR t0.is_vehicle_available IS NULL) AND t0.v_ass_fee_apr_status IN ('P', 'H')))");
        }

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            if ("70".equals(type)) {
                SQL_SEARCH = SQL_SEARCH.concat(" AND t0.d_approve_assign_datetime BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
            } else {
                SQL_SEARCH = SQL_SEARCH.concat(" AND t1.assign_datetime BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
            }

        }

        String SEL_SQL = SQL_SELECT_ALL_SUBMITTED_TO_GRID.concat(SQL_SEARCH).concat(SQL_ORDER);

        String COUNT_SQL = SQL_SELECT_SUBMITTED_COUNT_TO_GRID.concat(SQL_SEARCH);
        if (!AppConstant.STRING_EMPTY.equals(isRteOrAssessorDetails)) {
            if (offerType == 1) {
                if (AppConstant.RTE_DETAILS.equalsIgnoreCase(isRteOrAssessorDetails)) {
                    SEL_SQL = SQL_SELECT_ON_SITE_OFFER_LIST_RTE_DETAILS.concat(SQL_SEARCH).concat(SQL_ORDER);
                    COUNT_SQL = SQL_COUNT_ON_SITE_OFFER_LIST_RTE_DETAILS.concat(SQL_SEARCH);
                } else {
                    SEL_SQL = SQL_SELECT_ON_SITE_OFFER_LIST_ASSESSOR_DETAILS.concat(SQL_SEARCH).concat(SQL_ORDER);
                    COUNT_SQL = SQL_COUNT_ON_SITE_OFFER_LIST_ASSESSOR_DETAILS.concat(SQL_SEARCH);
                }
            } else if (offerType == 2) {
                if (AppConstant.RTE_DETAILS.equalsIgnoreCase(isRteOrAssessorDetails)) {
                    SEL_SQL = SQL_SELECT_GARAGE_OFFER_LIST_RTE_DETAILS.concat(SQL_SEARCH).concat(SQL_ORDER);
                    COUNT_SQL = SQL_COUNT_GARAGE_OFFER_LIST_RTE_DETAILS.concat(SQL_SEARCH);
                } else {
                    SEL_SQL = SQL_SELECT_GARAGE_OFFER_LIST_ASSESSOR_DETAILS.concat(SQL_SEARCH).concat(SQL_ORDER);
                    COUNT_SQL = SQL_COUNT_GARAGE_OFFER_LIST_ASSESSOR_DETAILS.concat(SQL_SEARCH);
                }
            } else if (offerType == 3) {
                SEL_SQL = SQL_SELECT_DESKTOP_OFFER_LIST_RTE_DETAILS.concat(SQL_SEARCH).concat(SQL_ORDER);
                COUNT_SQL = SQL_COUNT_DESKTOP_OFFER_LIST_ASSESSOR_DETAILS.concat(SQL_SEARCH);
            }
        }

        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    InspectionDetailsGridDto gridDto = new InspectionDetailsGridDto();
                    gridDto.setRefNo(rs.getInt("t1.ref_no"));
                    gridDto.setClaimNo(rs.getInt("t2.N_CLIM_NO"));
                    gridDto.setVehicleNo(rs.getString("t2.V_VEHICLE_NO"));
                    gridDto.setJobNo(rs.getString("t1.job_id"));
                    gridDto.setInspectionType(rs.getString("t3.inspection_type_desc"));
                    gridDto.setInspectionTypeId(rs.getString("t1.insepction_id"));
                    gridDto.setStatusId(rs.getInt("t1.record_status"));
                    gridDto.setPolRefNo(rs.getInt("t2.N_POL_REF_NO"));
                    gridDto.setAssignUser(rs.getString("t0.v_assign_rte_user") == null ? AppConstant.STRING_EMPTY : rs.getString("t0.v_assign_rte_user"));
                    gridDto.setAssignDateTime(rs.getString("t1.assign_datetime") == null
                            ? ""
                            : Utility.getDate(rs.getString("t1.assign_datetime"), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));

//                    forwarded user
                    gridDto.setApproveAssignRteUser(rs.getString("t0.v_approve_assign_rte_user") == null ? AppConstant.STRING_EMPTY : rs.getString("t0.v_approve_assign_rte_user"));
                    gridDto.setApproveAssignRteDateTime(rs.getString("t0.d_approve_assign_datetime") == null
                            ? ""
                            : Utility.getDate(rs.getString("t0.d_approve_assign_datetime"), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));


                    gridDto.setIntimationType(rs.getInt("t2.N_INTIMATION_TYPE"));
                    gridDto.setJobStatus(rs.getString("t4.v_status_desc"));
                    gridDto.setEstimationApprStatus(rs.getString("t0.v_ass_esti_apr_status"));
                    gridDto.setAssFeeAprStatus(rs.getString("t0.v_ass_fee_apr_status"));
                    gridDto.setPriority(inspectionId.equals("8") ? null == rs.getString("t1.priority") || rs.getString("t1.priority").isEmpty() ? AppConstant.PRIORITY_NORMAL : rs.getString("t1.priority") : null == rs.getString("t2.V_PRIORITY") || rs.getString("t2.V_PRIORITY").isEmpty() ? AppConstant.PRIORITY_NORMAL : rs.getString("t2.V_PRIORITY"));

                    if (recodeStatus == 8) {
                        gridDto.setClaimStatus(rs.getInt("t2.N_CLAIM_STATUS"));
//                        if ((rs.getString("t0.is_vehicle_available") == null ? AppConstant.STRING_EMPTY : rs.getString("t0.is_vehicle_available")).equals("N") && (rs.getString("t0.v_ass_fee_apr_status") == null ? AppConstant.STRING_EMPTY : rs.getString("t0.v_ass_fee_apr_status")).equals("A")) {
//                            continue;
//                        }
                    }

                    gridDto.setAssignToRteDateTime(rs.getString("t0.d_assign_rte_datetime") == null
                            ? ""
                            : Utility.getDate(rs.getString("t0.d_assign_rte_datetime"), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));

                    long[] hours;
                    if (rs.getInt("t1.insepction_id") == 8) { //8 --> Desktop Inspection
                        hours = Utility.getDayHoursMinSecondDifference(gridDto.getAssignDateTime(), Utility.sysDateTime(), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT);
                    } else {
                        hours = Utility.getDayHoursMinSecondDifference(gridDto.getAssignToRteDateTime(), Utility.sysDateTime(), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT);
                    }

                    long diffHours = hours[1];
                    long days = hours[0];
                    diffHours = (days * 24) + diffHours;
                    if (diffHours > 24 && diffHours <= 48) {
                        gridDto.setExcessType(1);
                    } else if (diffHours > 48) {
                        gridDto.setExcessType(2);
                    } else {
                        gridDto.setExcessType(0);
                    }
                    gridDto.setIndex(++index);
                    jobList.add(gridDto);
                }
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(jobList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public boolean isRteApproved(Connection connection, Integer refNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_RTE_APPROVED);
            ps.setInt(1, refNo);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public boolean isAssessorApproved(Connection connection, Integer refNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_ASSESSOR_APPROVED);
            ps.setInt(1, refNo);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public List<AssessorDto> getAssignAssessorsByClaim(Connection connection, Integer claimNo) {
        PreparedStatement ps = null;
        List<AssessorDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_ASSIGN_ALL_ASSESSORS_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                AssessorDto assessorDto = new AssessorDto();
                assessorDto.setFirstName(rs.getString("t2.v_firstname"));
                assessorDto.setLastName(rs.getString("t2.v_lastname"));
                assessorDto.setAssessorContactNo(rs.getString("t2.v_mobile"));
                list.add(assessorDto);
            }
            rs.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception e) {

            }
        }
        return list;
    }

    @Override
    public InspectionDetailsDto searchByRefNo(Connection connection, Integer keyId) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_FROM_ME_BY_REF_NO);
            ps.setObject(1, keyId);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getInspctionDetalMeDto(rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public boolean isApprovedInspectionNotInOnsite(Connection connection, int claimNo) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SELECT_ONE_BY_NOT_IN_ON_SITE_AND_APPROVED);
            ps.setInt(1, claimNo);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public MotorEngineerDetailsDto getInspectionReportDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, Object id) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_BY_REF_NO);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getInspctionReportDetalsDto(motorEngineerDetailsDto, rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public MotorEngineerDetailsDto getInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, Object id) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_BY_REF_NO);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getInspctionDetals(motorEngineerDetailsDto, rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public MotorEngineerDetailsDto getAssessorFeeInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, Object id) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_BY_REF_NO);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getAssessorFeeInspctionDetalsDto(motorEngineerDetailsDto, rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    private MotorEngineerDetailsDto getAssessorFeeInspctionDetalsDto(MotorEngineerDetailsDto inspectionDetailsDto, ResultSet rs) {
        try {
            AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();
            assessorAllocationDto.setJobId(rs.getString("v_job_no"));
            ClaimsDto claimsDto = new ClaimsDto();
            claimsDto.setClaimNo(rs.getInt("n_claim_no"));

            inspectionDetailsDto.setJobId(rs.getString("v_job_no"));
            inspectionDetailsDto.setClaimNo(rs.getInt("n_claim_no"));
            inspectionDetailsDto.setInspectionId(rs.getInt("n_inspection_id"));
            inspectionDetailsDto.setRefNo(rs.getInt("n_ref_no"));
            inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
            InspectionDto inspectionDto = new InspectionDto();
            inspectionDto.setInspectionId(rs.getInt("n_inspection_type"));
            inspectionDetailsDto.setInspectionDto(inspectionDto);
            inspectionDetailsDto.setJobType(rs.getInt("n_job_type"));
            inspectionDetailsDto.setAssignedLocation(rs.getString("v_assigned_location"));
            inspectionDetailsDto.setCurrentLocation(rs.getString("v_current_location"));
            inspectionDetailsDto.setPlaceOfInspection(rs.getString("v_place_of_inspection"));
            inspectionDetailsDto.setMileage(rs.getInt("n_mileage"));
            inspectionDetailsDto.setCostOfCall(rs.getBigDecimal("n_cost_of_call"));
            inspectionDetailsDto.setOtherFee(rs.getBigDecimal("n_other_fee"));
            inspectionDetailsDto.setTotalAssessorFee(rs.getBigDecimal("n_total_assessor_fee"));
            inspectionDetailsDto.setFeeDesc(rs.getString("v_fee_desc"));
            inspectionDetailsDto.setRecordStatus(rs.getInt("n_record_status"));
            inspectionDetailsDto.setInputUserId(rs.getString("v_inpuser"));
            inspectionDetailsDto.setInputDatetime(Utility.getDate(rs.getString("d_inpdatetime"), AppConstant.DATE_TIME_FORMAT));
            inspectionDetailsDto.setAssessorFeeAuthStatus(rs.getString("v_ass_fee_apr_status"));
            inspectionDetailsDto.setAssessorFeeAuthUserId(rs.getString("v_ass_fee_apr_user"));
            inspectionDetailsDto.setAssessorFeeAuthDatetime(rs.getString("v_ass_fee_apr_datetime"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return inspectionDetailsDto;
    }

    private MotorEngineerDetailsDto getInspctionDetals(MotorEngineerDetailsDto inspectionDetailsDto, ResultSet rs) {
        try {
            AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();
            assessorAllocationDto.setJobId(rs.getString("v_job_no"));
            ClaimsDto claimsDto = new ClaimsDto();
            claimsDto.setClaimNo(rs.getInt("n_claim_no"));

            inspectionDetailsDto.setJobId(rs.getString("v_job_no"));
            inspectionDetailsDto.setClaimNo(rs.getInt("n_claim_no"));
            inspectionDetailsDto.setInspectionId(rs.getInt("n_inspection_id"));
            inspectionDetailsDto.setRefNo(rs.getInt("n_ref_no"));
            inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);

            inspectionDetailsDto.setMakeConfirm(rs.getString("v_make_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_make_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_make_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);


            inspectionDetailsDto.setModelConfirm(rs.getString("v_model_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_model_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_model_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);


            inspectionDetailsDto.setEngNoConfirm(rs.getString("v_eng_no_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_eng_no_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_eng_no_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);

            inspectionDetailsDto.setChassisNo(rs.getString("v_chassis_no"));
            inspectionDetailsDto.setYearMakeConfirm(rs.getString("v_year_make_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_year_make_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_year_make_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);
            inspectionDetailsDto.setInspectDateTime(Utility.getCustomDateFormat(rs.getString("d_inspect_datetime"), AppConstant.DATE_TIME_FORMAT, AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
            inspectionDetailsDto.setPav(rs.getBigDecimal("n_pav"));
            inspectionDetailsDto.setDamageDetails(rs.getString("v_damage_details"));
            inspectionDetailsDto.setPad(rs.getString("v_pad"));
            if (rs.getString("v_genun_of_accid").equals("C")) {
                inspectionDetailsDto.setGenuineOfAccident(AccidentStatus.Consistent);
            } else if (rs.getString("v_genun_of_accid").equals("D")) {
                inspectionDetailsDto.setGenuineOfAccident(AccidentStatus.Doubtful);
            } else {
                inspectionDetailsDto.setGenuineOfAccident(AccidentStatus.Non_Consistence);
            }
            inspectionDetailsDto.setFirstStatementReq(rs.getString("v_first_statement_rqed").equals("Y") ? ConditionType.Yes : ConditionType.No);
            inspectionDetailsDto.setFirstStatementReqReason(rs.getString("v_first_statement_req_reason"));
            inspectionDetailsDto.setInvestReq(rs.getString("v_invest_rqed").equals("Y") ? ConditionType.Yes : ConditionType.No);
            inspectionDetailsDto.setAssessorSpecialRemark(rs.getString("v_assessor_remark"));
            InspectionDto inspectionDto = new InspectionDto();
            inspectionDto.setInspectionId(rs.getInt("n_inspection_type"));
            inspectionDetailsDto.setInspectionDto(inspectionDto);
            inspectionDetailsDto.setRecordStatus(rs.getInt("n_record_status"));
            inspectionDetailsDto.setInputUserId(rs.getString("v_inpuser"));
            inspectionDetailsDto.setInputDatetime(Utility.getDate(rs.getString("d_inpdatetime"), AppConstant.DATE_TIME_FORMAT));
            inspectionDetailsDto.setInspectionDetailsAuthStatus(rs.getString("v_ass_esti_apr_status"));
            inspectionDetailsDto.setInspectionDetailsAuthUserId(rs.getString("v_ass_esti_apr_user"));
            inspectionDetailsDto.setInspectionDetailsAuthDatetime(rs.getString("v_ass_esti_apr_datetime"));
            inspectionDetailsDto.setChassisNoConfirm(rs.getString("v_chassis_no_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_chassis_no_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_chassis_no_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);
            inspectionDetailsDto.setNotCheckedReason(rs.getInt("n_not_checked_reason"));


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return inspectionDetailsDto;
    }

    private MotorEngineerDetailsDto getInspctionReportDetalsDto(MotorEngineerDetailsDto inspectionDetailsDto, ResultSet rs) {
        try {
            AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();
            assessorAllocationDto.setJobId(rs.getString("v_job_no"));
            ClaimsDto claimsDto = new ClaimsDto();
            claimsDto.setClaimNo(rs.getInt("n_claim_no"));

            inspectionDetailsDto.setJobId(rs.getString("v_job_no"));
            inspectionDetailsDto.setClaimNo(rs.getInt("n_claim_no"));
            inspectionDetailsDto.setRefNo(rs.getInt("n_ref_no"));
            inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);

            inspectionDetailsDto.setMakeConfirm(rs.getString("v_make_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_make_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_make_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);


            inspectionDetailsDto.setModelConfirm(rs.getString("v_model_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_model_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_model_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);


            inspectionDetailsDto.setEngNoConfirm(rs.getString("v_eng_no_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_eng_no_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_eng_no_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);

            inspectionDetailsDto.setChassisNo(rs.getString("v_chassis_no"));
            inspectionDetailsDto.setYearMakeConfirm(rs.getString("v_year_make_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_year_make_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_year_make_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);
            inspectionDetailsDto.setInspectDateTime(Utility.getCustomDateFormat(rs.getString("d_inspect_datetime"), AppConstant.DATE_TIME_FORMAT, AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
            inspectionDetailsDto.setPav(rs.getBigDecimal("n_pav"));
            inspectionDetailsDto.setDamageDetails(rs.getString("v_damage_details"));
            inspectionDetailsDto.setPad(rs.getString("v_pad"));
            if (rs.getString("v_genun_of_accid").equals("C")) {
                inspectionDetailsDto.setGenuineOfAccident(AccidentStatus.Consistent);
            } else if (rs.getString("v_genun_of_accid").equals("D")) {
                inspectionDetailsDto.setGenuineOfAccident(AccidentStatus.Doubtful);
            } else {
                inspectionDetailsDto.setGenuineOfAccident(AccidentStatus.Non_Consistence);
            }
            inspectionDetailsDto.setFirstStatementReq(rs.getString("v_first_statement_rqed").equals("Y") ? ConditionType.Yes : ConditionType.No);
            inspectionDetailsDto.setFirstStatementReqReason(rs.getString("v_first_statement_req_reason"));
            inspectionDetailsDto.setInvestReq(rs.getString("v_invest_rqed").equals("Y") ? ConditionType.Yes : ConditionType.No);
            inspectionDetailsDto.setAssessorSpecialRemark(rs.getString("v_assessor_remark"));
            InspectionDto inspectionDto = new InspectionDto();
            inspectionDto.setInspectionId(rs.getInt("n_inspection_type"));
            inspectionDetailsDto.setInspectionDto(inspectionDto);
            inspectionDetailsDto.setRecordStatus(rs.getInt("n_record_status"));
            inspectionDetailsDto.setInputUserId(rs.getString("v_inpuser"));
            inspectionDetailsDto.setInputDatetime(Utility.getDate(rs.getString("d_inpdatetime"), AppConstant.DATE_TIME_FORMAT));
            inspectionDetailsDto.setChassisNoConfirm(rs.getString("v_chassis_no_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_chassis_no_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_chassis_no_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);
            inspectionDetailsDto.setNotCheckedReason(rs.getInt("n_not_checked_reason"));


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return inspectionDetailsDto;
    }

    @Override
    public DataGridDto getInspectionDetailsOfferGridDtoByUser(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, UserDto user, Integer offerType, List<RtePendingClaimsDto> allPendingClaims, Integer status) {
        String userId = user.getUserId();
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List<Object> jobList = new ArrayList<>(200);

        int recodeStatus = 0;
        for (FieldParameterDto fieldParameterDto : parameterList) {
            if ("t1.record_status".equals(fieldParameterDto.getDbFieldName())) {
                recodeStatus = Integer.valueOf(fieldParameterDto.getFieldValue());
            }
        }

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        String[] orderFields = {orderField};
        Object[] instances = {AppConstant.PRIORITY_HIGH, AppConstant.PRIORITY_NORMAL};
        final String SQL_ORDER = orderByCases(start, length, orderType, "t2.V_PRIORITY", orderFields, instances).toString();

        //Record Status --> 23 REJECTED | 4 REASSIGNED | 29 ASSIGNED
        //Inspection Id --> 8 Desktop Inspection
        SQL_SEARCH = SQL_SEARCH.concat("AND (t1.record_status NOT IN (23, 4, 29) OR (t1.record_status = 29 AND t1.insepction_id = 8))");
        if (user.getAccessUserType() != 1 && user.getAccessUserType() != 23 && user.getAccessUserType() != 24 && user.getAccessUserType() != 25) {
            SQL_SEARCH = SQL_SEARCH.concat("AND ((t0.v_assign_rte_user = '" + userId + "' AND t0.n_record_status <> '80') OR (t0.v_approve_assign_rte_user = '" + userId + "' AND t0.n_record_status = '80')) ");
        }

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            if (status.equals(14)) {
                SQL_SEARCH = SQL_SEARCH.concat(" AND t0.d_approve_assign_datetime BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
            } else {
                SQL_SEARCH = SQL_SEARCH.concat(" AND t1.assign_datetime BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
            }
        }

        SQL_SEARCH = SQL_SEARCH.concat(" AND t0.n_claim_no = ? ");

        String SEL_SQL = SQL_SELECT_ALL_SUBMITTED_TO_GRID.concat(SQL_SEARCH).concat(SQL_ORDER);

        String COUNT_SQL = SQL_SELECT_SUBMITTED_COUNT_TO_GRID.concat(SQL_SEARCH);
        if (offerType == 1 || offerType == 2) {
            SEL_SQL = SQL_SELECT_ON_SITE_OFFER_LIST.concat(SQL_SEARCH).concat(SQL_ORDER);
            COUNT_SQL = SQL_COUNT_ON_SITE_OFFER_LIST.concat(SQL_SEARCH);
        } else if (offerType == 3) {
            SEL_SQL = SQL_SELECT_ON_SITE_OFFER_LIST.concat(SQL_SEARCH).concat(SQL_ORDER);
            COUNT_SQL = SQL_COUNT_ON_SITE_OFFER_LIST.concat(SQL_SEARCH);
        }

        try {

            for (RtePendingClaimsDto pendingClaimsDto : allPendingClaims) {
                ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
                ps.setInt(1, pendingClaimsDto.getClaimNo());
                try (ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        count = rs.getInt("cnt");
                    }
                }
                ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
                ps.setInt(1, pendingClaimsDto.getClaimNo());
                try (ResultSet rs = ps.executeQuery()) {
                    while (rs.next()) {
                        InspectionDetailsGridDto gridDto = new InspectionDetailsGridDto();
                        gridDto.setRefNo(rs.getInt("t1.ref_no"));
                        gridDto.setClaimNo(rs.getInt("t2.N_CLIM_NO"));
                        gridDto.setVehicleNo(rs.getString("t2.V_VEHICLE_NO"));
                        gridDto.setJobNo(rs.getString("t1.job_id"));
                        gridDto.setInspectionType(rs.getString("t3.inspection_type_desc"));
                        gridDto.setInspectionTypeId(rs.getString("t1.insepction_id"));
                        gridDto.setStatusId(rs.getInt("t1.record_status"));
                        gridDto.setPolRefNo(rs.getInt("t2.N_POL_REF_NO"));
                        gridDto.setPriority(null == rs.getString("t2.V_PRIORITY") || rs.getString("t2.V_PRIORITY").isEmpty() ? AppConstant.PRIORITY_NORMAL : rs.getString("t2.V_PRIORITY"));
                        gridDto.setAssignUser(rs.getString("t0.v_assign_rte_user") == null ? AppConstant.STRING_EMPTY : rs.getString("t0.v_assign_rte_user"));
                        gridDto.setAssignDateTime(rs.getString("t1.assign_datetime") == null
                                ? ""
                                : Utility.getDate(rs.getString("t1.assign_datetime"), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
                        gridDto.setIntimationType(rs.getInt("t2.N_INTIMATION_TYPE"));
                        gridDto.setJobStatus(rs.getString("t4.v_status_desc"));
                        gridDto.setEstimationApprStatus(rs.getString("t0.v_ass_esti_apr_status"));
                        gridDto.setAssFeeAprStatus(rs.getString("t0.v_ass_fee_apr_status"));

                        if (recodeStatus == 8) {
                            gridDto.setClaimStatus(rs.getInt("t2.N_CLAIM_STATUS"));
                            if ((rs.getString("t0.is_vehicle_available") == null ? AppConstant.STRING_EMPTY : rs.getString("t0.is_vehicle_available")).equals("N") && (rs.getString("t0.v_ass_fee_apr_status") == null ? AppConstant.STRING_EMPTY : rs.getString("t0.v_ass_fee_apr_status")).equals("A")) {
                                continue;
                            }
                        }

                        gridDto.setAssignToRteDateTime(rs.getString("t0.d_assign_rte_datetime") == null
                                ? ""
                                : Utility.getDate(rs.getString("t0.d_assign_rte_datetime"), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));

                        long[] hours;
                        if (rs.getInt("t1.insepction_id") == 8) { //8 --> Desktop Inspection
                            hours = Utility.getDayHoursMinSecondDifference(gridDto.getAssignDateTime(), Utility.sysDateTime(), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT);
                        } else {
                            hours = Utility.getDayHoursMinSecondDifference(gridDto.getAssignToRteDateTime(), Utility.sysDateTime(), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT);
                        }

                        long diffHours = hours[1];
                        long days = hours[0];
                        diffHours = (days * 24) + diffHours;
                        if (diffHours > 24 && diffHours <= 48) {
                            gridDto.setExcessType(1);
                        } else if (diffHours > 48) {
                            gridDto.setExcessType(2);
                        } else {
                            gridDto.setExcessType(0);
                        }
                        gridDto.setIndex(++index);
                        jobList.add(gridDto);
                    }
                }
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(jobList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public boolean updateForwardDetails(Connection connection, String forwardUser, String forwardDateTime, int refNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement("UPDATE claim_inspection_info_main SET\n"
                    + "		v_approve_assign_rte_user=?,\n"
                    + "		d_approve_assign_datetime= ?\n"
                    + "WHERE n_ref_no = ?");
            ps.setString(1, forwardUser);
            ps.setString(2, forwardDateTime);
            ps.setInt(3, refNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
    }

    @Override
    public DataGridDto getSubmittedInspectionDetailsForwardedGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, UserDto user, Integer offerType) {
        String userId = user.getUserId();
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List jobList = new ArrayList(200);

        int recodeStatus = 0;
        for (FieldParameterDto fieldParameterDto : parameterList) {
            if ("t1.record_status".equals(fieldParameterDto.getDbFieldName())) {
                recodeStatus = Integer.valueOf(fieldParameterDto.getFieldValue());
            }
        }

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        String[] orderFields = {orderField};
        Object[] instances = {AppConstant.PRIORITY_HIGH, AppConstant.PRIORITY_NORMAL};
        final String SQL_ORDER = orderByCases(start, length, orderType, "t1.V_PRIORITY", orderFields, instances).toString();

        //Record Status --> 23 REJECTED | 4 REASSIGNED | 29 ASSIGNED
        //Inspection Id --> 8 Desktop Inspection
        SQL_SEARCH = SQL_SEARCH.concat("AND (t1.record_status NOT IN (23, 4, 29) OR (t1.record_status = 29 AND t1.insepction_id = 8))");
        if (user.getAccessUserType() != 1 && user.getAccessUserType() != 23 && user.getAccessUserType() != 24 && user.getAccessUserType() != 25) {
            SQL_SEARCH = SQL_SEARCH.concat("AND (t0.v_approve_assign_rte_user = '" + userId + "') ");
        }

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t1.assign_datetime BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

        String SEL_SQL = SQL_SELECT_ALL_SUBMITTED_AND_FORWARDED_TO_GRID.concat(SQL_SEARCH).concat(SQL_ORDER);

        String COUNT_SQL = SQL_SELECT_SUBMITTED_COUNT_TO_GRID.concat(SQL_SEARCH);
        if (offerType == 1 || offerType == 2) {
            SEL_SQL = SQL_SELECT_ONSITE_OFFER_LIST_FORWARDED.concat(SQL_SEARCH).concat(SQL_ORDER);
            COUNT_SQL = SQL_COUNT_ON_SITE_OFFER_LIST.concat(SQL_SEARCH);
        } else if (offerType == 3) {
            SEL_SQL = SQL_SELECT_ONSITE_OFFER_LIST_FORWARDED.concat(SQL_SEARCH).concat(SQL_ORDER);
            COUNT_SQL = SQL_COUNT_ON_SITE_OFFER_LIST.concat(SQL_SEARCH);
        }

        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    InspectionDetailsGridDto gridDto = new InspectionDetailsGridDto();
                    gridDto.setRefNo(rs.getInt("t1.ref_no"));
                    gridDto.setClaimNo(rs.getInt("t2.N_CLIM_NO"));
                    gridDto.setVehicleNo(rs.getString("t2.V_VEHICLE_NO"));
                    gridDto.setJobNo(rs.getString("t1.job_id"));
                    gridDto.setInspectionType(rs.getString("t3.inspection_type_desc"));
                    gridDto.setInspectionTypeId(rs.getString("t1.insepction_id"));
                    gridDto.setStatusId(rs.getInt("t1.record_status"));
                    gridDto.setPolRefNo(rs.getInt("t2.N_POL_REF_NO"));
                    gridDto.setPriority(null == rs.getString("t2.V_PRIORITY") || rs.getString("t2.V_PRIORITY").isEmpty() ? AppConstant.PRIORITY_NORMAL : rs.getString("t2.V_PRIORITY"));
                    gridDto.setAssignUser(rs.getString("t0.v_approve_assign_rte_user") == null ? AppConstant.STRING_EMPTY : rs.getString("t0.v_approve_assign_rte_user"));
                    gridDto.setAssignDateTime(rs.getString("t1.assign_datetime") == null
                            ? ""
                            : Utility.getDate(rs.getString("t1.assign_datetime"), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
                    gridDto.setIntimationType(rs.getInt("t2.N_INTIMATION_TYPE"));
                    gridDto.setJobStatus(rs.getString("t4.v_status_desc"));
                    gridDto.setEstimationApprStatus(rs.getString("t0.v_ass_esti_apr_status"));
                    gridDto.setAssFeeAprStatus(rs.getString("t0.v_ass_fee_apr_status"));

                    if (recodeStatus == 8) {
                        gridDto.setClaimStatus(rs.getInt("t2.N_CLAIM_STATUS"));
                        if ((rs.getString("t0.is_vehicle_available") == null ? AppConstant.STRING_EMPTY : rs.getString("t0.is_vehicle_available")).equals("N") && (rs.getString("t0.v_ass_fee_apr_status") == null ? AppConstant.STRING_EMPTY : rs.getString("t0.v_ass_fee_apr_status")).equals("A")) {
                            continue;
                        }
                    }

                    gridDto.setAssignToRteDateTime(rs.getString("t0.d_approve_assign_datetime") == null
                            ? ""
                            : Utility.getDate(rs.getString("t0.d_approve_assign_datetime"), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));

                    long[] hours;
                    if (rs.getInt("t1.insepction_id") == 8) { //8 --> Desktop Inspection
                        hours = Utility.getDayHoursMinSecondDifference(gridDto.getAssignDateTime(), Utility.sysDateTime(), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT);
                    } else {
                        hours = Utility.getDayHoursMinSecondDifference(gridDto.getAssignToRteDateTime(), Utility.sysDateTime(), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT);
                    }

                    long diffHours = hours[1];
                    long days = hours[0];
                    diffHours = (days * 24) + diffHours;
                    if (diffHours > 24 && diffHours <= 48) {
                        gridDto.setExcessType(1);
                    } else if (diffHours > 48) {
                        gridDto.setExcessType(2);
                    } else {
                        gridDto.setExcessType(0);
                    }
                    gridDto.setIndex(++index);
                    jobList.add(gridDto);
                }
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(jobList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public List<String> getAssignRteUsersForClaim(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<String> rteUsers = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_RTE_ASSIGN_USERS_FOR_CLAIM);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                rteUsers.add(rs.getString("V_REPORT_TO"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return rteUsers;
    }

    @Override
    public String getConsistencyByAssessorDetails(Connection connection, Integer refNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_GENUIN_OF_ACCID_BY_ASSESSOR);
            ps.setInt(1, refNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                return rs.getString("v_genun_of_accid");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return AppConstant.STRING_EMPTY;
    }

    @Override
    public String getConsistencyByRteDetails(Connection connection, Integer refNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_GENUIN_OF_ACCID_BY_RTE);
            ps.setInt(1, refNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                return rs.getString("v_genun_of_accid");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return AppConstant.STRING_EMPTY;
    }

    @Override
    public Integer getForwardedAuthLimit(Connection connection, int refNo, boolean isReporting) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(isReporting ? GET_REPORTING_AUTH_LIMIT : GET_FORWARDED_AUTH_LIMIT);
            ps.setInt(1, refNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                return rs.getInt("n_auth_level");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            ps.close();
            rs.close();
        }
        return null;
    }

    @Override
    public Integer getLatestInspectionRefNo(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(GET_LATEST_INSPECTION);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                return rs.getInt("ref_no");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return null;
    }

    @Override
    public DataGridDto getAssessorPendingJobDataGridDto(Connection connection, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List jobList = new ArrayList(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t3.assign_datetime BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

        final String SEL_SQL = SQL_SELECT_ASSESSOR_PENDING_ALL_TO_GRID.concat(SQL_SEARCH).concat(SQL_ORDER);

        try {
            ps = connection.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    InspectionDetailsGridDto gridDto = new InspectionDetailsGridDto();
                    gridDto.setRefNo(rs.getInt("t3.ref_no"));
                    gridDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
                    gridDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NO"));
                    gridDto.setJobNo(rs.getString("t3.job_id"));
                    gridDto.setInspectionType(rs.getString("t2.inspection_type_desc"));
                    gridDto.setStatusId(rs.getInt("t3.record_status"));
                    gridDto.setPolRefNo(rs.getInt("t1.N_POL_REF_NO"));
                    gridDto.setAssignDateTime(Utility.getDate(rs.getString("t3.assign_datetime"), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
                    gridDto.setIntimationType(rs.getInt("t1.N_INTIMATION_TYPE"));
                    gridDto.setJobStatus(rs.getString("t4.v_status_desc"));
                    gridDto.setIsOnlineInspection(rs.getString("t3.is_online_inspection"));

                    long[] hours = Utility.getDayHoursMinSecondDifference(gridDto.getAssignDateTime(), Utility.sysDateTime(), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT);
                    long diffHours = hours[1];
                    long days = hours[0];
                    diffHours = (days * 24) + diffHours;
                    if (diffHours > 24 && diffHours <= 48) {
                        gridDto.setExcessType(1);
                    } else if (diffHours > 48) {
                        gridDto.setExcessType(2);
                    } else {
                        gridDto.setExcessType(0);
                    }
                    gridDto.setAssignUser(rs.getString("assign_assessor") != null ? rs.getString("assign_assessor") : AppConstant.STRING_EMPTY);
                    gridDto.setIndex(++index);
                    jobList.add(gridDto);
                    count++;
                }
            }

            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(jobList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public String getInspectionTypeById(Connection connection, Integer inspectionId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_INSPECTION_TYPE_BY_INSPECTION_ID);
            ps.setInt(1, inspectionId);
            rs = ps.executeQuery();
            while (rs.next()) {
                return rs.getString("inspection_type_desc");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    public boolean getIsVehicleNotAvailableOnSiteOffSite(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_IS_VEHICLE_NOT_AVAILABLE_ONSITE_OFFSITE);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            ps.close();
            rs.close();
        }
        return false;
    }

}
