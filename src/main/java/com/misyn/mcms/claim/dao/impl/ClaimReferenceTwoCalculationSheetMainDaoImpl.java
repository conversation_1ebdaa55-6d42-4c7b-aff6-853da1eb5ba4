package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.ClaimReferenceTwoCalculationSheetMainDao;
import com.misyn.mcms.claim.dto.CalSheetTypeDto;
import com.misyn.mcms.claim.dto.CalculationSheetHistoryDto;
import com.misyn.mcms.claim.dto.ClaimCalculationSheetMainDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.enums.ClaimStatus;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
public class ClaimReferenceTwoCalculationSheetMainDaoImpl implements ClaimReferenceTwoCalculationSheetMainDao  {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimReferenceTwoCalculationSheetMainDaoImpl.class);

    @Override
    public ClaimCalculationSheetMainDto insertMaster(Connection connection, ClaimCalculationSheetMainDto claimCalculationSheetMainDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_MAIN_INSERT, Statement.RETURN_GENERATED_KEYS);
            ps.setInt(++index, claimCalculationSheetMainDto.getClaimNo());
            ps.setInt(++index, claimCalculationSheetMainDto.getLossType());
            ps.setInt(++index, claimCalculationSheetMainDto.getCauseOfLoss());
            ps.setInt(++index, claimCalculationSheetMainDto.getPaymentType());
            ps.setInt(++index, claimCalculationSheetMainDto.getCalSheetType());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getLabour());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getParts());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getTotalLabourAndParts());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getPolicyExcess());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getUnderInsuranceRate());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getUnderInsurance());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getBaldTyreRate());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getBaldTyre());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getSpecialDeductions());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getSpecialVatAmount());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getSpecialNbtAmount());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getTotalDeductions());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getTotalAfterDeductions());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getPaidAdvanceAmount());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getPayableAmount());
            ps.setString(++index, claimCalculationSheetMainDto.getRemark());
            ps.setInt(++index, claimCalculationSheetMainDto.getStatus());
            ps.setString(++index, claimCalculationSheetMainDto.getNoObjectionStatus());
            ps.setString(++index, claimCalculationSheetMainDto.getPremiumOutstandingStatus());
            ps.setString(++index, claimCalculationSheetMainDto.getIsExcessInclude());
            ps.setString(++index, claimCalculationSheetMainDto.getVoucherNo());
            ps.setString(++index, claimCalculationSheetMainDto.getVoucherGeneratedUserId());
            ps.setString(++index, claimCalculationSheetMainDto.getVoucherGeneratedDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getInputUser());
            ps.setString(++index, claimCalculationSheetMainDto.getInputDatetime());
            ps.setString(++index, claimCalculationSheetMainDto.getAssignUserId());
            ps.setString(++index, claimCalculationSheetMainDto.getAssignDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getAprUserId());
            ps.setString(++index, claimCalculationSheetMainDto.getAprDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getAprAssignUser());
            ps.setString(++index, claimCalculationSheetMainDto.getAprAssignDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getCheckedUser());
            ps.setString(++index, claimCalculationSheetMainDto.getCheckedDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getSparePartCordinatorAssignUserId());
            ps.setString(++index, claimCalculationSheetMainDto.getSparePartCordinatorAssignDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getScrutinizeTeamAssignUserId());
            ps.setString(++index, claimCalculationSheetMainDto.getScrutinizeTeamAssignDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getSpecialTeamAssignUserId());
            ps.setString(++index, claimCalculationSheetMainDto.getSpecialTeamAssignDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getSpecialTeamMofaAssignUserId());
            ps.setString(++index, claimCalculationSheetMainDto.getSpecialTeamMofaAssignDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getIsReleaseOrderGenerate());
            ps.setString(++index, claimCalculationSheetMainDto.getReleaseOrderGenerateUserId());
            ps.setString(++index, claimCalculationSheetMainDto.getReleaseOrderGenerateDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getIsNoObjectionUpload());
            ps.setInt(++index, claimCalculationSheetMainDto.getNoObjectionDocRefNo());
            ps.setString(++index, claimCalculationSheetMainDto.getIsPremiumOutstandingUpload());
            ps.setInt(++index, claimCalculationSheetMainDto.getPremiumOutstandingDocRefNo());
            ps.setString(++index, claimCalculationSheetMainDto.getNcbStatus());
            ps.setString(++index, claimCalculationSheetMainDto.getRteAssignUserId());
            ps.setString(++index, claimCalculationSheetMainDto.getRteAssignDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getRteAction());

            ps.setString(++index, claimCalculationSheetMainDto.getIsAdjustVatAmount());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getAdjustVatAmount());
            ps.setString(++index, claimCalculationSheetMainDto.getIsAdjustNbtAmount());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getAdjustNbtAmount());

            if (ps.executeUpdate() > 0) {
                try (ResultSet rsKeys = ps.getGeneratedKeys()) {
                    if (rsKeys.next()) {
                        int autoGeneratedId = rsKeys.getInt(1);
                        claimCalculationSheetMainDto.setCalSheetId(autoGeneratedId);
                        return claimCalculationSheetMainDto;
                    }
                }
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimCalculationSheetMainDto updateMaster(Connection connection, ClaimCalculationSheetMainDto claimCalculationSheetMainDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_MAIN_UPDATE);
            ps.setInt(++index, claimCalculationSheetMainDto.getClaimNo());
            ps.setInt(++index, claimCalculationSheetMainDto.getLossType());
            ps.setInt(++index, claimCalculationSheetMainDto.getCauseOfLoss());
            ps.setInt(++index, claimCalculationSheetMainDto.getPaymentType());
            ps.setInt(++index, claimCalculationSheetMainDto.getCalSheetType());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getLabour());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getParts());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getTotalLabourAndParts());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getPolicyExcess());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getUnderInsuranceRate());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getUnderInsurance());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getBaldTyreRate());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getBaldTyre());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getSpecialDeductions());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getSpecialVatAmount());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getSpecialNbtAmount());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getTotalDeductions());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getTotalAfterDeductions());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getPaidAdvanceAmount());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getPayableAmount());
            ps.setString(++index, claimCalculationSheetMainDto.getRemark());
            ps.setInt(++index, claimCalculationSheetMainDto.getStatus());
            ps.setString(++index, claimCalculationSheetMainDto.getNoObjectionStatus());
            ps.setString(++index, claimCalculationSheetMainDto.getPremiumOutstandingStatus());
            ps.setString(++index, claimCalculationSheetMainDto.getIsExcessInclude());
            ps.setString(++index, claimCalculationSheetMainDto.getVoucherNo());
            ps.setString(++index, claimCalculationSheetMainDto.getVoucherGeneratedUserId());
            ps.setString(++index, claimCalculationSheetMainDto.getVoucherGeneratedDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getInputUser());
            ps.setString(++index, claimCalculationSheetMainDto.getInputDatetime());
            ps.setString(++index, claimCalculationSheetMainDto.getAssignUserId());
            ps.setString(++index, claimCalculationSheetMainDto.getAssignDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getAprUserId());
            ps.setString(++index, claimCalculationSheetMainDto.getAprDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getAprAssignUser());
            ps.setString(++index, claimCalculationSheetMainDto.getAprAssignDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getCheckedUser());
            ps.setString(++index, claimCalculationSheetMainDto.getCheckedDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getSparePartCordinatorAssignUserId());
            ps.setString(++index, claimCalculationSheetMainDto.getSparePartCordinatorAssignDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getScrutinizeTeamAssignUserId());
            ps.setString(++index, claimCalculationSheetMainDto.getScrutinizeTeamAssignDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getSpecialTeamAssignUserId());
            ps.setString(++index, claimCalculationSheetMainDto.getSpecialTeamAssignDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getSpecialTeamMofaAssignUserId());
            ps.setString(++index, claimCalculationSheetMainDto.getSpecialTeamMofaAssignDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getNcbStatus());
            ps.setString(++index, claimCalculationSheetMainDto.getRteAssignUserId());
            ps.setString(++index, claimCalculationSheetMainDto.getRteAssignDateTime());
            ps.setString(++index, claimCalculationSheetMainDto.getIsAdjustVatAmount());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getAdjustVatAmount());
            ps.setString(++index, claimCalculationSheetMainDto.getIsAdjustNbtAmount());
            ps.setBigDecimal(++index, claimCalculationSheetMainDto.getAdjustNbtAmount());
            ps.setInt(++index, claimCalculationSheetMainDto.getCalSheetId());

            if (ps.executeUpdate() > 0) {
                return claimCalculationSheetMainDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimCalculationSheetMainDto insertTemporary(Connection connection, ClaimCalculationSheetMainDto claimCalculationSheetMainDto) throws Exception {
        return null;
    }

    @Override
    public ClaimCalculationSheetMainDto updateTemporary(Connection connection, ClaimCalculationSheetMainDto claimCalculationSheetMainDto) throws Exception {
        return null;
    }

    @Override
    public ClaimCalculationSheetMainDto insertHistory(Connection connection, ClaimCalculationSheetMainDto claimCalculationSheetMainDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_MAIN_DELETE);
            ps.setObject(1, id);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public ClaimCalculationSheetMainDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ClaimCalculationSheetMainDto claimCalculationSheetMainDto = new ClaimCalculationSheetMainDto();
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_MAIN_SEARCH);
            ps.setObject(1, id);
            rs = ps.executeQuery();
            if (rs.next()) {

                claimCalculationSheetMainDto.setCalSheetId(rs.getInt("N_CAL_SHEET_ID"));
                claimCalculationSheetMainDto.setClaimNo(rs.getInt("N_CLAIM_NO"));
                claimCalculationSheetMainDto.setLossType(rs.getInt("N_LOSS_TYPE"));
                claimCalculationSheetMainDto.setCauseOfLoss(rs.getInt("N_CAUSE_OF_LOSS"));
                claimCalculationSheetMainDto.setPaymentType(rs.getInt("N_PAYMENT_TYPE"));
                claimCalculationSheetMainDto.setCalSheetType(rs.getInt("N_CAL_SHEET_TYPE"));
                claimCalculationSheetMainDto.setLabour(rs.getBigDecimal("N_LABOUR"));
                claimCalculationSheetMainDto.setParts(rs.getBigDecimal("N_PARTS"));
                claimCalculationSheetMainDto.setTotalLabourAndParts(rs.getBigDecimal("N_TOTAL_LABOUR_AND_PARTS"));
                claimCalculationSheetMainDto.setPolicyExcess(rs.getBigDecimal("N_POLICY_EXCESS"));
                claimCalculationSheetMainDto.setUnderInsuranceRate(rs.getBigDecimal("N_UNDER_INSURANCE_RATE"));
                claimCalculationSheetMainDto.setUnderInsurance(rs.getBigDecimal("N_UNDER_INSURANCE"));
                claimCalculationSheetMainDto.setBaldTyreRate(rs.getBigDecimal("N_BALD_TYRE_RATE"));
                claimCalculationSheetMainDto.setBaldTyre(rs.getBigDecimal("N_BALD_TYRE"));
                claimCalculationSheetMainDto.setSpecialDeductions(rs.getBigDecimal("N_SPECIAL_DEDUCTIONS"));
                claimCalculationSheetMainDto.setTotalDeductions(rs.getBigDecimal("N_TOTAL_DEDUCTIONS"));
                claimCalculationSheetMainDto.setTotalAfterDeductions(rs.getBigDecimal("N_TOTAL_AFTER_DEDUCTIONS"));
                claimCalculationSheetMainDto.setPaidAdvanceAmount(rs.getBigDecimal("N_PAID_ADVANCE_AMOUNT"));
                claimCalculationSheetMainDto.setPayableAmount(rs.getBigDecimal("N_PAYABLE_AMOUNT"));
                claimCalculationSheetMainDto.setRemark(rs.getString("V_REMARK"));
                claimCalculationSheetMainDto.setStatus(rs.getInt("V_STATUS"));
                claimCalculationSheetMainDto.setNoObjectionStatus(rs.getString("V_NO_OBJECTION_STATUS"));
                claimCalculationSheetMainDto.setPremiumOutstandingStatus(rs.getString("V_PREMIUM_OUTSTANDING_STATUS"));
                claimCalculationSheetMainDto.setIsExcessInclude(rs.getString("V_IS_EXCESS_INCLUDE"));
                claimCalculationSheetMainDto.setVoucherNo(rs.getString("V_VOUCHER_NO"));
                claimCalculationSheetMainDto.setVoucherGeneratedUserId(rs.getString("V_VOUCHER_GENERATED_USER"));
                claimCalculationSheetMainDto.setVoucherGeneratedDateTime(rs.getString("D_VOUCHER_GENERATED_DATETIME"));
                claimCalculationSheetMainDto.setInputUser(rs.getString("V_INPUT_USER"));
                claimCalculationSheetMainDto.setInputDatetime(rs.getString("D_INPUT_DATETIME"));
                claimCalculationSheetMainDto.setAssignUserId(rs.getString("V_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setAssignDateTime(rs.getString("D_ASSIGN_DATE_TIME"));
                claimCalculationSheetMainDto.setAprUserId(rs.getString("V_APR_USER"));
                claimCalculationSheetMainDto.setAprDateTime(rs.getString("D_APR_DATE_TIME"));
                claimCalculationSheetMainDto.setAprAssignUser(rs.getString("V_APR_ASSIGN_USER"));
                claimCalculationSheetMainDto.setAprAssignDateTime(rs.getString("D_APR_ASSIGN_DATE_TIME"));
                claimCalculationSheetMainDto.setCheckedUser(rs.getString("V_CHECKED_USER"));
                claimCalculationSheetMainDto.setCheckedDateTime(rs.getString("D_CHECKED_DATE_TIME"));
                claimCalculationSheetMainDto.setSparePartCordinatorAssignUserId(rs.getString("V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setSparePartCordinatorAssignDateTime(rs.getString("D_SPARE_PART_CORDINATOR_ASSIGN_DATE_TIME"));
                claimCalculationSheetMainDto.setScrutinizeTeamAssignUserId(rs.getString("V_SCRUTINIZE_TEAM_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setScrutinizeTeamAssignDateTime(rs.getString("D_SCRUTINIZE_TEAM_ASSIGN_DATE_TIME"));
                claimCalculationSheetMainDto.setSpecialTeamAssignUserId(rs.getString("V_SPECIAL_TEAM_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setSpecialTeamAssignDateTime(rs.getString("D_SPECIAL_TEAM_ASSIGN_DATE_TIME"));
                claimCalculationSheetMainDto.setSpecialTeamMofaAssignUserId(rs.getString("V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setSpecialTeamMofaAssignDateTime(rs.getString("D_SPECIAL_TEAM_MOFA_ASSIGN_DATE_TIME"));
                claimCalculationSheetMainDto.setSpecialVatAmount(rs.getBigDecimal("N_SPECIAL_VAT_AMOUNT"));
                claimCalculationSheetMainDto.setSpecialNbtAmount(rs.getBigDecimal("N_SPECIAL_NBT_AMOUNT"));
                claimCalculationSheetMainDto.setIsNoObjectionUpload(rs.getString("V_IS_NO_OBJECTION_UPLOAD"));
                claimCalculationSheetMainDto.setNoObjectionDocRefNo(rs.getInt("N_NO_OBJECTION_DOC_REF_NO"));
                claimCalculationSheetMainDto.setIsPremiumOutstandingUpload(rs.getString("V_IS_PREMIUM_OUTSTANDING_UPLOAD"));
                claimCalculationSheetMainDto.setPremiumOutstandingDocRefNo(rs.getInt("N_PREMIUM_OUTSTANDING_DOC_REF_NO"));
                claimCalculationSheetMainDto.setNcbStatus(rs.getString("V_NCB_STATUS"));
                claimCalculationSheetMainDto.setRteAssignUserId(rs.getString("V_RTE_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setRteAssignDateTime(rs.getString("D_RTE_ASSIGN_DATE_TIME"));
                claimCalculationSheetMainDto.setRteAction(null == rs.getString("V_RTE_ACTION") ? AppConstant.STRING_EMPTY : rs.getString("V_RTE_ACTION"));

                claimCalculationSheetMainDto.setIsAdjustVatAmount(rs.getString("V_IS_ADJUST_VAT_AMOUNT"));
                claimCalculationSheetMainDto.setAdjustVatAmount(null == rs.getBigDecimal("N_ADJUST_VAT_AMOUNT") ? BigDecimal.ZERO : rs.getBigDecimal("N_ADJUST_VAT_AMOUNT"));
                claimCalculationSheetMainDto.setIsAdjustNbtAmount(rs.getString("V_IS_ADJUST_NBT_AMOUNT"));
                claimCalculationSheetMainDto.setAdjustNbtAmount(null == rs.getBigDecimal("N_ADJUST_NBT_AMOUNT") ? BigDecimal.ZERO : rs.getBigDecimal("N_ADJUST_NBT_AMOUNT"));

                return claimCalculationSheetMainDto;
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (null != rs) {
                rs.close();
            }
            if (null != ps) {
                ps.close();
            }
        }
        return claimCalculationSheetMainDto;
    }

    @Override
    public ClaimCalculationSheetMainDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<ClaimCalculationSheetMainDto> searchAll(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<ClaimCalculationSheetMainDto> claimCalculationSheetMainDtoList = new ArrayList();

        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_MAIN_SEARCH_ALL);
            rs = ps.executeQuery();

            while (rs.next()) {
                ClaimCalculationSheetMainDto claimCalculationSheetMainDto = new ClaimCalculationSheetMainDto();

                claimCalculationSheetMainDto.setCalSheetId(rs.getInt("N_CAL_SHEET_ID"));
                claimCalculationSheetMainDto.setClaimNo(rs.getInt("N_CLAIM_NO"));
                claimCalculationSheetMainDto.setLossType(rs.getInt("N_LOSS_TYPE"));
                claimCalculationSheetMainDto.setCauseOfLoss(rs.getInt("N_CAUSE_OF_LOSS"));
                claimCalculationSheetMainDto.setPaymentType(rs.getInt("N_PAYMENT_TYPE"));
                claimCalculationSheetMainDto.setCalSheetType(rs.getInt("N_CAL_SHEET_TYPE"));
                claimCalculationSheetMainDto.setLabour(rs.getBigDecimal("N_LABOUR"));
                claimCalculationSheetMainDto.setParts(rs.getBigDecimal("N_PARTS"));
                claimCalculationSheetMainDto.setTotalLabourAndParts(rs.getBigDecimal("N_TOTAL_LABOUR_AND_PARTS"));
                claimCalculationSheetMainDto.setPolicyExcess(rs.getBigDecimal("N_POLICY_EXCESS"));
                claimCalculationSheetMainDto.setUnderInsuranceRate(rs.getBigDecimal("N_UNDER_INSURANCE_RATE"));
                claimCalculationSheetMainDto.setUnderInsurance(rs.getBigDecimal("N_UNDER_INSURANCE"));
                claimCalculationSheetMainDto.setBaldTyreRate(rs.getBigDecimal("N_BALD_TYRE_RATE"));
                claimCalculationSheetMainDto.setBaldTyre(rs.getBigDecimal("N_BALD_TYRE"));
                claimCalculationSheetMainDto.setSpecialDeductions(rs.getBigDecimal("N_SPECIAL_DEDUCTIONS"));
                claimCalculationSheetMainDto.setTotalDeductions(rs.getBigDecimal("N_TOTAL_DEDUCTIONS"));
                claimCalculationSheetMainDto.setTotalAfterDeductions(rs.getBigDecimal("N_TOTAL_AFTER_DEDUCTIONS"));
                claimCalculationSheetMainDto.setPaidAdvanceAmount(rs.getBigDecimal("N_PAID_ADVANCE_AMOUNT"));
                claimCalculationSheetMainDto.setPayableAmount(rs.getBigDecimal("N_PAYABLE_AMOUNT"));
                claimCalculationSheetMainDto.setRemark(rs.getString("V_REMARK"));
                claimCalculationSheetMainDto.setStatus(rs.getInt("V_STATUS"));
                claimCalculationSheetMainDto.setNoObjectionStatus(rs.getString("V_NO_OBJECTION_STATUS"));
                claimCalculationSheetMainDto.setPremiumOutstandingStatus(rs.getString("V_PREMIUM_OUTSTANDING_STATUS"));
                claimCalculationSheetMainDto.setIsExcessInclude(rs.getString("V_IS_EXCESS_INCLUDE"));
                claimCalculationSheetMainDto.setVoucherNo(rs.getString("V_VOUCHER_NO"));
                claimCalculationSheetMainDto.setVoucherGeneratedUserId(rs.getString("V_VOUCHER_GENERATED_USER"));
                claimCalculationSheetMainDto.setVoucherGeneratedDateTime(rs.getString("D_VOUCHER_GENERATED_DATETIME"));
                claimCalculationSheetMainDto.setInputUser(rs.getString("V_INPUT_USER"));
                claimCalculationSheetMainDto.setInputDatetime(rs.getString("D_INPUT_DATETIME"));
                claimCalculationSheetMainDto.setAssignUserId(rs.getString("V_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setAssignDateTime(rs.getString("D_ASSIGN_DATE_TIME"));
                claimCalculationSheetMainDto.setAprUserId(rs.getString("V_APR_USER"));
                claimCalculationSheetMainDto.setAprDateTime(rs.getString("D_APR_DATE_TIME"));
                claimCalculationSheetMainDto.setAprAssignUser(rs.getString("V_APR_ASSIGN_USER"));
                claimCalculationSheetMainDto.setAprAssignDateTime(rs.getString("D_APR_ASSIGN_DATE_TIME"));
                claimCalculationSheetMainDto.setCheckedUser(rs.getString("V_CHECKED_USER"));
                claimCalculationSheetMainDto.setCheckedDateTime(rs.getString("D_CHECKED_DATE_TIME"));
                claimCalculationSheetMainDto.setSparePartCordinatorAssignUserId(rs.getString("V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setSparePartCordinatorAssignDateTime(rs.getString("D_SPARE_PART_CORDINATOR_ASSIGN_DATE_TIME"));
                claimCalculationSheetMainDto.setScrutinizeTeamAssignUserId(rs.getString("V_SCRUTINIZE_TEAM_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setScrutinizeTeamAssignDateTime(rs.getString("D_SCRUTINIZE_TEAM_ASSIGN_DATE_TIME"));
                claimCalculationSheetMainDto.setSpecialTeamAssignUserId(rs.getString("V_SPECIAL_TEAM_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setSpecialTeamAssignDateTime(rs.getString("D_SPECIAL_TEAM_ASSIGN_DATE_TIME"));
                claimCalculationSheetMainDto.setSpecialTeamMofaAssignUserId(rs.getString("V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setSpecialTeamMofaAssignDateTime(rs.getString("D_SPECIAL_TEAM_MOFA_ASSIGN_DATE_TIME"));
                claimCalculationSheetMainDto.setSpecialVatAmount(rs.getBigDecimal("N_SPECIAL_VAT_AMOUNT"));
                claimCalculationSheetMainDto.setSpecialNbtAmount(rs.getBigDecimal("N_SPECIAL_NBT_AMOUNT"));
                claimCalculationSheetMainDto.setNcbStatus(rs.getString("V_NCB_STATUS"));

                claimCalculationSheetMainDto.setIsAdjustVatAmount(rs.getString("V_IS_ADJUST_VAT_AMOUNT"));
                claimCalculationSheetMainDto.setAdjustVatAmount(null == rs.getBigDecimal("N_ADJUST_VAT_AMOUNT") ? BigDecimal.ZERO : rs.getBigDecimal("N_ADJUST_VAT_AMOUNT"));
                claimCalculationSheetMainDto.setIsAdjustNbtAmount(rs.getString("V_IS_ADJUST_NBT_AMOUNT"));
                claimCalculationSheetMainDto.setAdjustNbtAmount(null == rs.getBigDecimal("N_ADJUST_NBT_AMOUNT") ? BigDecimal.ZERO : rs.getBigDecimal("N_ADJUST_NBT_AMOUNT"));

                claimCalculationSheetMainDtoList.add(claimCalculationSheetMainDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return claimCalculationSheetMainDtoList;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public List<ClaimCalculationSheetMainDto> searchByClaimNo(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<ClaimCalculationSheetMainDto> claimCalculationSheetMainDtoList = new ArrayList();

        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_MAIN_SEARCH_ALL_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();

            while (rs.next()) {
                ClaimCalculationSheetMainDto claimCalculationSheetMainDto = new ClaimCalculationSheetMainDto();

                claimCalculationSheetMainDto.setCalSheetId(rs.getInt("t1.N_CAL_SHEET_ID"));
                claimCalculationSheetMainDto.setClaimNo(rs.getInt("t1.N_CLAIM_NO"));
                claimCalculationSheetMainDto.setLossType(rs.getInt("t1.N_LOSS_TYPE"));
                claimCalculationSheetMainDto.setCauseOfLoss(rs.getInt("t1.N_CAUSE_OF_LOSS"));
                claimCalculationSheetMainDto.setPaymentType(rs.getInt("t1.N_PAYMENT_TYPE"));
                claimCalculationSheetMainDto.setCalSheetType(rs.getInt("t1.N_CAL_SHEET_TYPE"));
                claimCalculationSheetMainDto.setLabour(rs.getBigDecimal("t1.N_LABOUR"));
                claimCalculationSheetMainDto.setParts(rs.getBigDecimal("t1.N_PARTS"));
                claimCalculationSheetMainDto.setTotalLabourAndParts(rs.getBigDecimal("t1.N_TOTAL_LABOUR_AND_PARTS"));
                claimCalculationSheetMainDto.setPolicyExcess(rs.getBigDecimal("t1.N_POLICY_EXCESS"));
                claimCalculationSheetMainDto.setUnderInsuranceRate(rs.getBigDecimal("t1.N_UNDER_INSURANCE_RATE"));
                claimCalculationSheetMainDto.setUnderInsurance(rs.getBigDecimal("t1.N_UNDER_INSURANCE"));
                claimCalculationSheetMainDto.setBaldTyreRate(rs.getBigDecimal("t1.N_BALD_TYRE_RATE"));
                claimCalculationSheetMainDto.setBaldTyre(rs.getBigDecimal("t1.N_BALD_TYRE"));
                claimCalculationSheetMainDto.setSpecialDeductions(rs.getBigDecimal("t1.N_SPECIAL_DEDUCTIONS"));
                claimCalculationSheetMainDto.setTotalDeductions(rs.getBigDecimal("t1.N_TOTAL_DEDUCTIONS"));
                claimCalculationSheetMainDto.setTotalAfterDeductions(rs.getBigDecimal("t1.N_TOTAL_AFTER_DEDUCTIONS"));
                claimCalculationSheetMainDto.setPaidAdvanceAmount(rs.getBigDecimal("t1.N_PAID_ADVANCE_AMOUNT"));
                claimCalculationSheetMainDto.setPayableAmount(rs.getBigDecimal("t1.N_PAYABLE_AMOUNT"));
                claimCalculationSheetMainDto.setRemark(rs.getString("t1.V_REMARK"));
                claimCalculationSheetMainDto.setStatus(rs.getInt("t1.V_STATUS"));
                claimCalculationSheetMainDto.setNoObjectionStatus(rs.getString("t1.V_NO_OBJECTION_STATUS"));
                claimCalculationSheetMainDto.setPremiumOutstandingStatus(rs.getString("t1.V_PREMIUM_OUTSTANDING_STATUS"));
                claimCalculationSheetMainDto.setIsExcessInclude(rs.getString("t1.V_IS_EXCESS_INCLUDE"));
                claimCalculationSheetMainDto.setVoucherNo(rs.getString("t1.V_VOUCHER_NO"));
                claimCalculationSheetMainDto.setVoucherGeneratedUserId(rs.getString("t1.V_VOUCHER_GENERATED_USER"));
                claimCalculationSheetMainDto.setVoucherGeneratedDateTime(Utility.getDate(rs.getString("t1.D_VOUCHER_GENERATED_DATETIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setInputUser(rs.getString("t1.V_INPUT_USER"));
                claimCalculationSheetMainDto.setInputDatetime(Utility.getDate(rs.getString("t1.D_INPUT_DATETIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setAssignUserId(rs.getString("t1.V_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setAssignDateTime(Utility.getDate(rs.getString("t1.D_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setAprUserId(rs.getString("t1.V_APR_USER"));
                claimCalculationSheetMainDto.setAprDateTime(Utility.getDate(rs.getString("t1.D_APR_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setAprAssignUser(rs.getString("t1.V_APR_ASSIGN_USER"));
                claimCalculationSheetMainDto.setAprAssignDateTime(Utility.getDate(rs.getString("t1.D_APR_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setCheckedUser(rs.getString("t1.V_CHECKED_USER"));
                claimCalculationSheetMainDto.setCheckedDateTime(Utility.getDate(rs.getString("t1.D_CHECKED_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setSparePartCordinatorAssignUserId(rs.getString("t1.V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setSparePartCordinatorAssignDateTime(Utility.getDate(rs.getString("t1.D_SPARE_PART_CORDINATOR_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setScrutinizeTeamAssignUserId(rs.getString("t1.V_SCRUTINIZE_TEAM_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setScrutinizeTeamAssignDateTime(Utility.getDate(rs.getString("t1.D_SCRUTINIZE_TEAM_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setSpecialTeamAssignUserId(rs.getString("t1.V_SPECIAL_TEAM_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setSpecialTeamAssignDateTime(Utility.getDate(rs.getString("t1.D_SPECIAL_TEAM_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setSpecialTeamMofaAssignUserId(rs.getString("t1.V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setSpecialTeamMofaAssignDateTime(Utility.getDate(rs.getString("t1.D_SPECIAL_TEAM_MOFA_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setPaymentTypeDesc(rs.getString("t2.V_PAYMENT_TYPE_DESC"));
                claimCalculationSheetMainDto.setCalSheetTypeDesc(rs.getString("t3.V_CAL_SHEET_TYPE_DESC"));
                claimCalculationSheetMainDto.setIsReleaseOrderGenerate(rs.getString("t1.V_IS_RELEASE_ORDER_GENERATE"));
                claimCalculationSheetMainDto.setReleaseOrderGenerateUserId(rs.getString("t1.V_RELEASE_ORDER_GENERATE_USER_ID"));
                claimCalculationSheetMainDto.setReleaseOrderGenerateDateTime(rs.getString("t1.D_RELEASE_ORDER_GENERATE_DATE_TIME"));
                claimCalculationSheetMainDto.setSpecialVatAmount(rs.getBigDecimal("t1.N_SPECIAL_VAT_AMOUNT"));
                claimCalculationSheetMainDto.setSpecialNbtAmount(rs.getBigDecimal("t1.N_SPECIAL_NBT_AMOUNT"));
                claimCalculationSheetMainDto.setNcbStatus(rs.getString("t1.V_NCB_STATUS"));

                claimCalculationSheetMainDtoList.add(claimCalculationSheetMainDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return claimCalculationSheetMainDtoList;
    }

    @Override
    public List<ClaimCalculationSheetMainDto> searchByClaimNoAndType(Connection connection, Integer claimNo, Integer calType) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<ClaimCalculationSheetMainDto> claimCalculationSheetMainDtoList = new ArrayList();

        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_MAIN_SEARCH_ALL_BY_CLAIM_NO_AND_CAL_TYPE);
            ps.setInt(1, claimNo);
            ps.setInt(2, calType);
            rs = ps.executeQuery();

            while (rs.next()) {
                ClaimCalculationSheetMainDto claimCalculationSheetMainDto = new ClaimCalculationSheetMainDto();

                claimCalculationSheetMainDto.setCalSheetId(rs.getInt("t1.N_CAL_SHEET_ID"));
                claimCalculationSheetMainDto.setClaimNo(rs.getInt("t1.N_CLAIM_NO"));
                claimCalculationSheetMainDto.setLossType(rs.getInt("t1.N_LOSS_TYPE"));
                claimCalculationSheetMainDto.setCauseOfLoss(rs.getInt("t1.N_CAUSE_OF_LOSS"));
                claimCalculationSheetMainDto.setPaymentType(rs.getInt("t1.N_PAYMENT_TYPE"));
                claimCalculationSheetMainDto.setCalSheetType(rs.getInt("t1.N_CAL_SHEET_TYPE"));
                claimCalculationSheetMainDto.setLabour(rs.getBigDecimal("t1.N_LABOUR"));
                claimCalculationSheetMainDto.setParts(rs.getBigDecimal("t1.N_PARTS"));
                claimCalculationSheetMainDto.setTotalLabourAndParts(rs.getBigDecimal("t1.N_TOTAL_LABOUR_AND_PARTS"));
                claimCalculationSheetMainDto.setPolicyExcess(rs.getBigDecimal("t1.N_POLICY_EXCESS"));
                claimCalculationSheetMainDto.setUnderInsuranceRate(rs.getBigDecimal("t1.N_UNDER_INSURANCE_RATE"));
                claimCalculationSheetMainDto.setUnderInsurance(rs.getBigDecimal("t1.N_UNDER_INSURANCE"));
                claimCalculationSheetMainDto.setBaldTyreRate(rs.getBigDecimal("t1.N_BALD_TYRE_RATE"));
                claimCalculationSheetMainDto.setBaldTyre(rs.getBigDecimal("t1.N_BALD_TYRE"));
                claimCalculationSheetMainDto.setSpecialDeductions(rs.getBigDecimal("t1.N_SPECIAL_DEDUCTIONS"));
                claimCalculationSheetMainDto.setTotalDeductions(rs.getBigDecimal("t1.N_TOTAL_DEDUCTIONS"));
                claimCalculationSheetMainDto.setTotalAfterDeductions(rs.getBigDecimal("t1.N_TOTAL_AFTER_DEDUCTIONS"));
                claimCalculationSheetMainDto.setPaidAdvanceAmount(rs.getBigDecimal("t1.N_PAID_ADVANCE_AMOUNT"));
                claimCalculationSheetMainDto.setPayableAmount(rs.getBigDecimal("t1.N_PAYABLE_AMOUNT"));
                claimCalculationSheetMainDto.setRemark(rs.getString("t1.V_REMARK"));
                claimCalculationSheetMainDto.setStatus(rs.getInt("t1.V_STATUS"));
                claimCalculationSheetMainDto.setNoObjectionStatus(rs.getString("t1.V_NO_OBJECTION_STATUS"));
                claimCalculationSheetMainDto.setPremiumOutstandingStatus(rs.getString("t1.V_PREMIUM_OUTSTANDING_STATUS"));
                claimCalculationSheetMainDto.setIsExcessInclude(rs.getString("t1.V_IS_EXCESS_INCLUDE"));
                claimCalculationSheetMainDto.setVoucherNo(rs.getString("t1.V_VOUCHER_NO"));
                claimCalculationSheetMainDto.setVoucherGeneratedUserId(rs.getString("t1.V_VOUCHER_GENERATED_USER"));
                claimCalculationSheetMainDto.setVoucherGeneratedDateTime(Utility.getDate(rs.getString("t1.D_VOUCHER_GENERATED_DATETIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setInputUser(rs.getString("t1.V_INPUT_USER"));
                claimCalculationSheetMainDto.setInputDatetime(Utility.getDate(rs.getString("t1.D_INPUT_DATETIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setAssignUserId(rs.getString("t1.V_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setAssignDateTime(Utility.getDate(rs.getString("t1.D_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setAprUserId(rs.getString("t1.V_APR_USER"));
                claimCalculationSheetMainDto.setAprDateTime(Utility.getDate(rs.getString("t1.D_APR_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setAprAssignUser(rs.getString("t1.V_APR_ASSIGN_USER"));
                claimCalculationSheetMainDto.setAprAssignDateTime(Utility.getDate(rs.getString("t1.D_APR_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setCheckedUser(rs.getString("t1.V_CHECKED_USER"));
                claimCalculationSheetMainDto.setCheckedDateTime(Utility.getDate(rs.getString("t1.D_CHECKED_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setSparePartCordinatorAssignUserId(rs.getString("t1.V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setSparePartCordinatorAssignDateTime(Utility.getDate(rs.getString("t1.D_SPARE_PART_CORDINATOR_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setScrutinizeTeamAssignUserId(rs.getString("t1.V_SCRUTINIZE_TEAM_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setScrutinizeTeamAssignDateTime(Utility.getDate(rs.getString("t1.D_SCRUTINIZE_TEAM_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setSpecialTeamAssignUserId(rs.getString("t1.V_SPECIAL_TEAM_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setSpecialTeamAssignDateTime(Utility.getDate(rs.getString("t1.D_SPECIAL_TEAM_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setSpecialTeamMofaAssignUserId(rs.getString("t1.V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setSpecialTeamMofaAssignDateTime(Utility.getDate(rs.getString("t1.D_SPECIAL_TEAM_MOFA_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setPaymentTypeDesc(rs.getString("t2.V_PAYMENT_TYPE_DESC"));
                claimCalculationSheetMainDto.setCalSheetTypeDesc(rs.getString("t3.V_CAL_SHEET_TYPE_DESC"));
                claimCalculationSheetMainDto.setIsReleaseOrderGenerate(rs.getString("t1.V_IS_RELEASE_ORDER_GENERATE"));
                claimCalculationSheetMainDto.setReleaseOrderGenerateUserId(rs.getString("t1.V_RELEASE_ORDER_GENERATE_USER_ID"));
                claimCalculationSheetMainDto.setReleaseOrderGenerateDateTime(rs.getString("t1.D_RELEASE_ORDER_GENERATE_DATE_TIME"));
                claimCalculationSheetMainDto.setSpecialVatAmount(rs.getBigDecimal("t1.N_SPECIAL_VAT_AMOUNT"));
                claimCalculationSheetMainDto.setSpecialNbtAmount(rs.getBigDecimal("t1.N_SPECIAL_NBT_AMOUNT"));
                claimCalculationSheetMainDto.setNcbStatus(rs.getString("t1.V_NCB_STATUS"));

                claimCalculationSheetMainDtoList.add(claimCalculationSheetMainDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return claimCalculationSheetMainDtoList;
    }

    @Override
    public void updateSparePartCordAssignUser(Connection connection, Integer calSheetId, String userId, String sysDateTime) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement("UPDATE claim_ref_two_calculation_sheet_main "
                + "SET"
                + " V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID = ?,"
                + " D_SPARE_PART_CORDINATOR_ASSIGN_DATE_TIME = ? WHERE N_CAL_SHEET_ID = ?")) {
            ps.setString(++index, userId);
            ps.setString(++index, sysDateTime);
            ps.setInt(++index, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public void updateReleaseOrderDetails(Connection connection, int calSheetId, String isReleaseOrderGenerate, String releaseOrderGenerateUserId) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement("UPDATE claim_ref_two_calculation_sheet_main "
                + "SET"
                + " V_IS_RELEASE_ORDER_GENERATE = ?,"
                + " V_RELEASE_ORDER_GENERATE_USER_ID = ?,"
                + " D_RELEASE_ORDER_GENERATE_DATE_TIME = ? WHERE N_CAL_SHEET_ID = ?")) {
            ps.setString(++index, isReleaseOrderGenerate);
            ps.setString(++index, releaseOrderGenerateUserId);
            ps.setString(++index, Utility.sysDateTime());
            ps.setInt(++index, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public void updateScrutinizingTeamAssignUser(Connection connection, Integer calSheetId, String userId, String sysDateTime) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement("UPDATE claim_ref_two_calculation_sheet_main "
                + "SET"
                + " V_SCRUTINIZE_TEAM_ASSIGN_USER_ID = ?,"
                + " D_SCRUTINIZE_TEAM_ASSIGN_DATE_TIME = ? WHERE N_CAL_SHEET_ID = ?")) {
            ps.setString(++index, userId);
            ps.setString(++index, sysDateTime);
            ps.setInt(++index, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public void updateSpecialTeamAssignUser(Connection connection, Integer calSheetId, String userId, String sysDateTime) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement("UPDATE claim_ref_two_calculation_sheet_main "
                + "SET"
                + " V_SPECIAL_TEAM_ASSIGN_USER_ID = ?,"
                + " D_SPECIAL_TEAM_ASSIGN_DATE_TIME = ? WHERE N_CAL_SHEET_ID = ?")) {
            ps.setString(++index, userId);
            ps.setString(++index, sysDateTime);
            ps.setInt(++index, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public void updateMofaTeamAssignUser(Connection connection, Integer calSheetId, String userId, String sysDateTime) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement("UPDATE claim_ref_two_calculation_sheet_main "
                + "SET"
                + " V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID = ?,"
                + " D_SPECIAL_TEAM_MOFA_ASSIGN_DATE_TIME = ? WHERE N_CAL_SHEET_ID = ?")) {
            ps.setString(++index, userId);
            ps.setString(++index, sysDateTime);
            ps.setInt(++index, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public void updateStatus(Connection connection, Integer calSheetId, Integer status) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement("UPDATE claim_ref_two_calculation_sheet_main "
                + "SET"
                + " V_STATUS = ? WHERE N_CAL_SHEET_ID = ?")) {
            ps.setInt(++index, status);
            ps.setInt(++index, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public void updatePaymentApproveStatus(Connection connection, Integer calSheetId, Integer status, String userId) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement("UPDATE claim_ref_two_calculation_sheet_main "
                + "SET"
                + " V_STATUS = ?," +
                "V_APR_USER=?," +
                "D_APR_DATE_TIME=?" +
                " WHERE N_CAL_SHEET_ID = ?")) {
            ps.setInt(++index, status);
            ps.setString(++index, userId);
            ps.setString(++index, Utility.sysDateTime());
            ps.setInt(++index, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public void updateSparePartCoordinatorAssignDetails(Connection connection, Integer calSheetId, Integer status, String sparePartCoordinatorUserId) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement("UPDATE claim_ref_two_calculation_sheet_main "
                + "SET"
                + " V_STATUS = ?," +
                "V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID=?," +
                "D_SPARE_PART_CORDINATOR_ASSIGN_DATE_TIME=?" +
                " WHERE N_CAL_SHEET_ID = ?")) {
            ps.setInt(++index, status);
            ps.setString(++index, sparePartCoordinatorUserId);
            ps.setString(++index, Utility.sysDateTime());
            ps.setInt(++index, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public void updateVoucherNo(Connection connection, Integer calSheetId, String voucherNo) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement("UPDATE claim_ref_two_calculation_sheet_main "
                + "SET"
                + " V_VOUCHER_NO = ? WHERE N_CAL_SHEET_ID = ?")) {
            ps.setString(++index, voucherNo);
            ps.setInt(++index, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public void updateVoucherNo(Connection connection, Integer calSheetId, String voucherNo, String generatedUser, String generatedDateTime) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement("UPDATE claim_ref_two_calculation_sheet_main "
                + "SET"
                + " V_VOUCHER_NO = ?, V_VOUCHER_GENERATED_USER = ?, D_VOUCHER_GENERATED_DATETIME = ? WHERE N_CAL_SHEET_ID = ?")) {
            ps.setString(++index, voucherNo);
            ps.setString(++index, generatedUser);
            ps.setString(++index, generatedDateTime);
            ps.setInt(++index, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public List<UserDto> getMofaUserList(Connection connection, String amount, int accessUserType) throws Exception {
        PreparedStatement ps = null;
        List<UserDto> userList = new ArrayList<>();
        try {
            ResultSet rs = null;
            ps = connection.prepareStatement("SELECT t1.v_usrid, t1.n_usrcode, t1.v_firstname, t1.v_lastname, t1.N_PAYMENT_AUTH_LIMIT FROM usr_mst AS t1 WHERE " +
                    "t1.n_accessusrtype IN(?,47,48) AND t1.N_PAYMENT_AUTH_LIMIT >= ?");
            ps.setObject(1, accessUserType);
            ps.setObject(2, amount);
            rs = ps.executeQuery();
            while (rs.next()) {
                UserDto user = new UserDto();
                user.setUserId(rs.getString("t1.v_usrid"));
                user.setUserCode(rs.getInt("t1.n_usrcode"));
                user.setFirstName(rs.getString("t1.v_firstname"));
                user.setLastName(rs.getString("t1.v_lastname"));
                user.setPaymentAuthLimit(rs.getDouble("t1.N_PAYMENT_AUTH_LIMIT"));
                userList.add(user);
            }
            return userList;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            ps.close();
        }
    }

    @Override
    public Integer isSupplyOrderCalculationSheetApproved(Connection connection, Integer supplyOrderId) {
        Integer result = AppConstant.ZERO_INT;
        PreparedStatement ps;
        try {
            ResultSet rs;
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_MAIN_SEARCH_ALL_BY_N_SUPPLIER_ORDER_ID);
            ps.setInt(1, supplyOrderId);
            rs = ps.executeQuery();
            if (rs.next()) {
                result = rs.getInt("t1.N_CAL_SHEET_ID");
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return result;
    }

    @Override
    public boolean isAvailableCalculationSheet(Connection connection, Integer claimNo) {
        boolean result = false;
        PreparedStatement ps;
        try {
            ResultSet rs;
            ps = connection.prepareStatement(SELECT_CALCULATION_MAIN_BY_CLIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                result = true;
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return result;
    }

    @Override
    public List<CalSheetTypeDto> getCalSheetType(Connection connection) throws Exception {
        PreparedStatement ps = null;
        List<CalSheetTypeDto> list = new ArrayList<>();
        try {
            ResultSet rs = null;
            ps = connection.prepareStatement(SELECT_CAL_SHEET_TYPES);

            rs = ps.executeQuery();
            while (rs.next()) {
                CalSheetTypeDto cal = new CalSheetTypeDto();
                cal.setTypeId(rs.getInt("N_CAL_SHEET_TYPE_ID"));
                cal.setTypeDesc(rs.getString("V_CAL_SHEET_TYPE_DESC"));
                cal.setCode(rs.getString("V_CODE"));
                list.add(cal);
            }
            return list;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            ps.close();
        }
    }

    @Override
    public void updateNoObjectionStatus(Connection connection, Integer calSheetId, String status) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement("UPDATE claim_ref_two_calculation_sheet_main "
                + "SET"
                + " V_NO_OBJECTION_STATUS = ? WHERE N_CAL_SHEET_ID = ?")) {
            ps.setString(++index, status);
            ps.setInt(++index, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public void updateNoObjectionUploadReject(Connection connection, Integer claimNo, Integer docRefNo) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement("UPDATE claim_ref_two_calculation_sheet_main\n" +
                "SET V_IS_NO_OBJECTION_UPLOAD = 'R' \n" +
                "WHERE N_CLAIM_NO = ? AND \n" +
                "N_NO_OBJECTION_DOC_REF_NO = ?")) {
            ps.setInt(++index, claimNo);
            ps.setInt(++index, docRefNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public void updatePremiumOutstandingUploadReject(Connection connection, Integer claimNo, Integer docRefNo) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement("UPDATE claim_ref_two_calculation_sheet_main\n" +
                "SET V_IS_PREMIUM_OUTSTANDING_UPLOAD = 'R' \n" +
                "WHERE N_CLAIM_NO = ? AND \n" +
                "N_PREMIUM_OUTSTANDING_DOC_REF_NO = ?")) {
            ps.setInt(++index, claimNo);
            ps.setInt(++index, docRefNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public void updatePremiumOutstandingStatus(Connection connection, Integer calSheetId, String status) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement("UPDATE claim_ref_two_calculation_sheet_main "
                + "SET"
                + " V_PREMIUM_OUTSTANDING_STATUS = ? WHERE N_CAL_SHEET_ID = ?")) {
            ps.setString(++index, status);
            ps.setInt(++index, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public String getSpecialTeamAssignUserId(Connection connection, Integer ClaimNo, Integer Status, Integer calSheetType) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        String SpecialTeamAssignUserId = "";
        try {
            ps = connection.prepareStatement(SELECT_SPECIAL_TEAM_USER_ID);
            ps.setInt(1, ClaimNo);
            ps.setInt(2, Status);
            ps.setInt(3, calSheetType);
            rs = ps.executeQuery();
            while (rs.next()) {
                SpecialTeamAssignUserId = rs.getString("V_SPECIAL_TEAM_ASSIGN_USER_ID");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return SpecialTeamAssignUserId;
    }

    @Override
    public void updateClaSheetPrintStatus(Connection connection, Integer calSheetId, String user, String dateTime) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement("UPDATE claim_ref_two_calculation_sheet_main SET V_IS_RELEASE_ORDER_GENERATE = ? , " +
                "V_RELEASE_ORDER_GENERATE_USER_ID=?," +
                " D_RELEASE_ORDER_GENERATE_DATE_TIME=? WHERE N_CAL_SHEET_ID = ?")) {
            ps.setString(++index, AppConstant.YES);
            ps.setString(++index, user);
            ps.setString(++index, dateTime);
            ps.setInt(++index, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public BigDecimal getTotalPaybleAmount(Connection connection, int claimNo, int calSheetNo,int type) {
        String SQL = "SELECT\n" +
                "\tsum(N_PAYABLE_AMOUNT) AS tot\n" +
                "FROM\n" +
                "\tclaim_ref_two_calculation_sheet_main\n" +
                "WHERE\n" +
                "\tN_CLAIM_NO = ?\n" +
                "AND V_STATUS NOT IN (66, 72, 73)\n" +
                "AND N_CAL_SHEET_ID = ? and N_CAL_SHEET_TYPE = ";


        BigDecimal sum = BigDecimal.ZERO;
        PreparedStatement ps;
        try {
            ResultSet rs;
            ps = connection.prepareStatement(SQL);
            ps.setInt(1, claimNo);
            ps.setInt(2, calSheetNo);
            ps.setInt(3, type);
            rs = ps.executeQuery();
            if (rs.next()) {
                sum = (null == rs.getBigDecimal("tot") ? BigDecimal.ZERO : rs.getBigDecimal("tot"));
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return sum;
    }

    @Override
    public void updateStatusVoucherGenerate(Connection connection, Integer calSheetId, Integer status, String userId) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement("UPDATE claim_ref_two_calculation_sheet_main "
                + "SET"
                + " V_STATUS = ? ,D_VOUCHER_GENERATED_DATETIME = ? ,V_VOUCHER_GENERATED_USER = ? WHERE N_CAL_SHEET_ID = ?")) {
            ps.setInt(++index, status);
            ps.setString(++index, Utility.sysDateTime());
            ps.setString(++index, userId);
            ps.setInt(++index, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public boolean isFoundPendingDocUploadNoObjection(Connection connection, int claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        boolean isFoundPendingDocUploadNoObjection = false;
        String noObjectionStatus = "";
        String noObjectionUpload = "";
        try {
            ps = connection.prepareStatement(SELECT_NO_OBJECTION_STATUS_AND_UPLOAD_BY_CLAIM_ID);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                isFoundPendingDocUploadNoObjection = true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return isFoundPendingDocUploadNoObjection;
    }

    @Override
    public boolean isFoundPendingDocUploadPremiumOutstanding(Connection connection, int claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        boolean isFoundPendingDocUploadPremiumOutstanding = false;
        String premiumOutstandingStatus = "";
        String premiumOutstandingUpload = "";
        try {
            ps = connection.prepareStatement(SELECT_PREMIUM_OUTSTANDING_STATUS_AND_UPLOAD_BY_CLAIM_ID);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                isFoundPendingDocUploadPremiumOutstanding = true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return isFoundPendingDocUploadPremiumOutstanding;
    }

    @Override
    public void updateNoObjectionDocRefNo(Connection connection, int claimNo, int docRefNo) throws Exception {

        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_NO_OBJECTION_DOC_REF_NO)) {
            ps.setInt(++index, docRefNo);
            ps.setInt(++index, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public void updatePremiumOutstandingDocRefNo(Connection connection, int claimNo, int docRefNo) throws Exception {

        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_PREMIUM_OUTSTANDING_DOC_REF_NO)) {
            ps.setInt(++index, docRefNo);
            ps.setInt(++index, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }

    }

    @Override
    public Integer getClaimNoByCalSheetId(Connection connection, int calsheetId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        Integer claimNo = 0;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_NO_BY_CAL_SHEET_ID);
            ps.setInt(1, calsheetId);
            rs = ps.executeQuery();
            if (rs.next()) {
                claimNo = rs.getInt("N_CLAIM_NO");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return claimNo;
    }

    @Override
    public void updateCalsheetAssignUser(Connection connection, String assignUserId, Integer claimNo) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_ASSIGN_USER_BY_CLAIM_NO)) {
            ps.setString(++index, assignUserId);
            ps.setInt(++index, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }

    }

    @Override
    public boolean isExcessAlreadyApply(Connection connection, Integer claimNo, Integer calSheetId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        boolean isExcessAlreadyApply = false;
        try {
            ps = connection.prepareStatement(SELECT_N_POLICY_EXCESS_GREATER_THAN_ZERO_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            ps.setInt(2, calSheetId);
            rs = ps.executeQuery();
            if (rs.next()) {
                isExcessAlreadyApply = true;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return isExcessAlreadyApply;
    }

    @Override
    public boolean isPendingCalsheet(Connection connection, Integer calSheetId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        boolean isPending = false;
        try {
            ps = connection.prepareStatement(SELECT_PENDING_CALSHEET_BY_CALSHEET_ID);
            ps.setInt(1, calSheetId);
            rs = ps.executeQuery();
            if (rs.next()) {
                isPending = true;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return isPending;
    }

    @Override
    public List<ClaimCalculationSheetMainDto> searchByClaimNoOrderByDesc(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<ClaimCalculationSheetMainDto> claimCalculationSheetMainDtoList = new ArrayList();

        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_MAIN_SEARCH_ALL_BY_CLAIM_NO_ORDER_BY_DESC);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();

            while (rs.next()) {
                ClaimCalculationSheetMainDto claimCalculationSheetMainDto = new ClaimCalculationSheetMainDto();

                claimCalculationSheetMainDto.setCalSheetId(rs.getInt("t1.N_CAL_SHEET_ID"));
                claimCalculationSheetMainDto.setClaimNo(rs.getInt("t1.N_CLAIM_NO"));
                claimCalculationSheetMainDto.setLossType(rs.getInt("t1.N_LOSS_TYPE"));
                claimCalculationSheetMainDto.setCauseOfLoss(rs.getInt("t1.N_CAUSE_OF_LOSS"));
                claimCalculationSheetMainDto.setPaymentType(rs.getInt("t1.N_PAYMENT_TYPE"));
                claimCalculationSheetMainDto.setCalSheetType(rs.getInt("t1.N_CAL_SHEET_TYPE"));
                claimCalculationSheetMainDto.setLabour(rs.getBigDecimal("t1.N_LABOUR"));
                claimCalculationSheetMainDto.setParts(rs.getBigDecimal("t1.N_PARTS"));
                claimCalculationSheetMainDto.setTotalLabourAndParts(rs.getBigDecimal("t1.N_TOTAL_LABOUR_AND_PARTS"));
                claimCalculationSheetMainDto.setPolicyExcess(rs.getBigDecimal("t1.N_POLICY_EXCESS"));
                claimCalculationSheetMainDto.setUnderInsuranceRate(rs.getBigDecimal("t1.N_UNDER_INSURANCE_RATE"));
                claimCalculationSheetMainDto.setUnderInsurance(rs.getBigDecimal("t1.N_UNDER_INSURANCE"));
                claimCalculationSheetMainDto.setBaldTyreRate(rs.getBigDecimal("t1.N_BALD_TYRE_RATE"));
                claimCalculationSheetMainDto.setBaldTyre(rs.getBigDecimal("t1.N_BALD_TYRE"));
                claimCalculationSheetMainDto.setSpecialDeductions(rs.getBigDecimal("t1.N_SPECIAL_DEDUCTIONS"));
                claimCalculationSheetMainDto.setTotalDeductions(rs.getBigDecimal("t1.N_TOTAL_DEDUCTIONS"));
                claimCalculationSheetMainDto.setTotalAfterDeductions(rs.getBigDecimal("t1.N_TOTAL_AFTER_DEDUCTIONS"));
                claimCalculationSheetMainDto.setPaidAdvanceAmount(rs.getBigDecimal("t1.N_PAID_ADVANCE_AMOUNT"));
                claimCalculationSheetMainDto.setPayableAmount(rs.getBigDecimal("t1.N_PAYABLE_AMOUNT"));
                claimCalculationSheetMainDto.setRemark(rs.getString("t1.V_REMARK"));
                claimCalculationSheetMainDto.setStatus(rs.getInt("t1.V_STATUS"));
                claimCalculationSheetMainDto.setNoObjectionStatus(rs.getString("t1.V_NO_OBJECTION_STATUS"));
                claimCalculationSheetMainDto.setPremiumOutstandingStatus(rs.getString("t1.V_PREMIUM_OUTSTANDING_STATUS"));
                claimCalculationSheetMainDto.setIsExcessInclude(rs.getString("t1.V_IS_EXCESS_INCLUDE"));
                claimCalculationSheetMainDto.setVoucherNo(rs.getString("t1.V_VOUCHER_NO"));
                claimCalculationSheetMainDto.setVoucherGeneratedUserId(rs.getString("t1.V_VOUCHER_GENERATED_USER"));
                claimCalculationSheetMainDto.setVoucherGeneratedDateTime(Utility.getDate(rs.getString("t1.D_VOUCHER_GENERATED_DATETIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setInputUser(rs.getString("t1.V_INPUT_USER"));
                claimCalculationSheetMainDto.setInputDatetime(Utility.getDate(rs.getString("t1.D_INPUT_DATETIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setAssignUserId(rs.getString("t1.V_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setAssignDateTime(Utility.getDate(rs.getString("t1.D_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setAprUserId(rs.getString("t1.V_APR_USER"));
                claimCalculationSheetMainDto.setAprDateTime(Utility.getDate(rs.getString("t1.D_APR_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setAprAssignUser(rs.getString("t1.V_APR_ASSIGN_USER"));
                claimCalculationSheetMainDto.setAprAssignDateTime(Utility.getDate(rs.getString("t1.D_APR_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setCheckedUser(rs.getString("t1.V_CHECKED_USER"));
                claimCalculationSheetMainDto.setCheckedDateTime(Utility.getDate(rs.getString("t1.D_CHECKED_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setSparePartCordinatorAssignUserId(rs.getString("t1.V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setSparePartCordinatorAssignDateTime(Utility.getDate(rs.getString("t1.D_SPARE_PART_CORDINATOR_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setScrutinizeTeamAssignUserId(rs.getString("t1.V_SCRUTINIZE_TEAM_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setScrutinizeTeamAssignDateTime(Utility.getDate(rs.getString("t1.D_SCRUTINIZE_TEAM_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setSpecialTeamAssignUserId(rs.getString("t1.V_SPECIAL_TEAM_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setSpecialTeamAssignDateTime(Utility.getDate(rs.getString("t1.D_SPECIAL_TEAM_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setSpecialTeamMofaAssignUserId(rs.getString("t1.V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setSpecialTeamMofaAssignDateTime(Utility.getDate(rs.getString("t1.D_SPECIAL_TEAM_MOFA_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimCalculationSheetMainDto.setPaymentTypeDesc(rs.getString("t2.V_PAYMENT_TYPE_DESC"));
                claimCalculationSheetMainDto.setCalSheetTypeDesc(rs.getString("t3.V_CAL_SHEET_TYPE_DESC"));
                claimCalculationSheetMainDto.setIsReleaseOrderGenerate(rs.getString("t1.V_IS_RELEASE_ORDER_GENERATE"));
                claimCalculationSheetMainDto.setReleaseOrderGenerateUserId(rs.getString("t1.V_RELEASE_ORDER_GENERATE_USER_ID"));
                claimCalculationSheetMainDto.setReleaseOrderGenerateDateTime(rs.getString("t1.D_RELEASE_ORDER_GENERATE_DATE_TIME"));
                claimCalculationSheetMainDto.setSpecialVatAmount(rs.getBigDecimal("t1.N_SPECIAL_VAT_AMOUNT"));
                claimCalculationSheetMainDto.setSpecialNbtAmount(rs.getBigDecimal("t1.N_SPECIAL_NBT_AMOUNT"));
                claimCalculationSheetMainDto.setNcbStatus(rs.getString("t1.V_NCB_STATUS"));
                claimCalculationSheetMainDtoList.add(claimCalculationSheetMainDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return claimCalculationSheetMainDtoList;
    }

    @Override
    public BigDecimal getPaidTotalAdvanceAmount(Connection connection, int claimNo) {
        BigDecimal sum = BigDecimal.ZERO;
        PreparedStatement ps;
        try {
            ResultSet rs;
            ps = connection.prepareStatement(SUM_OF_TOTAL_PAID_ADVANCE_AMOUNT);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                sum = (null == rs.getBigDecimal("tot") ? BigDecimal.ZERO : rs.getBigDecimal("tot"));
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return sum;
    }

    @Override
    public boolean isPaymentForwardedToApprovalOrApprove(Connection connection, int claimNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        boolean isPending = false;
        try {
            ps = connection.prepareStatement(SELECT_ONE_FROM_CALSHEET_APPROVED_OR_FORWARD_TO_APPROVAL);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                isPending = true;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return isPending;
    }

    @Override
    public List<Integer> getCalSheetIdsByClaimNo(Connection connection, int claimNo) {
        PreparedStatement ps;
        ResultSet rs;
        List<Integer> calSheetIds = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_CALCULATION_MAIN_BY_CLIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();

            while (rs.next()) {
                calSheetIds.add(rs.getInt("N_CAL_SHEET_ID"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return calSheetIds;
    }

    @Override
    public List<ClaimCalculationSheetMainDto> searchByClaimNoAndNotInVoucherGeneratedOrPending(Connection connection, int claimNo) {
        PreparedStatement ps;
        ResultSet rs;
        List<ClaimCalculationSheetMainDto> claimCalculationSheetMainDtoList = new ArrayList();

        try {
            ps = connection.prepareStatement(SELECT_ALL_FROM_CALSHEET_BY_CLAIM_NO_AND_VOUCHER_GENERATED_AND_PENDING);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();

            while (rs.next()) {
                ClaimCalculationSheetMainDto claimCalculationSheetMainDto = new ClaimCalculationSheetMainDto();

                claimCalculationSheetMainDto.setCalSheetId(rs.getInt("N_CAL_SHEET_ID"));
                claimCalculationSheetMainDto.setClaimNo(rs.getInt("N_CLAIM_NO"));
                claimCalculationSheetMainDto.setLossType(rs.getInt("N_LOSS_TYPE"));
                claimCalculationSheetMainDto.setCauseOfLoss(rs.getInt("N_CAUSE_OF_LOSS"));
                claimCalculationSheetMainDto.setPaymentType(rs.getInt("N_PAYMENT_TYPE"));
                claimCalculationSheetMainDto.setCalSheetType(rs.getInt("N_CAL_SHEET_TYPE"));
                claimCalculationSheetMainDto.setLabour(rs.getBigDecimal("N_LABOUR"));
                claimCalculationSheetMainDto.setParts(rs.getBigDecimal("N_PARTS"));
                claimCalculationSheetMainDto.setTotalLabourAndParts(rs.getBigDecimal("N_TOTAL_LABOUR_AND_PARTS"));
                claimCalculationSheetMainDto.setPolicyExcess(rs.getBigDecimal("N_POLICY_EXCESS"));
                claimCalculationSheetMainDto.setUnderInsuranceRate(rs.getBigDecimal("N_UNDER_INSURANCE_RATE"));
                claimCalculationSheetMainDto.setUnderInsurance(rs.getBigDecimal("N_UNDER_INSURANCE"));
                claimCalculationSheetMainDto.setBaldTyreRate(rs.getBigDecimal("N_BALD_TYRE_RATE"));
                claimCalculationSheetMainDto.setBaldTyre(rs.getBigDecimal("N_BALD_TYRE"));
                claimCalculationSheetMainDto.setSpecialDeductions(rs.getBigDecimal("N_SPECIAL_DEDUCTIONS"));
                claimCalculationSheetMainDto.setTotalDeductions(rs.getBigDecimal("N_TOTAL_DEDUCTIONS"));
                claimCalculationSheetMainDto.setTotalAfterDeductions(rs.getBigDecimal("N_TOTAL_AFTER_DEDUCTIONS"));
                claimCalculationSheetMainDto.setPaidAdvanceAmount(rs.getBigDecimal("N_PAID_ADVANCE_AMOUNT"));
                claimCalculationSheetMainDto.setPayableAmount(rs.getBigDecimal("N_PAYABLE_AMOUNT"));
                claimCalculationSheetMainDto.setRemark(rs.getString("V_REMARK"));
                claimCalculationSheetMainDto.setStatus(rs.getInt("V_STATUS"));
                claimCalculationSheetMainDto.setNoObjectionStatus(rs.getString("V_NO_OBJECTION_STATUS"));
                claimCalculationSheetMainDto.setPremiumOutstandingStatus(rs.getString("V_PREMIUM_OUTSTANDING_STATUS"));
                claimCalculationSheetMainDto.setIsExcessInclude(rs.getString("V_IS_EXCESS_INCLUDE"));
                claimCalculationSheetMainDto.setVoucherNo(rs.getString("V_VOUCHER_NO"));
                claimCalculationSheetMainDto.setVoucherGeneratedUserId(rs.getString("V_VOUCHER_GENERATED_USER"));
                claimCalculationSheetMainDto.setVoucherGeneratedDateTime(rs.getString("D_VOUCHER_GENERATED_DATETIME"));
                claimCalculationSheetMainDto.setInputUser(rs.getString("V_INPUT_USER"));
                claimCalculationSheetMainDto.setInputDatetime(rs.getString("D_INPUT_DATETIME"));
                claimCalculationSheetMainDto.setAssignUserId(rs.getString("V_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setAssignDateTime(rs.getString("D_ASSIGN_DATE_TIME"));
                claimCalculationSheetMainDto.setAprUserId(rs.getString("V_APR_USER"));
                claimCalculationSheetMainDto.setAprDateTime(rs.getString("D_APR_DATE_TIME"));
                claimCalculationSheetMainDto.setAprAssignUser(rs.getString("V_APR_ASSIGN_USER"));
                claimCalculationSheetMainDto.setAprAssignDateTime(rs.getString("D_APR_ASSIGN_DATE_TIME"));
                claimCalculationSheetMainDto.setCheckedUser(rs.getString("V_CHECKED_USER"));
                claimCalculationSheetMainDto.setCheckedDateTime(rs.getString("D_CHECKED_DATE_TIME"));
                claimCalculationSheetMainDto.setSparePartCordinatorAssignUserId(rs.getString("V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setSparePartCordinatorAssignDateTime(rs.getString("D_SPARE_PART_CORDINATOR_ASSIGN_DATE_TIME"));
                claimCalculationSheetMainDto.setScrutinizeTeamAssignUserId(rs.getString("V_SCRUTINIZE_TEAM_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setScrutinizeTeamAssignDateTime(rs.getString("D_SCRUTINIZE_TEAM_ASSIGN_DATE_TIME"));
                claimCalculationSheetMainDto.setSpecialTeamAssignUserId(rs.getString("V_SPECIAL_TEAM_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setSpecialTeamAssignDateTime(rs.getString("D_SPECIAL_TEAM_ASSIGN_DATE_TIME"));
                claimCalculationSheetMainDto.setSpecialTeamMofaAssignUserId(rs.getString("V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setSpecialTeamMofaAssignDateTime(rs.getString("D_SPECIAL_TEAM_MOFA_ASSIGN_DATE_TIME"));
                claimCalculationSheetMainDto.setSpecialVatAmount(rs.getBigDecimal("N_SPECIAL_VAT_AMOUNT"));
                claimCalculationSheetMainDto.setSpecialNbtAmount(rs.getBigDecimal("N_SPECIAL_NBT_AMOUNT"));
                claimCalculationSheetMainDto.setNcbStatus(rs.getString("V_NCB_STATUS"));

                claimCalculationSheetMainDto.setIsAdjustVatAmount(rs.getString("V_IS_ADJUST_VAT_AMOUNT"));
                claimCalculationSheetMainDto.setAdjustVatAmount(null == rs.getBigDecimal("N_ADJUST_VAT_AMOUNT") ? BigDecimal.ZERO : rs.getBigDecimal("N_ADJUST_VAT_AMOUNT"));
                claimCalculationSheetMainDto.setIsAdjustNbtAmount(rs.getString("V_IS_ADJUST_NBT_AMOUNT"));
                claimCalculationSheetMainDto.setAdjustNbtAmount(null == rs.getBigDecimal("N_ADJUST_NBT_AMOUNT") ? BigDecimal.ZERO : rs.getBigDecimal("N_ADJUST_NBT_AMOUNT"));

                claimCalculationSheetMainDtoList.add(claimCalculationSheetMainDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimCalculationSheetMainDtoList;
    }

    @Override
    public void assignSpTeamAndMofaTeam(Connection connection, ClaimCalculationSheetMainDto updateCalculationShettMainDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_SPECIAL_TEAM_AND_MOFA_ASSIGN_USER_BY_CALSHEET_ID)) {
            ps.setString(++index, updateCalculationShettMainDto.getSpecialTeamAssignUserId());
            ps.setString(++index, updateCalculationShettMainDto.getSpecialTeamAssignDateTime());
            ps.setString(++index, updateCalculationShettMainDto.getSpecialTeamMofaAssignUserId());
            ps.setString(++index, updateCalculationShettMainDto.getSpecialTeamMofaAssignDateTime());
            ps.setInt(++index, updateCalculationShettMainDto.getCalSheetId());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public void assignSpTeamTeam(Connection connection, Integer calSheetId, String userId) throws Exception {
        try {
            PreparedStatement ps = connection.prepareStatement(UPDATE_SPECIAL_TEAM_ASSIGN_USER_BY_CALSHEET_ID);
            ps.setString(1, userId);
            ps.setString(2, Utility.sysDateTime());
            ps.setInt(3, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception("System Error", e);
        }
    }

    @Override
    public String getMofaUser(Connection connection, BigDecimal payableAmount, Integer accessUserType) throws Exception {
        PreparedStatement ps = null;
        try {
            ResultSet rs = null;
            ps = connection.prepareStatement(SELECT_MOFA_USER);
            ps.setObject(1, accessUserType);
            ps.setObject(2, payableAmount);
            rs = ps.executeQuery();
            while (rs.next()) {
                return (rs.getString("t1.v_usrid"));

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            ps.close();
        }
        return null;
    }

    @Override
    public ClaimCalculationSheetMainDto getSparePartsCoordFromClaimNo(Connection connection, Integer claimNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ClaimCalculationSheetMainDto claimCalculationSheetMainDto = null;
        try {
            ps = connection.prepareStatement(GET_SPARE_PARTS_COORDINATOR_FROM_CALSHEET_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                claimCalculationSheetMainDto = new ClaimCalculationSheetMainDto();
                claimCalculationSheetMainDto.setSparePartCordinatorAssignUserId(rs.getString("V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setCalSheetId(rs.getInt("N_CAL_SHEET_ID"));
            }
            ps.close();
            rs.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return claimCalculationSheetMainDto;
    }

    @Override
    public ClaimCalculationSheetMainDto getScrutinizingUserFromClaimNo(Connection connection, Integer claimNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ClaimCalculationSheetMainDto claimCalculationSheetMainDto = null;
        try {
            ps = connection.prepareStatement(GET_SCRUTINIZING_USER_FROM_CALSHEET_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                claimCalculationSheetMainDto = new ClaimCalculationSheetMainDto();
                claimCalculationSheetMainDto.setScrutinizeTeamAssignUserId(rs.getString("V_SCRUTINIZE_TEAM_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setCalSheetId(rs.getInt("N_CAL_SHEET_ID"));
            }
            ps.close();
            rs.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return claimCalculationSheetMainDto;
    }

    @Override
    public BigDecimal getTotalApprovedPaymentAmount(Connection connection, Integer claimNo) throws Exception {
        BigDecimal sum = BigDecimal.ZERO;
        PreparedStatement ps;
        try {
            ResultSet rs;
            ps = connection.prepareStatement(SELECT_SUM_OF_APPROVED_CALSHEET_PAYABLE_AMOUNT);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                sum = (null == rs.getBigDecimal("tot") ? BigDecimal.ZERO : rs.getBigDecimal("tot"));
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return sum;
    }

    @Override
    public void updateNoClaimBonusStatus(Connection connection, Integer calSheetId, String status) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement("UPDATE claim_ref_two_calculation_sheet_main "
                + "SET"
                + " V_NCB_STATUS = ? WHERE N_CAL_SHEET_ID = ?")) {
            ps.setString(++index, status);
            ps.setInt(++index, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public String getAssignUserId(Connection connection, Integer calSheetId) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_ASSIGN_USER_ID_BY_CALSHEET_ID);
            ps.setInt(1, calSheetId);
            rs = ps.executeQuery();
            while (rs.next()) {
                return rs.getString("V_ASSIGN_USER_ID");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public boolean isAvailableCalSheetByStatus(Connection connection, int claimNo, int claimStatus) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_ONE_BY_CLAIM_NO_AND_STATUS);
            ps.setObject(1, claimNo);
            ps.setObject(2, claimStatus);
            rs = ps.executeQuery();
            while (rs.next()) {
                return true;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public ClaimCalculationSheetMainDto getLatestCalsheet(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ClaimCalculationSheetMainDto calSheet = null;
        try {
            ps = connection.prepareStatement(SELECT_LATEST_CALSHEET);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                calSheet = new ClaimCalculationSheetMainDto();
                calSheet.setStatus(rs.getInt("V_STATUS"));
                calSheet.setVoucherGeneratedUserId(rs.getString("V_VOUCHER_GENERATED_USER"));
                calSheet.setInputUser(rs.getString("V_INPUT_USER"));
                calSheet.setAssignUserId(rs.getString("V_ASSIGN_USER_ID"));
                calSheet.setAprUserId(rs.getString("V_APR_USER"));
                calSheet.setAprAssignUser(rs.getString("V_APR_ASSIGN_USER"));
                calSheet.setSparePartCordinatorAssignUserId(rs.getString("V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID"));
                calSheet.setScrutinizeTeamAssignUserId(rs.getString("V_SCRUTINIZE_TEAM_ASSIGN_USER_ID"));
                calSheet.setSpecialTeamAssignUserId(rs.getString("V_SPECIAL_TEAM_ASSIGN_USER_ID"));
                calSheet.setSpecialTeamMofaAssignUserId(rs.getString("V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID"));
                calSheet.setReleaseOrderGenerateUserId(rs.getString("V_RELEASE_ORDER_GENERATE_USER_ID"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return calSheet;
    }

    @Override
    public List<CalculationSheetHistoryDto> getCalculationSheetListByClaimNo(Connection connection, Integer claimNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<CalculationSheetHistoryDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_BY_CLAIM_NO_NOT_IN_REJECTED);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                CalculationSheetHistoryDto dto = new CalculationSheetHistoryDto();
                dto.setCalsheetId(rs.getInt("t1.N_CAL_SHEET_ID"));
                dto.setCalsheetTypeId(rs.getInt("t1.N_CAL_SHEET_TYPE"));
                dto.setCalsheetTypeDesc(rs.getString("t2.V_CAL_SHEET_TYPE_DESC"));
                dto.setPaidAmount(rs.getBigDecimal("t1.N_PAYABLE_AMOUNT"));
                dto.setDateOfPaid(null == rs.getString("t1.D_VOUCHER_GENERATED_DATETIME")
                        || AppConstant.STRING_EMPTY.equalsIgnoreCase(rs.getString("t1.D_VOUCHER_GENERATED_DATETIME"))
                        ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t1.D_VOUCHER_GENERATED_DATETIME"), AppConstant.DATE_TIME_FORMAT));
                dto.setClaimHandlerUserId(rs.getString("t1.V_ASSIGN_USER_ID"));
                dto.setSpteadmUserId(rs.getString("t1.V_SPECIAL_TEAM_ASSIGN_USER_ID"));
                dto.setApprovedUserId(rs.getString("t1.V_APR_USER"));
                dto.setRteAction(rs.getString("t1.V_RTE_ACTION"));
                dto.setAcrApprovedRte(rs.getString("t4.V_ACR_APRV_USER"));
                dto.setApprovedRte(rs.getString("t1.V_RTE_ASSIGN_USER_ID"));
                dto.setPaymentStatus(rs.getInt("t1.V_STATUS"));

                list.add(dto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return list;
    }

    @Override
    public void updateRteAction(Connection connection, Integer calSheetId, String rteAction) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_RTE_ACTION_BY_CALSHEET_ID)) {
            ps.setString(++index, rteAction);
            ps.setInt(++index, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public BigDecimal getTotalPayableAmount(Connection connection, Integer claimNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ClaimCalculationSheetMainDto calSheet = null;
        BigDecimal total = BigDecimal.ZERO;
        try {
            ps = connection.prepareStatement(SELECT_TOT_PAYABLE_AMOUNT);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                total = null == rs.getBigDecimal("tot") ? BigDecimal.ZERO : rs.getBigDecimal("tot");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return total;
    }

    @Override
    public ClaimCalculationSheetMainDto getPendingBalanceCalsheet(Connection connection, Integer claimNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ClaimCalculationSheetMainDto calSheet = null;
        try {
            ps = connection.prepareStatement(SELECT_PENDING_BALANCE_CALSHEET);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                calSheet = new ClaimCalculationSheetMainDto();
                calSheet.setCalSheetId(rs.getInt("t1.N_CAL_SHEET_ID"));
                calSheet.setStatus(rs.getInt("t1.V_STATUS"));
                calSheet.setVoucherGeneratedUserId(rs.getString("t1.V_VOUCHER_GENERATED_USER"));
                calSheet.setInputUser(rs.getString("t1.V_INPUT_USER"));
                calSheet.setAssignUserId(rs.getString("t1.V_ASSIGN_USER_ID"));
                calSheet.setAprUserId(rs.getString("t1.V_APR_USER"));
                calSheet.setAprAssignUser(rs.getString("t1.V_APR_ASSIGN_USER"));
                calSheet.setSparePartCordinatorAssignUserId(rs.getString("t1.V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID"));
                calSheet.setScrutinizeTeamAssignUserId(rs.getString("t1.V_SCRUTINIZE_TEAM_ASSIGN_USER_ID"));
                calSheet.setSpecialTeamAssignUserId(rs.getString("t1.V_SPECIAL_TEAM_ASSIGN_USER_ID"));
                calSheet.setSpecialTeamMofaAssignUserId(rs.getString("t1.V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID"));
                calSheet.setReleaseOrderGenerateUserId(rs.getString("t1.V_RELEASE_ORDER_GENERATE_USER_ID"));
                calSheet.setCalSheetTypeDesc(rs.getString("t2.V_CAL_SHEET_TYPE_DESC"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return calSheet;
    }

    @Override
    public String getRteAction(Connection connection, Integer calsheetId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        String action = null;
        try {
            ps = connection.prepareStatement(GET_RTE_ACTION);
            ps.setInt(1, calsheetId);
            rs = ps.executeQuery();
            if (rs.next()) {
                action = rs.getString("V_RTE_ACTION");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return action;
    }

    @Override
    public void updateMotorEngineer(Connection connection, Integer calSheetId, String rteAssignUserId, String curDate) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_MOTOR_ENGINEER);
            ps.setInt(1, ClaimStatus.FORWARDED_TO_THE_MOTOR_ENGINEERING_TEAM_FOR_RESERVE_AMOUNT_APPROVAL.getClaimStatus());
            ps.setString(2, rteAssignUserId);
            ps.setString(3, curDate);
            ps.setInt(4, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer isAvailableCalSheetByType(Connection connection, Integer claimNo, Integer calSheetType) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        Integer isAvailableCalsheetByType = 0;
        try {
            ps = connection.prepareStatement(IS_AVAILABLE_CAL_SHEET_BY_TYPE);
            ps.setInt(1, calSheetType);
            ps.setInt(2, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                isAvailableCalsheetByType = rs.getInt("N_CAL_SHEET_ID");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return isAvailableCalsheetByType;
    }

    @Override
    public BigDecimal getTotalPayableAmount(Connection connection, Integer claimNo, Integer calSheetId) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        BigDecimal total = BigDecimal.ZERO;
        try {
            ps = connection.prepareStatement(SELECT_TOT_PAID_AMOUNT);
            ps.setInt(1, claimNo);
            ps.setInt(2, calSheetId);
            rs = ps.executeQuery();
            if (rs.next()) {
                total = null == rs.getBigDecimal("tot") ? BigDecimal.ZERO : rs.getBigDecimal("tot");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return total;
    }

    @Override
    public String getAssignRte(Connection connection, Integer calSheetId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        String rte = null;
        try {
            ps = connection.prepareStatement(GET_ASSIGN_RTE);
            ps.setInt(1, calSheetId);
            rs = ps.executeQuery();
            if (rs.next()) {
                rte = rs.getString("V_RTE_ASSIGN_USER_ID");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return rte;
    }

    @Override
    public boolean isHavingVoucherGeneratedCalsheetForDo(Connection connection, Integer supplyOrderRefNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(IS_VOU_GEN_CAL_SHEET_FOR_DO);
            ps.setInt(1, supplyOrderRefNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return false;
    }

    @Override
    public boolean getIsLoss(Connection connection, Integer calSheetId) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        boolean isLoss = false;
        try {
            ps = connection.prepareStatement(IS_LOSS_CALSHEET);
            ps.setInt(1, calSheetId);
            rs = ps.executeQuery();
            while (rs.next()) {
                isLoss = true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return isLoss;
    }

    @Override
    public String getPolicyChannelType(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        String policyChannelType = AppConstant.STRING_EMPTY;
        try {
            ps = connection.prepareStatement(GET_POLICY_CHANNEL_TYPE_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                policyChannelType = rs.getString("V_POLICY_CHANNEL_TYPE");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return policyChannelType;
    }

    @Override
    public void updateClaimNumberByTxnId(Connection connection, Integer claimNo, Integer claimStatus) throws Exception {
        PreparedStatement ps;

        try{
            ps=connection.prepareStatement(UPDATE_CLAIM_STATUS_BY_TXNID);
            ps.setString(1, String.valueOf(claimStatus));
            ps.setString(2, String.valueOf(claimNo));
            boolean s= ps.executeUpdate() > 0;
        }catch (Exception e){
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer searchByClaimNoForBillChecking(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        Integer calSheetId = AppConstant.ZERO_INT;
        try {
            ps = connection.prepareStatement(CALSHEET_FOR_BILL_CHECKING);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                calSheetId = rs.getInt("N_CAL_SHEET_ID");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return calSheetId;
    }

    @Override
    public boolean checkPendingPayment(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        int pendingStatus = AppConstant.ZERO_INT;
        try {
            ps = connection.prepareStatement(IS_PENDING_PAYMENT);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (ps != null) {
                ps.close();
            }
            if (rs != null) {
                rs.close();
            }
        }
        return false;
    }
}
