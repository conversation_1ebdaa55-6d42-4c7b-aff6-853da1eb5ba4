package com.misyn.mcms.claim.dto;

import com.misyn.mcms.claim.enums.FileTypeEnum;
import com.misyn.mcms.utility.AppConstant;

import java.io.InputStream;
import java.io.Serializable;
public class ClaimDocumentDto implements Serializable {
    private Integer refNo = AppConstant.ZERO_INT;
    private Integer jobRefNo = AppConstant.ZERO_INT;
    private Integer claimNo = AppConstant.ZERO_INT;
    private Integer documentTypeId = AppConstant.ZERO_INT;
    private Integer departmentId = AppConstant.ZERO_INT;
    private String isMandatory = AppConstant.NO;
    private String documentStatus = AppConstant.STRING_PENDING;
    private String documentPath = AppConstant.STRING_EMPTY;
    private String documentName = AppConstant.STRING_EMPTY;
    private String isDocumentUpload = AppConstant.NO;

    private String isCheck = AppConstant.NO;

    private String checkUser = AppConstant.NO;

    private String checkDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String holdUser = AppConstant.STRING_EMPTY;
    private String holdDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String rejectUser = AppConstant.STRING_EMPTY;
    private String rejectDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String billSummaryRemark = AppConstant.STRING_EMPTY;
    private String billSummaryCheckUser = AppConstant.STRING_EMPTY;
    private String billSummaryCheckDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String remark = AppConstant.STRING_EMPTY;
    private String inpStat = "I";
    private String inpUser = AppConstant.STRING_EMPTY;
    private String inpDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String doSerialNo = AppConstant.DEFAULT_DATE_TIME;

    private InputStream inputStream = null;
    private FileTypeEnum fileTypeEnum = FileTypeEnum.PDF;
    private String fileExtension = AppConstant.STRING_EMPTY;
    private String toolTip = AppConstant.STRING_EMPTY;
    private Integer calculationSheetNo = 0;
    private Integer billCheckRefNo = 0;

    private String cancelUser = AppConstant.STRING_EMPTY;
    private String cancelDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String cancelRemark = AppConstant.STRING_EMPTY;

    private ClaimDocumentTypeDto claimDocumentTypeDto = new ClaimDocumentTypeDto();
    private String isBankDetails= AppConstant.NO;

    public String getFileExtension() {
        return fileExtension;
    }

    public void setFileExtension(String fileExtension) {
        this.fileExtension = fileExtension;
    }

    public Integer getRefNo() {
        return refNo;
    }

    public void setRefNo(Integer refNo) {
        this.refNo = refNo;
    }

    public Integer getJobRefNo() {
        return jobRefNo;
    }

    public void setJobRefNo(Integer jobRefNo) {
        this.jobRefNo = jobRefNo;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public Integer getDocumentTypeId() {
        return documentTypeId;
    }

    public void setDocumentTypeId(Integer documentTypeId) {
        this.documentTypeId = documentTypeId;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public String getIsMandatory() {
        return isMandatory;
    }

    public void setIsMandatory(String isMandatory) {
        this.isMandatory = isMandatory;
    }

    public String getDocumentStatus() {
        return documentStatus;
    }

    public void setDocumentStatus(String documentStatus) {
        this.documentStatus = documentStatus;
    }

    public String getDocumentPath() {
        return documentPath;
    }

    public void setDocumentPath(String documentPath) {
        this.documentPath = documentPath;
    }

    public String getDocumentName() {
        return documentName;
    }

    public void setDocumentName(String documentName) {
        this.documentName = documentName;
    }

    public String getIsDocumentUpload() {
        return isDocumentUpload;
    }

    public void setIsDocumentUpload(String isDocumentUpload) {
        this.isDocumentUpload = isDocumentUpload;
    }

    public String getIsCheck() {
        return isCheck;
    }

    public void setIsCheck(String isCheck) {
        this.isCheck = isCheck;
    }

    public String getCheckUser() {
        return checkUser;
    }

    public void setCheckUser(String checkUser) {
        this.checkUser = checkUser;
    }

    public String getCheckDateTime() {
        return checkDateTime;
    }

    public void setCheckDateTime(String checkDateTime) {
        this.checkDateTime = checkDateTime;
    }

    public String getHoldUser() {
        return holdUser;
    }

    public void setHoldUser(String holdUser) {
        this.holdUser = holdUser;
    }

    public String getHoldDateTime() {
        return holdDateTime;
    }

    public void setHoldDateTime(String holdDateTime) {
        this.holdDateTime = holdDateTime;
    }

    public String getRejectUser() {
        return rejectUser;
    }

    public void setRejectUser(String rejectUser) {
        this.rejectUser = rejectUser;
    }

    public String getRejectDateTime() {
        return rejectDateTime;
    }

    public void setRejectDateTime(String rejectDateTime) {
        this.rejectDateTime = rejectDateTime;
    }

    public String getBillSummaryRemark() {
        return billSummaryRemark;
    }

    public void setBillSummaryRemark(String billSummaryRemark) {
        this.billSummaryRemark = billSummaryRemark;
    }

    public String getBillSummaryCheckUser() {
        return billSummaryCheckUser;
    }

    public void setBillSummaryCheckUser(String billSummaryCheckUser) {
        this.billSummaryCheckUser = billSummaryCheckUser;
    }

    public String getBillSummaryCheckDateTime() {
        return billSummaryCheckDateTime;
    }

    public void setBillSummaryCheckDateTime(String billSummaryCheckDateTime) {
        this.billSummaryCheckDateTime = billSummaryCheckDateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getInpStat() {
        return inpStat;
    }

    public void setInpStat(String inpStat) {
        this.inpStat = inpStat;
    }

    public String getInpUser() {
        return inpUser;
    }

    public void setInpUser(String inpUser) {
        this.inpUser = inpUser;
    }

    public String getInpDateTime() {
        return inpDateTime;
    }

    public void setInpDateTime(String inpDateTime) {
        this.inpDateTime = inpDateTime;
    }

    public InputStream getInputStream() {
        return inputStream;
    }

    public void setInputStream(InputStream inputStream) {
        this.inputStream = inputStream;
    }

    public FileTypeEnum getFileTypeEnum() {
        return fileTypeEnum;
    }

    public void setFileTypeEnum(FileTypeEnum fileTypeEnum) {
        this.fileTypeEnum = fileTypeEnum;
    }

    public ClaimDocumentTypeDto getClaimDocumentTypeDto() {
        return claimDocumentTypeDto;
    }

    public void setClaimDocumentTypeDto(ClaimDocumentTypeDto claimDocumentTypeDto) {
        this.claimDocumentTypeDto = claimDocumentTypeDto;
    }

    public String getToolTip() {
        return toolTip;
    }

    public void setToolTip(String toolTip) {
        this.toolTip = toolTip;
    }

    public Integer getCalculationSheetNo() {
        return calculationSheetNo;
    }

    public void setCalculationSheetNo(Integer calculationSheetNo) {
        this.calculationSheetNo = calculationSheetNo;
    }

    public Integer getBillCheckRefNo() {
        return billCheckRefNo;
    }

    public void setBillCheckRefNo(Integer billCheckRefNo) {
        this.billCheckRefNo = billCheckRefNo;
    }

    public String getCancelUser() {
        return cancelUser;
    }

    public void setCancelUser(String cancelUser) {
        this.cancelUser = cancelUser;
    }

    public String getCancelDateTime() {
        return cancelDateTime;
    }

    public void setCancelDateTime(String cancelDateTime) {
        this.cancelDateTime = cancelDateTime;
    }

    public String getCancelRemark() {
        return cancelRemark;
    }

    public void setCancelRemark(String cancelRemark) {
        this.cancelRemark = cancelRemark;
    }

    public String getIsBankDetails() {
        return isBankDetails;
    }

    public void setIsBankDetails(String isBankDetails) {
        this.isBankDetails = isBankDetails;
    }

    public String getDoSerialNo() {
        return doSerialNo;
    }

    public void setDoSerialNo(String doSerialNo) {
        this.doSerialNo = doSerialNo;
    }
}
