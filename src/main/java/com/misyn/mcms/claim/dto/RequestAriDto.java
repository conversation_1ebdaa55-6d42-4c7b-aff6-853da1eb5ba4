package com.misyn.mcms.claim.dto;

import com.misyn.mcms.claim.enums.ConditionType;
import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class RequestAriDto implements Serializable {
    private Integer claimNo = AppConstant.ZERO_INT;
    private String vehicleNo = AppConstant.STRING_EMPTY;
    private String customerName = AppConstant.STRING_EMPTY;
    private String contactNo = AppConstant.STRING_EMPTY;
    private String accidentDate = AppConstant.STRING_EMPTY;
    private String requestedUser = AppConstant.STRING_EMPTY;
    private String requestedDate = AppConstant.STRING_EMPTY;
    private String assignedAssessor = AppConstant.STRING_EMPTY;
    private String address1 = AppConstant.STRING_EMPTY;
    private String address2 = AppConstant.STRING_EMPTY;
    private String address3 = AppConstant.STRING_EMPTY;
    private String inpUserId = AppConstant.STRING_EMPTY;
    private String inpDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String assignedAssessorCode = AppConstant.STRING_EMPTY;
    private ConditionType thirdPartyInvoled = ConditionType.No;
    private String status = AppConstant.STRING_EMPTY;
    private String remark = AppConstant.STRING_EMPTY;
    private int id = AppConstant.ZERO_INT;
    private int refId = AppConstant.ZERO_INT;
    private Integer index = AppConstant.ZERO_INT;
    private String assigningAssessorCode = AppConstant.STRING_EMPTY;
    private String rteCode = AppConstant.STRING_EMPTY;
    private String revokeUser = AppConstant.STRING_EMPTY;
    private String revokeDate = AppConstant.DEFAULT_DATE;
    private String refNo = AppConstant.STRING_EMPTY;
    private String assessorSubmittedDate = AppConstant.DEFAULT_DATE_TIME;
    private String assigningDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String documentUploadUser = AppConstant.STRING_EMPTY;
    private Integer daysFromRequest = AppConstant.ZERO_INT;
    private Integer requestAriReason = AppConstant.ZERO_INT;
    private String daysFromAssignment = AppConstant.STRING_EMPTY;
    private String priority;

    //akila gothaya moda haraka mee haraka mati puusa raa bada

    //heeeeee ;)

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getContactNo() {
        return contactNo;
    }

    public void setContactNo(String contactNo) {
        this.contactNo = contactNo;
    }

    public String getAccidentDate() {
        return accidentDate;
    }

    public void setAccidentDate(String accidentDate) {
        this.accidentDate = accidentDate;
    }

    public String getRequestedUser() {
        return requestedUser;
    }

    public void setRequestedUser(String requestedUser) {
        this.requestedUser = requestedUser;
    }

    public String getRequestedDate() {
        return requestedDate;
    }

    public void setRequestedDate(String requestedDate) {
        this.requestedDate = requestedDate;
    }

    public String getAssignedAssessor() {
        return assignedAssessor;
    }

    public void setAssignedAssessor(String assignedAssessor) {
        this.assignedAssessor = assignedAssessor;
    }

    public String getAddress1() {
        return address1;
    }

    public void setAddress1(String address1) {
        this.address1 = address1;
    }

    public String getAddress2() {
        return address2;
    }

    public void setAddress2(String address2) {
        this.address2 = address2;
    }

    public String getAddress3() {
        return address3;
    }

    public void setAddress3(String address3) {
        this.address3 = address3;
    }

    public String getInpUserId() {
        return inpUserId;
    }

    public void setInpUserId(String inpUserId) {
        this.inpUserId = inpUserId;
    }

    public String getInpDateTime() {
        return inpDateTime;
    }

    public void setInpDateTime(String inpDateTime) {
        this.inpDateTime = inpDateTime;
    }

    public String getAssignedAssessorCode() {
        return assignedAssessorCode;
    }

    public void setAssignedAssessorCode(String assignedAssessorCode) {
        this.assignedAssessorCode = assignedAssessorCode;
    }

    public ConditionType getThirdPartyInvoled() {
        return thirdPartyInvoled;
    }

    public void setThirdPartyInvoled(ConditionType thirdPartyInvoled) {
        this.thirdPartyInvoled = thirdPartyInvoled;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getRefId() {
        return refId;
    }

    public void setRefId(int refId) {
        this.refId = refId;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getAssigningAssessorCode() {
        return assigningAssessorCode;
    }

    public void setAssigningAssessorCode(String assigningAssessorCode) {
        this.assigningAssessorCode = assigningAssessorCode;
    }

    public String getRteCode() {
        return rteCode;
    }

    public void setRteCode(String rteCode) {
        this.rteCode = rteCode;
    }

    public String getRevokeUser() {
        return revokeUser;
    }

    public void setRevokeUser(String revokeUser) {
        this.revokeUser = revokeUser;
    }

    public String getRevokeDate() {
        return revokeDate;
    }

    public void setRevokeDate(String revokeDate) {
        this.revokeDate = revokeDate;
    }

    public String getRefNo() {
        return refNo;
    }

    public void setRefNo(String refNo) {
        this.refNo = refNo;
    }

    public String getAssessorSubmittedDate() {
        return assessorSubmittedDate;
    }

    public void setAssessorSubmittedDate(String assessorSubmittedDate) {
        this.assessorSubmittedDate = assessorSubmittedDate;
    }

    public String getAssigningDateTime() {
        return assigningDateTime;
    }

    public void setAssigningDateTime(String assigningDateTime) {
        this.assigningDateTime = assigningDateTime;
    }

    public String getDocumentUploadUser() {
        return documentUploadUser;
    }

    public void setDocumentUploadUser(String documentUploadUser) {
        this.documentUploadUser = documentUploadUser;
    }

    public Integer getDaysFromRequest() {
        return daysFromRequest;
    }

    public void setDaysFromRequest(Integer daysFromRequest) {
        this.daysFromRequest = daysFromRequest;
    }

    public Integer getRequestAriReason() {
        return requestAriReason;
    }

    public void setRequestAriReason(Integer requestAriReason) {
        this.requestAriReason = requestAriReason;
    }

    public String getDaysFromAssignment() {
        return daysFromAssignment;
    }

    public void setDaysFromAssignment(String daysFromAssignment) {
        this.daysFromAssignment = daysFromAssignment;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }
}
