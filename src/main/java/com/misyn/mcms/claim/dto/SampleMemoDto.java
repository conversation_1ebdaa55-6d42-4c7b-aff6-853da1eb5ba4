package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class SampleMemoDto implements Serializable {

    private String no = AppConstant.STRING_EMPTY;
    private String memo = AppConstant.STRING_EMPTY;
    private String date = AppConstant.STRING_EMPTY;
    private String exclusion = AppConstant.STRING_EMPTY;
    private String details = AppConstant.STRING_EMPTY;

    public SampleMemoDto(String no, String memo, String date, String exclusion, String details) {
        this.no = no;
        this.memo = memo;
        this.date = date;
        this.exclusion = exclusion;
        this.details = details;
    }

    public String getNo() {
        return no;
    }

    public void setNo(String no) {
        this.no = no;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getExclusion() {
        return exclusion;
    }

    public void setExclusion(String exclusion) {
        this.exclusion = exclusion;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }
}


