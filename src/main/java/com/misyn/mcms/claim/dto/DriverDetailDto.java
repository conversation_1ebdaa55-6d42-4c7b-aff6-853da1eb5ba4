package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class DriverDetailDto implements Serializable {
    private Integer claimNo = 0;
    private Integer driverStatus = AppConstant.ZERO_INT;
    private Integer driverTitle = AppConstant.ZERO_INT;
    private String driverName = AppConstant.STRING_EMPTY;
    private Integer driverReleshipInsurd = AppConstant.ZERO_INT;
    private String driverNic = AppConstant.STRING_EMPTY;
    private String dlNo = AppConstant.STRING_EMPTY;
    private String driverLicenceType = AppConstant.STRING_EMPTY;
    private String reporterRemark = AppConstant.STRING_EMPTY;
    private String driverDetailSubmit = AppConstant.NO;
    private String driverDetailNotRelevant = AppConstant.NO;

    public DriverDetailDto() {
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public Integer getDriverStatus() {
        return driverStatus;
    }

    public void setDriverStatus(Integer driverStatus) {
        this.driverStatus = driverStatus;
    }

    public Integer getDriverTitle() {
        return driverTitle;
    }

    public void setDriverTitle(Integer driverTitle) {
        this.driverTitle = driverTitle;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public Integer getDriverReleshipInsurd() {
        return driverReleshipInsurd;
    }

    public void setDriverReleshipInsurd(Integer driverReleshipInsurd) {
        this.driverReleshipInsurd = driverReleshipInsurd;
    }

    public String getDriverNic() {
        return driverNic;
    }

    public void setDriverNic(String driverNic) {
        this.driverNic = driverNic;
    }

    public String getDlNo() {
        return dlNo;
    }

    public void setDlNo(String dlNo) {
        this.dlNo = dlNo;
    }

    public String getReporterRemark() {
        return reporterRemark;
    }

    public void setReporterRemark(String reporterRemark) {
        this.reporterRemark = reporterRemark;
    }

    public String getDriverDetailSubmit() {
        return driverDetailSubmit;
    }

    public void setDriverDetailSubmit(String driverDetailSubmit) {
        this.driverDetailSubmit = driverDetailSubmit;
    }

    public String getDriverDetailNotRelevant() {
        return driverDetailNotRelevant;
    }

    public void setDriverDetailNotRelevant(String driverDetailNotRelevant) {
        this.driverDetailNotRelevant = driverDetailNotRelevant;
    }

    public String getDriverLicenceType() {
        return driverLicenceType;
    }

    public void setDriverLicenceType(String driverLicenseType) {
        this.driverLicenceType = driverLicenseType;
    }
}
