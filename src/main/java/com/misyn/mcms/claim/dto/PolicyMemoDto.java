package com.misyn.mcms.claim.dto;

import java.io.Serializable;
public class PolicyMemoDto implements Serializable {
    private Integer txnRefNo;
    private String policyNo;
    private String memo;
    private String position;
    private String flag;
    private String exclusion;
    private Integer order;
    private String memoDate;
    private String delete;


    public Integer getTxnRefNo() {
        return txnRefNo;
    }

    public void setTxnRefNo(Integer txnRefNo) {
        this.txnRefNo = txnRefNo;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getExclusion() {
        return exclusion;
    }

    public void setExclusion(String exclusion) {
        this.exclusion = exclusion;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getMemoDate() {
        return memoDate;
    }

    public void setMemoDate(String memoDate) {
        this.memoDate = memoDate;
    }

    public String getDelete() {return delete;}

    public void setDelete(String delete) {this.delete = delete;}
}
