/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class NotificationDto implements Serializable {

    private Integer txnId;
    private int claimNo;
    private String inpUserId = AppConstant.STRING_EMPTY;
    private String message = AppConstant.STRING_EMPTY;
    private String readStatus = AppConstant.NO;
    private String readDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String priorityStatus = "LOW";
    private String notifyDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String ipAddress = AppConstant.STRING_EMPTY;
    private Integer jobNo = AppConstant.ZERO_INT;
    private Integer polRefNo = AppConstant.ZERO_INT;
    private String assignUserId = AppConstant.STRING_EMPTY;
    private Integer refNo = AppConstant.ZERO_INT;
    private String url = AppConstant.STRING_EMPTY;
    private String vehicleNo = AppConstant.STRING_EMPTY;
    private String coverNoteNo = AppConstant.STRING_EMPTY;
    private String accidentDate = AppConstant.DEFAULT_DATE;
    private String checkedStatus = AppConstant.NO;
    private String isMobileRead = AppConstant.STRING_EMPTY;
    private String colorCode = "#FFFFFF";
    private String policyChannelType;
    private String productName;
    private String categoryDesc;


    public Integer getTxnId() {
        return txnId;
    }

    public void setTxnId(Integer txnId) {
        this.txnId = txnId;
    }

    public int getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(int claimNo) {
        this.claimNo = claimNo;
    }

    public String getInpUserId() {
        return inpUserId;
    }

    public void setInpUserId(String inpUserId) {
        this.inpUserId = inpUserId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getReadStatus() {
        return readStatus;
    }

    public void setReadStatus(String readStatus) {
        this.readStatus = readStatus;
    }

    public String getPriorityStatus() {
        return priorityStatus;
    }

    public void setPriorityStatus(String priorityStatus) {
        this.priorityStatus = priorityStatus;
    }

    public String getNotifyDateTime() {
        return notifyDateTime;
    }

    public void setNotifyDateTime(String notifyDateTime) {
        this.notifyDateTime = notifyDateTime;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getAssignUserId() {
        return assignUserId;
    }

    public void setAssignUserId(String assignUserId) {
        this.assignUserId = assignUserId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getReadDateTime() {
        return readDateTime;
    }

    public void setReadDateTime(String readDateTime) {
        this.readDateTime = readDateTime;
    }

    public Integer getJobNo() {
        return jobNo;
    }

    public void setJobNo(Integer jobNo) {
        this.jobNo = jobNo;
    }

    public Integer getPolRefNo() {
        return polRefNo;
    }

    public void setPolRefNo(Integer polRefNo) {
        this.polRefNo = polRefNo;
    }

    public Integer getRefNo() {
        return refNo;
    }

    public void setRefNo(Integer refNo) {
        this.refNo = refNo;
    }

    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    public String getCoverNoteNo() {
        return coverNoteNo;
    }

    public void setCoverNoteNo(String coverNoteNo) {
        this.coverNoteNo = coverNoteNo;
    }

    public String getAccidentDate() {
        return accidentDate;
    }

    public void setAccidentDate(String accidentDate) {
        this.accidentDate = accidentDate;
    }

    public String getCheckedStatus() {
        return checkedStatus;
    }

    public void setCheckedStatus(String checkedStatus) {
        this.checkedStatus = checkedStatus;
    }

    public String getIsMobileRead() {
        return isMobileRead;
    }

    public void setIsMobileRead(String isMobileRead) {
        this.isMobileRead = isMobileRead;
    }

    public String getColorCode() {
        return colorCode;
    }

    public void setColorCode(String colorCode) {
        this.colorCode = colorCode;
    }

    public String getPolicyChannelType() {
        return policyChannelType;
    }

    public void setPolicyChannelType(String policyChannelType) {
        this.policyChannelType = policyChannelType;
    }

    public String getProductName() { return productName; }

    public void setProductName(String productName) { this.productName = productName; }

    public String getCategoryDesc() { return categoryDesc; }

    public void setCategoryDesc(String categoryDesc) { this.categoryDesc = categoryDesc; }
}
