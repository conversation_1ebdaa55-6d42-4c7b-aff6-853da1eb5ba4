package com.misyn.mcms.claim.dto;

import java.io.Serializable;
public class AgentGarageDto implements Serializable {

    private Integer id;
    private String gargCode;
    private String gargName;
    private String address1;
    private String address2;
    private String address3;
    private String conPerson;
    private String conNumber;
    private String genTelNo;
    private String status;
    private int index;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getGargCode() {
        return gargCode;
    }

    public void setGargCode(String gargCode) {
        this.gargCode = gargCode;
    }

    public String getGargName() {
        return gargName;
    }

    public void setGargName(String gargName) {
        this.gargName = gargName;
    }

    public String getAddress1() {
        return address1;
    }

    public void setAddress1(String address1) {
        this.address1 = address1;
    }

    public String getAddress2() {
        return address2;
    }

    public void setAddress2(String address2) {
        this.address2 = address2;
    }

    public String getAddress3() {
        return address3;
    }

    public void setAddress3(String address3) {
        this.address3 = address3;
    }

    public String getConPerson() {
        return conPerson;
    }

    public void setConPerson(String conPerson) {
        this.conPerson = conPerson;
    }

    public String getConNumber() {
        return conNumber;
    }

    public void setConNumber(String conNumber) {
        this.conNumber = conNumber;
    }

    public String getGenTelNo() {
        return genTelNo;
    }

    public void setGenTelNo(String genTelNo) {
        this.genTelNo = genTelNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }
}
