package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Time;
import java.time.LocalDateTime;

public class AssessorFeeDto implements Serializable {
    private Integer assessorFeeDetailId;
    private Time fromTime;
    private Time toTime;
    private Integer inspectionTypeId;
    private Integer dayTypeId;
    private BigDecimal permanentStaffFee;
    private BigDecimal hybridStaffFee;
    private String inspectionTypeDescription;
    private String dayTypeDescription;

    private String recordStatus;
    private LocalDateTime inputDateTime;
    private String inputUser;
    private LocalDateTime lastModifiedDateTime;
    private String lastModifiedUser;

    public Integer getAssessorFeeDetailId() {
        return assessorFeeDetailId;
    }

    public void setAssessorFeeDetailId(Integer assessorFeeDetailId) {
        this.assessorFeeDetailId = assessorFeeDetailId;
    }

    public Time getFromTime() {
        return fromTime;
    }

    public void setFromTime(Time fromTime) {
        this.fromTime = fromTime;
    }

    public Time getToTime() {
        return toTime;
    }

    public void setToTime(Time toTime) {
        this.toTime = toTime;
    }

    public Integer getInspectionTypeId() {
        return inspectionTypeId;
    }

    public void setInspectionTypeId(Integer inspectionTypeId) {
        this.inspectionTypeId = inspectionTypeId;
    }

    public Integer getDayTypeId() {
        return dayTypeId;
    }

    public void setDayTypeId(Integer dayTypeId) {
        this.dayTypeId = dayTypeId;
    }

    public BigDecimal getPermanentStaffFee() {
        return permanentStaffFee;
    }

    public void setPermanentStaffFee(BigDecimal permanentStaffFee) {
        this.permanentStaffFee = permanentStaffFee;
    }

    public BigDecimal getHybridStaffFee() {
        return hybridStaffFee;
    }

    public void setHybridStaffFee(BigDecimal hybridStaffFee) {
        this.hybridStaffFee = hybridStaffFee;
    }

    public String getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
    }

    public LocalDateTime getInputDateTime() {
        return inputDateTime;
    }

    public void setInputDateTime(LocalDateTime inputDateTime) {
        this.inputDateTime = inputDateTime;
    }

    public String getInputUser() {
        return inputUser;
    }

    public void setInputUser(String inputUser) {
        this.inputUser = inputUser;
    }

    public LocalDateTime getLastModifiedDateTime() {
        return lastModifiedDateTime;
    }

    public void setLastModifiedDateTime(LocalDateTime lastModifiedDateTime) {
        this.lastModifiedDateTime = lastModifiedDateTime;
    }

    public String getLastModifiedUser() {
        return lastModifiedUser;
    }

    public void setLastModifiedUser(String lastModifiedUser) {
        this.lastModifiedUser = lastModifiedUser;
    }

    public String getInspectionTypeDescription() {
        return inspectionTypeDescription;
    }

    public void setInspectionTypeDescription(String inspectionTypeDescription) {
        this.inspectionTypeDescription = inspectionTypeDescription;
    }

    public String getDayTypeDescription() {
        return dayTypeDescription;
    }

    public void setDayTypeDescription(String dayTypeDescription) {
        this.dayTypeDescription = dayTypeDescription;
    }
}
