package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class TireCondtionDto implements Serializable {
    private ClaimsDto claimsDto = new ClaimsDto();
    private int refNo = 0;
    private int position = 0;
    private String rf = AppConstant.STRING_EMPTY;
    private String lf = AppConstant.STRING_EMPTY;
    private String rr = AppConstant.STRING_EMPTY;
    private String rl = AppConstant.STRING_EMPTY;
    private String rri = AppConstant.STRING_EMPTY;
    private String lri = AppConstant.STRING_EMPTY;
    private String other = AppConstant.STRING_EMPTY;

    public ClaimsDto getClaimsDto() {
        return claimsDto;
    }

    public void setClaimsDto(ClaimsDto claimsDto) {
        this.claimsDto = claimsDto;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public String getRf() {
        return rf;
    }

    public void setRf(String rf) {
        this.rf = rf;
    }

    public String getLf() {
        return lf;
    }

    public void setLf(String lf) {
        this.lf = lf;
    }

    public String getRr() {
        return rr;
    }

    public void setRr(String rr) {
        this.rr = rr;
    }

    public String getRl() {
        return rl;
    }

    public void setRl(String rl) {
        this.rl = rl;
    }

    public String getRri() {
        return rri;
    }

    public void setRri(String rri) {
        this.rri = rri;
    }

    public String getLri() {
        return lri;
    }

    public void setLri(String lri) {
        this.lri = lri;
    }

    public String getOther() {
        return other;
    }

    public void setOther(String other) {
        this.other = other;
    }

    public int getRefNo() {
        return refNo;
    }

    public void setRefNo(int refNo) {
        this.refNo = refNo;
    }
}
