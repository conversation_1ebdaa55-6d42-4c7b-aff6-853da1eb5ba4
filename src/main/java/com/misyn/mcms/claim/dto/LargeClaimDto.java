package com.misyn.mcms.claim.dto;

import java.math.BigDecimal;
public class LargeClaimDto {
    private BigDecimal retention;
    private BigDecimal qs;
    private BigDecimal fac;
    private BigDecimal coins;

    public LargeClaimDto() {
    }

    public LargeClaimDto(BigDecimal retention, BigDecimal qs, BigDecimal fac, BigDecimal coins) {
        this.retention = retention;
        this.qs = qs;
        this.fac = fac;
        this.coins = coins;
    }

    public BigDecimal getRetention() {
        return retention;
    }

    public void setRetention(BigDecimal retention) {
        this.retention = retention;
    }

    public BigDecimal getQs() {
        return qs;
    }

    public void setQs(BigDecimal qs) {
        this.qs = qs;
    }

    public BigDecimal getFac() {
        return fac;
    }

    public void setFac(BigDecimal fac) {
        this.fac = fac;
    }

    public BigDecimal getCoins() {
        return coins;
    }

    public void setCoins(BigDecimal coins) {
        this.coins = coins;
    }
}
