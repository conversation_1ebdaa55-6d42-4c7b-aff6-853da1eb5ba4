package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class ClaimLogTrailDto implements Serializable {
    private Integer id;
    private Integer claimNo;
    private Integer formNameId;
    private Integer versionId;
    private String fieldName;
    private String fieldValue;
    private String userId;
    private String inputDateTime;
    private Integer jobRefNo = AppConstant.ZERO_INT;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public Integer getFormNameId() {
        return formNameId;
    }

    public void setFormNameId(Integer formNameId) {
        this.formNameId = formNameId;
    }

    public Integer getVersionId() {
        return versionId;
    }

    public void setVersionId(Integer versionId) {
        this.versionId = versionId;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getFieldValue() {
        return fieldValue;
    }

    public void setFieldValue(String fieldValue) {
        this.fieldValue = fieldValue;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getInputDateTime() {
        return inputDateTime;
    }

    public void setInputDateTime(String inputDateTime) {
        this.inputDateTime = inputDateTime;
    }

    public Integer getJobRefNo() {
        return jobRefNo;
    }

    public void setJobRefNo(Integer jobRefNo) {
        this.jobRefNo = jobRefNo;
    }
}
