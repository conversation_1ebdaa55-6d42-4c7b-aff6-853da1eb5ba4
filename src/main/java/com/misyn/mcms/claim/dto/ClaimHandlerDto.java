package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
public class ClaimHandlerDto implements Serializable {
    private Integer txnNo;
    private Integer claimNo;
    private Integer teamId;
    private String assignUserId;
    private String assignStatus = AppConstant.STRING_PENDING;
    private String assignDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String initLiabilityAssignUserId;
    private String initLiabilityAssignDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String initLiabilityAprvUserId;
    private String initLiabilityAprvStatus;
    private String initLiabilityAprvDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String liabilityAprvAssignUser;
    private String liabilityAprvAssignDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String liabilityAprvUser;
    private String liabilityAprvStatus;
    private String liabilityAprvDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String managerUserId;
    private String channelCode;
    private Integer accessUserType;
    private Integer claimStatus;
    private BigDecimal reserveAmount = BigDecimal.ZERO;
    private String claimHandlerMobileNo;

    private BigDecimal reserveAmountAfterAprv = BigDecimal.ZERO;
    private BigDecimal aprvTotAcrAmount = BigDecimal.ZERO;
    private String acrAprvUser = AppConstant.STRING_EMPTY;
    private BigDecimal engineerApprovedAmount = BigDecimal.ZERO;
    private Integer lossType;
    private String isAllDocUpload;
    private String isCheckAllMndDocs;
    private BigDecimal penaltyBaldTyre = BigDecimal.ZERO;
    private BigDecimal penaltyBaldTyreRate = BigDecimal.ZERO;
    private String penaltyBaldTyreUser;
    private String penaltyBaldTyreDateTime = AppConstant.DEFAULT_DATE_TIME;
    private BigDecimal penaltyUnderInsurce = BigDecimal.ZERO;
    private BigDecimal penaltyUnderInsurceRate = BigDecimal.ZERO;
    private String penaltyUnderInsurceUser;
    private String penaltyUnderInsurceDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String causeOfLoss = AppConstant.STRING_EMPTY;
    private String lossTypeDecs = AppConstant.STRING_EMPTY;
    private String claimStatusPara = AppConstant.STRING_EMPTY;
    private Integer rejectionLatterType = AppConstant.ZERO_INT;

    private String intimationChk;
    private String intimationChkUser;
    private String intimationChkDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String isLcChk1;
    private String isLcChk2;
    private String isLcChk3;
    private String isLcChk4;
    private String isLcChk5;
    private String isLcChk6;
    private String isLcChk7;
    private String isLcChk8;
    private String lcChkUser;
    private String lcChkDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String claimPanelAssignUsers;
    private String claimPanelAssignUserDateTime = AppConstant.DEFAULT_DATE_TIME;
    private Integer decisionApproveClaimPanel = AppConstant.ZERO_INT;
    private String claimPanelDecision;
    private String claimPanelDecisionDateTime = AppConstant.DEFAULT_DATE_TIME;
    private Integer repudiatedType;
    private String isPrintRepudiatedLetter;
    private String repudiatedLetterPrintUserId;
    private String repudiatedLetterPrintDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String financialInterest;
    private Integer leasingRefNo;
    private String finalizeUserId;
    private String finalizeDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String isGenarateSupplyOrder;
    private String supplyOrderAssignUser;
    private String supplyOrderAssignDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String supplyOrderCreateUser;
    private String supplyOrderCreateDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String supplyOrderCreateClose;
    private String isFileStore;
    private String fileUserStoreUserId;
    private String fileStoreDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String reopenAssignUserId;
    private String reopenAssignUserDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String reopenUserId;
    private String reopenDateTime = AppConstant.DEFAULT_DATE_TIME;
    private Integer reopenNoOfTime;
    private String isGenFinalRemindLetter = AppConstant.NO;
    private String genFinalRemindLetterDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String decisionMakingAssignUserId;
    private String decisionMakingAssignDateTime = AppConstant.DEFAULT_DATE_TIME;

    private String investigationStatus = AppConstant.NO;
    private String investigationAssignUserId;
    private String investigationAssignDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String investigationArrangeUserId;
    private String investigationArrangeDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String investigationCompletedUserId;
    private String investigationCompletedDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String specialApprovalInputUserId = AppConstant.STRING_EMPTY;
    private String specialApprovalInputDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String specialApprovalUserId = AppConstant.STRING_EMPTY;
    private String specialApprovalDateTime = AppConstant.DEFAULT_DATE_TIME;
    private Integer oldClaimStatus = AppConstant.ZERO_INT;

    private String isDoubt = AppConstant.NO;
    private String isOnSiteOffer = AppConstant.NO;
    private BigDecimal aprvAdvanceAmount = BigDecimal.ZERO;
    private String reOpenType = AppConstant.NO;
    private String closeStatus = AppConstant.CLOSE_STATUS_PENDING;
    private String closeUser = AppConstant.STRING_EMPTY;
    private String closeDateTime = AppConstant.DEFAULT_DATE_TIME;


    private String supplyOrderAssignStatus = AppConstant.NO;


    private String inpStatus;
    private String inpUserId;
    private String inpDateTime = AppConstant.DEFAULT_DATE_TIME;
    private Integer versionNo;
    private BigDecimal labourCost;
    private BigDecimal partCost;


    private ClaimsDto claimsDto = new ClaimsDto();
    private Integer processingStage;
    private Integer repudiatedLetterType = AppConstant.ZERO_INT;
    private String RepudiatedLetterPrintUserName;
    private String isExcessInclude = AppConstant.NO;
    private String isProvideOffer = AppConstant.NO;
    private String letterPanelUserId=AppConstant.STRING_EMPTY;

    private String advanceApprovalAssignUser = AppConstant.STRING_EMPTY;
    private String advanceApprovalAssignDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String advanceStatus = AppConstant.STRING_PENDING;
    private String advanceForwardedUser = AppConstant.STRING_EMPTY;
    private BigDecimal pendingAdvance = BigDecimal.ZERO;
    private String advanceApprovedUser = AppConstant.STRING_EMPTY;
    private String advanceApprovedDateTime = AppConstant.DEFAULT_DATE_TIME;

    private String forwardedEngineer = AppConstant.STRING_EMPTY;
    private String letterPanelDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String isRejectionAttached = AppConstant.NO;
    private String rejectionAttachedDateTime = AppConstant.DEFAULT_DATE_TIME;

    private Integer oldEngineerClaimStatus = AppConstant.ZERO_INT;


    public String getReOpenType() {
        return reOpenType;
    }

    public void setReOpenType(String reOpenType) {
        this.reOpenType = reOpenType;
    }

    public BigDecimal getAprvAdvanceAmount() {
        return aprvAdvanceAmount;
    }

    public void setAprvAdvanceAmount(BigDecimal aprvAdvanceAmount) {
        this.aprvAdvanceAmount = aprvAdvanceAmount;
    }

    public Integer getTxnNo() {
        return txnNo;
    }

    public void setTxnNo(Integer txnNo) {
        this.txnNo = txnNo;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public Integer getTeamId() {
        return teamId;
    }

    public void setTeamId(Integer teamId) {
        this.teamId = teamId;
    }

    public String getAssignUserId() {
        return assignUserId;
    }

    public void setAssignUserId(String assignUserId) {
        this.assignUserId = assignUserId;
    }

    public String getAssignStatus() {
        return assignStatus;
    }

    public void setAssignStatus(String assignStatus) {
        this.assignStatus = assignStatus;
    }

    public String getAssignDateTime() {
        return assignDateTime;
    }

    public void setAssignDateTime(String assignDateTime) {
        this.assignDateTime = assignDateTime;
    }

    public String getInitLiabilityAssignUserId() {
        return initLiabilityAssignUserId;
    }

    public void setInitLiabilityAssignUserId(String initLiabilityAssignUserId) {
        this.initLiabilityAssignUserId = initLiabilityAssignUserId;
    }

    public String getInitLiabilityAssignDateTime() {
        return initLiabilityAssignDateTime;
    }

    public void setInitLiabilityAssignDateTime(String initLiabilityAssignDateTime) {
        this.initLiabilityAssignDateTime = initLiabilityAssignDateTime;
    }

    public String getInitLiabilityAprvUserId() {
        return initLiabilityAprvUserId;
    }

    public void setInitLiabilityAprvUserId(String initLiabilityAprvUserId) {
        this.initLiabilityAprvUserId = initLiabilityAprvUserId;
    }

    public String getInitLiabilityAprvStatus() {
        return initLiabilityAprvStatus;
    }

    public void setInitLiabilityAprvStatus(String initLiabilityAprvStatus) {
        this.initLiabilityAprvStatus = initLiabilityAprvStatus;
    }

    public String getInitLiabilityAprvDateTime() {
        return initLiabilityAprvDateTime;
    }

    public void setInitLiabilityAprvDateTime(String initLiabilityAprvDateTime) {
        this.initLiabilityAprvDateTime = initLiabilityAprvDateTime;
    }

    public String getLiabilityAprvAssignUser() {
        return liabilityAprvAssignUser;
    }

    public void setLiabilityAprvAssignUser(String liabilityAprvAssignUser) {
        this.liabilityAprvAssignUser = liabilityAprvAssignUser;
    }

    public String getLiabilityAprvAssignDateTime() {
        return liabilityAprvAssignDateTime;
    }

    public void setLiabilityAprvAssignDateTime(String liabilityAprvAssignDateTime) {
        this.liabilityAprvAssignDateTime = liabilityAprvAssignDateTime;
    }

    public String getLiabilityAprvUser() {
        return liabilityAprvUser;
    }

    public void setLiabilityAprvUser(String liabilityAprvUser) {
        this.liabilityAprvUser = liabilityAprvUser;
    }

    public String getLiabilityAprvStatus() {
        return liabilityAprvStatus;
    }

    public void setLiabilityAprvStatus(String liabilityAprvStatus) {
        this.liabilityAprvStatus = liabilityAprvStatus;
    }

    public String getLiabilityAprvDateTime() {
        return liabilityAprvDateTime;
    }

    public void setLiabilityAprvDateTime(String liabilityAprvDateTime) {
        this.liabilityAprvDateTime = liabilityAprvDateTime;
    }

    public String getManagerUserId() {
        return managerUserId;
    }

    public void setManagerUserId(String managerUserId) {
        this.managerUserId = managerUserId;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public Integer getAccessUserType() {
        return accessUserType;
    }

    public void setAccessUserType(Integer accessUserType) {
        this.accessUserType = accessUserType;
    }

    public Integer getClaimStatus() {
        return claimStatus;
    }

    public void setClaimStatus(Integer claimStatus) {
        this.claimStatus = claimStatus;
    }

    public BigDecimal getReserveAmount() {
        return reserveAmount;
    }

    public void setReserveAmount(BigDecimal reserveAmount) {
        this.reserveAmount = reserveAmount;
    }

    public BigDecimal getReserveAmountAfterAprv() {
        return reserveAmountAfterAprv;
    }

    public void setReserveAmountAfterAprv(BigDecimal reserveAmountAfterAprv) {
        this.reserveAmountAfterAprv = reserveAmountAfterAprv;
    }

    public BigDecimal getAprvTotAcrAmount() {
        return aprvTotAcrAmount;
    }

    public void setAprvTotAcrAmount(BigDecimal aprvTotAcrAmount) {
        this.aprvTotAcrAmount = aprvTotAcrAmount;
    }

    public Integer getLossType() {
        return lossType;
    }

    public void setLossType(Integer lossType) {
        this.lossType = lossType;
    }

    public String getIsAllDocUpload() {
        return isAllDocUpload;
    }

    public void setIsAllDocUpload(String isAllDocUpload) {
        this.isAllDocUpload = isAllDocUpload;
    }

    public String getIsCheckAllMndDocs() {
        return isCheckAllMndDocs;
    }

    public void setIsCheckAllMndDocs(String isCheckAllMndDocs) {
        this.isCheckAllMndDocs = isCheckAllMndDocs;
    }

    public BigDecimal getPenaltyBaldTyre() {
        return penaltyBaldTyre;
    }

    public void setPenaltyBaldTyre(BigDecimal penaltyBaldTyre) {
        this.penaltyBaldTyre = penaltyBaldTyre;
    }

    public String getPenaltyBaldTyreUser() {
        return penaltyBaldTyreUser;
    }

    public void setPenaltyBaldTyreUser(String penaltyBaldTyreUser) {
        this.penaltyBaldTyreUser = penaltyBaldTyreUser;
    }

    public String getPenaltyBaldTyreDateTime() {
        return penaltyBaldTyreDateTime;
    }

    public void setPenaltyBaldTyreDateTime(String penaltyBaldTyreDateTime) {
        this.penaltyBaldTyreDateTime = penaltyBaldTyreDateTime;
    }

    public BigDecimal getPenaltyUnderInsurce() {
        return penaltyUnderInsurce;
    }

    public void setPenaltyUnderInsurce(BigDecimal penaltyUnderInsurce) {
        this.penaltyUnderInsurce = penaltyUnderInsurce;
    }

    public String getPenaltyUnderInsurceUser() {
        return penaltyUnderInsurceUser;
    }

    public void setPenaltyUnderInsurceUser(String penaltyUnderInsurceUser) {
        this.penaltyUnderInsurceUser = penaltyUnderInsurceUser;
    }

    public String getPenaltyUnderInsurceDateTime() {
        return penaltyUnderInsurceDateTime;
    }

    public void setPenaltyUnderInsurceDateTime(String penaltyUnderInsurceDateTime) {
        this.penaltyUnderInsurceDateTime = penaltyUnderInsurceDateTime;
    }

    public String getInvestigationArrangeUserId() {
        return investigationArrangeUserId;
    }

    public void setInvestigationArrangeUserId(String investigationArrangeUserId) {
        this.investigationArrangeUserId = investigationArrangeUserId;
    }

    public String getInvestigationArrangeDateTime() {
        return investigationArrangeDateTime;
    }

    public void setInvestigationArrangeDateTime(String investigationArrangeDateTime) {
        this.investigationArrangeDateTime = investigationArrangeDateTime;
    }

    public String getIntimationChk() {
        return intimationChk;
    }

    public void setIntimationChk(String intimationChk) {
        this.intimationChk = intimationChk;
    }

    public String getIntimationChkUser() {
        return intimationChkUser;
    }

    public void setIntimationChkUser(String intimationChkUser) {
        this.intimationChkUser = intimationChkUser;
    }

    public String getIntimationChkDateTime() {
        return intimationChkDateTime;
    }

    public void setIntimationChkDateTime(String intimationChkDateTime) {
        this.intimationChkDateTime = intimationChkDateTime;
    }

    public String getIsLcChk1() {
        return isLcChk1;
    }

    public void setIsLcChk1(String isLcChk1) {
        this.isLcChk1 = isLcChk1;
    }

    public String getIsLcChk2() {
        return isLcChk2;
    }

    public void setIsLcChk2(String isLcChk2) {
        this.isLcChk2 = isLcChk2;
    }

    public String getIsLcChk3() {
        return isLcChk3;
    }

    public void setIsLcChk3(String isLcChk3) {
        this.isLcChk3 = isLcChk3;
    }

    public String getIsLcChk4() {
        return isLcChk4;
    }

    public void setIsLcChk4(String isLcChk4) {
        this.isLcChk4 = isLcChk4;
    }

    public String getIsLcChk5() {
        return isLcChk5;
    }

    public void setIsLcChk5(String isLcChk5) {
        this.isLcChk5 = isLcChk5;
    }

    public String getIsLcChk6() {
        return isLcChk6;
    }

    public void setIsLcChk6(String isLcChk6) {
        this.isLcChk6 = isLcChk6;
    }

    public String getLcChkUser() {
        return lcChkUser;
    }

    public void setLcChkUser(String lcChkUser) {
        this.lcChkUser = lcChkUser;
    }

    public String getLcChkDateTime() {
        return lcChkDateTime;
    }

    public void setLcChkDateTime(String lcChkDateTime) {
        this.lcChkDateTime = lcChkDateTime;
    }

    public String getClaimPanelAssignUsers() {
        return claimPanelAssignUsers;
    }

    public void setClaimPanelAssignUsers(String claimPanelAssignUsers) {
        this.claimPanelAssignUsers = claimPanelAssignUsers;
    }

    public String getClaimPanelAssignUserDateTime() {
        return claimPanelAssignUserDateTime;
    }

    public void setClaimPanelAssignUserDateTime(String claimPanelAssignUserDateTime) {
        this.claimPanelAssignUserDateTime = claimPanelAssignUserDateTime;
    }

    public Integer getDecisionApproveClaimPanel() {
        return decisionApproveClaimPanel;
    }

    public void setDecisionApproveClaimPanel(Integer decisionApproveClaimPanel) {
        this.decisionApproveClaimPanel = decisionApproveClaimPanel;
    }

    public String getClaimPanelDecision() {
        return claimPanelDecision;
    }

    public void setClaimPanelDecision(String claimPanelDecision) {
        this.claimPanelDecision = claimPanelDecision;
    }

    public String getClaimPanelDecisionDateTime() {
        return claimPanelDecisionDateTime;
    }

    public void setClaimPanelDecisionDateTime(String claimPanelDecisionDateTime) {
        this.claimPanelDecisionDateTime = claimPanelDecisionDateTime;
    }

    public Integer getRepudiatedType() {
        return repudiatedType;
    }

    public void setRepudiatedType(Integer repudiatedType) {
        this.repudiatedType = repudiatedType;
    }

    public String getIsPrintRepudiatedLetter() {
        return isPrintRepudiatedLetter;
    }

    public void setIsPrintRepudiatedLetter(String isPrintRepudiatedLetter) {
        this.isPrintRepudiatedLetter = isPrintRepudiatedLetter;
    }

    public String getRepudiatedLetterPrintUserId() {
        return repudiatedLetterPrintUserId;
    }

    public void setRepudiatedLetterPrintUserId(String repudiatedLetterPrintUserId) {
        this.repudiatedLetterPrintUserId = repudiatedLetterPrintUserId;
    }

    public String getFinancialInterest() {
        return financialInterest;
    }

    public void setFinancialInterest(String financialInterest) {
        this.financialInterest = financialInterest;
    }

    public Integer getLeasingRefNo() {
        return leasingRefNo;
    }

    public void setLeasingRefNo(Integer leasingRefNo) {
        this.leasingRefNo = leasingRefNo;
    }

    public String getFinalizeUserId() {
        return finalizeUserId;
    }

    public void setFinalizeUserId(String finalizeUserId) {
        this.finalizeUserId = finalizeUserId;
    }

    public String getFinalizeDateTime() {
        return finalizeDateTime;
    }

    public void setFinalizeDateTime(String finalizeDateTime) {
        this.finalizeDateTime = finalizeDateTime;
    }

    public String getIsGenarateSupplyOrder() {
        return isGenarateSupplyOrder;
    }

    public void setIsGenarateSupplyOrder(String isGenarateSupplyOrder) {
        this.isGenarateSupplyOrder = isGenarateSupplyOrder;
    }

    public String getSupplyOrderAssignUser() {
        return supplyOrderAssignUser;
    }

    public void setSupplyOrderAssignUser(String supplyOrderAssignUser) {
        this.supplyOrderAssignUser = supplyOrderAssignUser;
    }

    public String getSupplyOrderAssignDateTime() {
        return supplyOrderAssignDateTime;
    }

    public void setSupplyOrderAssignDateTime(String supplyOrderAssignDateTime) {
        this.supplyOrderAssignDateTime = supplyOrderAssignDateTime;
    }

    public String getSupplyOrderCreateUser() {
        return supplyOrderCreateUser;
    }

    public void setSupplyOrderCreateUser(String supplyOrderCreateUser) {
        this.supplyOrderCreateUser = supplyOrderCreateUser;
    }

    public String getSupplyOrderCreateDateTime() {
        return supplyOrderCreateDateTime;
    }

    public void setSupplyOrderCreateDateTime(String supplyOrderCreateDateTime) {
        this.supplyOrderCreateDateTime = supplyOrderCreateDateTime;
    }

    public String getSupplyOrderCreateClose() {
        return supplyOrderCreateClose;
    }

    public void setSupplyOrderCreateClose(String supplyOrderCreateClose) {
        this.supplyOrderCreateClose = supplyOrderCreateClose;
    }

    public String getIsFileStore() {
        return isFileStore;
    }

    public void setIsFileStore(String isFileStore) {
        this.isFileStore = isFileStore;
    }

    public String getFileUserStoreUserId() {
        return fileUserStoreUserId;
    }

    public void setFileUserStoreUserId(String fileUserStoreUserId) {
        this.fileUserStoreUserId = fileUserStoreUserId;
    }

    public String getFileStoreDateTime() {
        return fileStoreDateTime;
    }

    public void setFileStoreDateTime(String fileStoreDateTime) {
        this.fileStoreDateTime = fileStoreDateTime;
    }

    public String getReopenAssignUserId() {
        return reopenAssignUserId;
    }

    public void setReopenAssignUserId(String reopenAssignUserId) {
        this.reopenAssignUserId = reopenAssignUserId;
    }

    public String getReopenAssignUserDateTime() {
        return reopenAssignUserDateTime;
    }

    public void setReopenAssignUserDateTime(String reopenAssignUserDateTime) {
        this.reopenAssignUserDateTime = reopenAssignUserDateTime;
    }

    public String getReopenUserId() {
        return reopenUserId;
    }

    public void setReopenUserId(String reopenUserId) {
        this.reopenUserId = reopenUserId;
    }

    public String getReopenDateTime() {
        return reopenDateTime;
    }

    public void setReopenDateTime(String reopenDateTime) {
        this.reopenDateTime = reopenDateTime;
    }

    public Integer getReopenNoOfTime() {
        return reopenNoOfTime;
    }

    public void setReopenNoOfTime(Integer reopenNoOfTime) {
        this.reopenNoOfTime = reopenNoOfTime;
    }

    public String getIsGenFinalRemindLetter() {
        return isGenFinalRemindLetter;
    }

    public void setIsGenFinalRemindLetter(String isGenFinalRemindLetter) {
        this.isGenFinalRemindLetter = isGenFinalRemindLetter;
    }

    public String getGenFinalRemindLetterDateTime() {
        return genFinalRemindLetterDateTime;
    }

    public void setGenFinalRemindLetterDateTime(String genFinalRemindLetterDateTime) {
        this.genFinalRemindLetterDateTime = genFinalRemindLetterDateTime;
    }

    public String getInpStatus() {
        return inpStatus;
    }

    public void setInpStatus(String inpStatus) {
        this.inpStatus = inpStatus;
    }

    public String getInpUserId() {
        return inpUserId;
    }

    public void setInpUserId(String inpUserId) {
        this.inpUserId = inpUserId;
    }

    public String getInpDateTime() {
        return inpDateTime;
    }

    public void setInpDateTime(String inpDateTime) {
        this.inpDateTime = inpDateTime;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public ClaimsDto getClaimsDto() {
        return claimsDto;
    }

    public void setClaimsDto(ClaimsDto claimsDto) {
        this.claimsDto = claimsDto;
    }

    public String getRepudiatedLetterPrintDateTime() {
        return repudiatedLetterPrintDateTime;
    }

    public void setRepudiatedLetterPrintDateTime(String repudiatedLetterPrintDateTime) {
        this.repudiatedLetterPrintDateTime = repudiatedLetterPrintDateTime;
    }

    public String getDecisionMakingAssignUserId() {
        return decisionMakingAssignUserId;
    }

    public void setDecisionMakingAssignUserId(String decisionMakingAssignUserId) {
        this.decisionMakingAssignUserId = decisionMakingAssignUserId;
    }

    public String getDecisionMakingAssignDateTime() {
        return decisionMakingAssignDateTime;
    }

    public void setDecisionMakingAssignDateTime(String decisionMakingAssignDateTime) {
        this.decisionMakingAssignDateTime = decisionMakingAssignDateTime;
    }

    public String getInvestigationStatus() {
        return investigationStatus;
    }

    public void setInvestigationStatus(String investigationStatus) {
        this.investigationStatus = investigationStatus;
    }

    public String getInvestigationAssignUserId() {
        return investigationAssignUserId;
    }

    public void setInvestigationAssignUserId(String investigationAssignUserId) {
        this.investigationAssignUserId = investigationAssignUserId;
    }

    public String getInvestigationAssignDateTime() {
        return investigationAssignDateTime;
    }

    public void setInvestigationAssignDateTime(String investigationAssignDateTime) {
        this.investigationAssignDateTime = investigationAssignDateTime;
    }

    public String getInvestigationCompletedUserId() {
        return investigationCompletedUserId;
    }

    public void setInvestigationCompletedUserId(String investigationCompletedUserId) {
        this.investigationCompletedUserId = investigationCompletedUserId;
    }

    public String getSpecialApprovalUserId() {
        return specialApprovalUserId;
    }

    public void setSpecialApprovalUserId(String specialApprovalUserId) {
        this.specialApprovalUserId = specialApprovalUserId;
    }

    public String getSpecialApprovalDateTime() {
        return specialApprovalDateTime;
    }

    public void setSpecialApprovalDateTime(String specialApprovalDateTime) {
        this.specialApprovalDateTime = specialApprovalDateTime;
    }

    public Integer getOldClaimStatus() {
        return oldClaimStatus;
    }

    public void setOldClaimStatus(Integer oldClaimStatus) {
        this.oldClaimStatus = oldClaimStatus;
    }

    public String getInvestigationCompletedDateTime() {
        return investigationCompletedDateTime;
    }

    public void setInvestigationCompletedDateTime(String investigationCompletedDateTime) {
        this.investigationCompletedDateTime = investigationCompletedDateTime;
    }

    public String getSupplyOrderAssignStatus() {
        return supplyOrderAssignStatus;
    }

    public void setSupplyOrderAssignStatus(String supplyOrderAssignStatus) {
        this.supplyOrderAssignStatus = supplyOrderAssignStatus;
    }

    public String getIsLcChk7() {
        return isLcChk7;
    }

    public void setIsLcChk7(String isLcChk7) {
        this.isLcChk7 = isLcChk7;
    }

    public String getIsLcChk8() {
        return isLcChk8;
    }

    public void setIsLcChk8(String isLcChk8) {
        this.isLcChk8 = isLcChk8;
    }

    public BigDecimal getPenaltyBaldTyreRate() {
        return penaltyBaldTyreRate;
    }

    public void setPenaltyBaldTyreRate(BigDecimal penaltyBaldTyreRate) {
        this.penaltyBaldTyreRate = penaltyBaldTyreRate;
    }

    public BigDecimal getPenaltyUnderInsurceRate() {
        return penaltyUnderInsurceRate;
    }

    public void setPenaltyUnderInsurceRate(BigDecimal penaltyUnderInsurceRate) {
        this.penaltyUnderInsurceRate = penaltyUnderInsurceRate;
    }

    public BigDecimal getLabourCost() {
        return labourCost;
    }

    public void setLabourCost(BigDecimal labourCost) {
        this.labourCost = labourCost;
    }

    public BigDecimal getPartCost() {
        return partCost;
    }

    public void setPartCost(BigDecimal partCost) {
        this.partCost = partCost;
    }

    public String getCauseOfLoss() {
        return causeOfLoss;
    }

    public void setCauseOfLoss(String causeOfLoss) {
        this.causeOfLoss = causeOfLoss;
    }

    public String getLossTypeDecs() {
        return lossTypeDecs;
    }

    public void setLossTypeDecs(String lossTypeDecs) {
        this.lossTypeDecs = lossTypeDecs;
    }

    public String getClaimStatusPara() {
        return claimStatusPara;
    }

    public void setClaimStatusPara(String claimStatusPara) {
        this.claimStatusPara = claimStatusPara;
    }

    public String getSpecialApprovalInputUserId() {
        return specialApprovalInputUserId;
    }

    public void setSpecialApprovalInputUserId(String specialApprovalInputUserId) {
        this.specialApprovalInputUserId = specialApprovalInputUserId;
    }

    public String getSpecialApprovalInputDateTime() {
        return specialApprovalInputDateTime;
    }

    public void setSpecialApprovalInputDateTime(String specialApprovalInputDateTime) {
        this.specialApprovalInputDateTime = specialApprovalInputDateTime;
    }

    public String getIsDoubt() {
        return isDoubt;
    }

    public void setIsDoubt(String isDoubt) {
        this.isDoubt = isDoubt;
    }

    public String getIsOnSiteOffer() {
        return isOnSiteOffer;
    }

    public void setIsOnSiteOffer(String isOnSiteOffer) {
        this.isOnSiteOffer = isOnSiteOffer;
    }


    public String getCloseStatus() {
        return closeStatus;
    }

    public void setCloseStatus(String closeStatus) {
        this.closeStatus = closeStatus;
    }

    public String getCloseUser() {
        return closeUser;
    }

    public void setCloseUser(String closeUser) {
        this.closeUser = closeUser;
    }

    public String getCloseDateTime() {
        return closeDateTime;
    }

    public void setCloseDateTime(String closeDateTime) {
        this.closeDateTime = closeDateTime;
    }

    public Integer getProcessingStage() {
        return processingStage;
    }

    public void setProcessingStage(Integer processingStage) {
        this.processingStage = processingStage;
    }

    public Integer getRejectionLatterType() {
        return rejectionLatterType;
    }

    public void setRejectionLatterType(Integer rejectionLatterType) {
        this.rejectionLatterType = rejectionLatterType;
    }

    public Integer getRepudiatedLetterType() {
        return repudiatedLetterType;
    }

    public void setRepudiatedLetterType(Integer repudiatedLetterType) {
        this.repudiatedLetterType = repudiatedLetterType;
    }

    public String getRepudiatedLetterPrintUserName() {
        return RepudiatedLetterPrintUserName;
    }

    public void setRepudiatedLetterPrintUserName(String repudiatedLetterPrintUserName) {
        RepudiatedLetterPrintUserName = repudiatedLetterPrintUserName;
    }

    public String getClaimHandlerMobileNo() {
        return claimHandlerMobileNo;
    }

    public void setClaimHandlerMobileNo(String claimHandlerMobileNo) {
        this.claimHandlerMobileNo = claimHandlerMobileNo;
    }

    public String getIsExcessInclude() {
        return isExcessInclude;
    }

    public void setIsExcessInclude(String isExcessInclude) {
        this.isExcessInclude = isExcessInclude;
    }

    public String getIsProvideOffer() {
        return isProvideOffer;
    }

    public void setIsProvideOffer(String isProvideOffer) {
        this.isProvideOffer = isProvideOffer;
    }

    public BigDecimal getEngineerApprovedAmount() {
        return engineerApprovedAmount;
    }

    public void setEngineerApprovedAmount(BigDecimal engineerApprovedAmount) {
        this.engineerApprovedAmount = engineerApprovedAmount;
    }

    public String getAcrAprvUser() {
        return acrAprvUser;
    }

    public void setAcrAprvUser(String acrAprvUser) {
        this.acrAprvUser = acrAprvUser;
    }

    public String getLetterPanelUserId() {
        return letterPanelUserId;
    }

    public void setLetterPanelUserId(String letterPanelUserId) {
        this.letterPanelUserId = letterPanelUserId;
    }

    public String getAdvanceApprovalAssignUser() {
        return advanceApprovalAssignUser;
    }

    public void setAdvanceApprovalAssignUser(String advanceApprovalAssignUser) {
        this.advanceApprovalAssignUser = advanceApprovalAssignUser;
    }

    public String getAdvanceApprovalAssignDateTime() {
        return advanceApprovalAssignDateTime;
    }

    public void setAdvanceApprovalAssignDateTime(String advanceApprovalAssignDateTime) {
        this.advanceApprovalAssignDateTime = advanceApprovalAssignDateTime;
    }

    public String getAdvanceForwardedUser() {
        return advanceForwardedUser;
    }

    public void setAdvanceForwardedUser(String advanceForwardedUser) {
        this.advanceForwardedUser = advanceForwardedUser;
    }

    public BigDecimal getPendingAdvance() {
        return pendingAdvance;
    }

    public void setPendingAdvance(BigDecimal pendingAdvance) {
        this.pendingAdvance = pendingAdvance;
    }

    public String getAdvanceStatus() {
        return advanceStatus;
    }

    public void setAdvanceStatus(String advanceStatus) {
        this.advanceStatus = advanceStatus;
    }

    public String getAdvanceApprovedDateTime() {
        return advanceApprovedDateTime;
    }

    public void setAdvanceApprovedDateTime(String advanceApprovedDateTime) {
        this.advanceApprovedDateTime = advanceApprovedDateTime;
    }

    public String getAdvanceApprovedUser() {
        return advanceApprovedUser;
    }

    public void setAdvanceApprovedUser(String advanceApprovedUser) {
        this.advanceApprovedUser = advanceApprovedUser;
    }

    public String getForwardedEngineer() {
        return forwardedEngineer;
    }

    public void setForwardedEngineer(String forwardedEngineer) {
        this.forwardedEngineer = forwardedEngineer;
    }

    public String getLetterPanelDateTime() {
        return letterPanelDateTime;
    }

    public void setLetterPanelDateTime(String letterPanelDateTime) {
        this.letterPanelDateTime = letterPanelDateTime;
    }

    public String getIsRejectionAttached() {
        return isRejectionAttached;
    }

    public void setIsRejectionAttached(String isRejectionAttached) {
        this.isRejectionAttached = isRejectionAttached;
    }

    public String getRejectionAttachedDateTime() {
        return rejectionAttachedDateTime;
    }

    public void setRejectionAttachedDateTime(String rejectionAttachedDateTime) {
        this.rejectionAttachedDateTime = rejectionAttachedDateTime;
    }

    public Integer getOldEngineerClaimStatus() {
        return oldEngineerClaimStatus;
    }

    public void setOldEngineerClaimStatus(Integer oldEngineerClaimStatus) {
        this.oldEngineerClaimStatus = oldEngineerClaimStatus;
    }
}
