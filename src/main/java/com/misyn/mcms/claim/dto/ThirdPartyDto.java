package com.misyn.mcms.claim.dto;

import com.misyn.mcms.claim.enums.YesNoWantDecideEnum;
import com.misyn.mcms.utility.AppConstant;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import org.hibernate.validator.constraints.NotEmpty;

import java.io.Serializable;
public class ThirdPartyDto implements Serializable {

    private Integer txnId = AppConstant.ZERO_INT;
    private Integer claimNo = AppConstant.ZERO_INT;
    @NotNull
    @NotEmpty
    private String thirdPartyInvolved = YesNoWantDecideEnum.NO.getYesNoWantDecide();
    @NotNull
    @Min(value = 1)
    private Integer lossType = AppConstant.ZERO_INT;
    @NotNull
    @Min(value = 1)
    private Integer itemType = AppConstant.ZERO_INT;
    private String vehicleNo = AppConstant.STRING_EMPTY;
    @NotNull
    @NotEmpty
    private String intendClaim = YesNoWantDecideEnum.NO.getYesNoWantDecide();
    /*@NotNull
    @NotEmpty*/
    private String remark = AppConstant.STRING_EMPTY;
    private String inpUserId = AppConstant.STRING_EMPTY;
    private String inpDateTime = AppConstant.STRING_EMPTY;


    public Integer getTxnId() {
        return txnId;
    }

    public void setTxnId(Integer txnId) {
        this.txnId = txnId;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public String getThirdPartyInvolved() {
        return thirdPartyInvolved;
    }

    public void setThirdPartyInvolved(String thirdPartyInvolved) {
        this.thirdPartyInvolved = thirdPartyInvolved;
    }

    public Integer getLossType() {
        return lossType;
    }

    public void setLossType(Integer lossType) {
        this.lossType = lossType;
    }

    public Integer getItemType() {
        return itemType;
    }

    public void setItemType(Integer itemType) {
        this.itemType = itemType;
    }

    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    public String getIntendClaim() {
        return intendClaim;
    }

    public void setIntendClaim(String intendClaim) {
        this.intendClaim = intendClaim;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getInpUserId() {
        return inpUserId;
    }

    public void setInpUserId(String inpUserId) {
        this.inpUserId = inpUserId;
    }

    public String getInpDateTime() {
        return inpDateTime;
    }

    public void setInpDateTime(String inpDateTime) {
        this.inpDateTime = inpDateTime;
    }
}
