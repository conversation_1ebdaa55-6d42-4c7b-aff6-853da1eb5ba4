package com.misyn.mcms.admin.admin.service.impl;

import com.misyn.mcms.admin.admin.dao.*;
import com.misyn.mcms.admin.admin.dao.impl.*;
import com.misyn.mcms.admin.admin.dto.*;
import com.misyn.mcms.admin.admin.service.UserManagementService;
import com.misyn.mcms.claim.dao.ClaimUserAllocationDao;
import com.misyn.mcms.claim.dao.UserDao;
import com.misyn.mcms.claim.dao.impl.ClaimUserAllocationDaoImpl;
import com.misyn.mcms.claim.dao.impl.UserDaoImpl;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.ListItemDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.exception.ErrorMsgException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.*;
import java.util.stream.Collectors;
public class UserManagementServiceImpl extends AbstractBaseService<UserManagementServiceImpl> implements UserManagementService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserManagementServiceImpl.class);
    private final ClaimChannelTeamDao claimChannelTeamDao = new ClaimChannelTeamDaoImpl();
    private final CoverDetailMstDao coverDetailMstDao = new CoverDetailMstDaoImpl();
    private final BenefitDetailDao benefitDetailDao = new BenefitDetailDaoImpl();
    private final ConditionAndExclusionDetailDao conditionAndExclusionDetailDao = new ConditionAndExclusionDetailDaoImpl();
    private final ServiceFactorDetailDao serviceFactorDetailDao = new ServiceFactorDetailDaoImpl();
    private final SpecialPackageDao specialPackageDao = new SpecialPackageDaoImpl();
    private final SrcctcDetailDao srcctcDetailDao = new SrcctcDetailDaoImpl();
    private final UserDao userDao = new UserDaoImpl();
    private final BranchMstDao branchMstDao = new BranchMstDaoImpl();
    private final ClaimUserAllocationDao claimUserAllocationDao = new ClaimUserAllocationDaoImpl();

    @Override
    public List<ClaimChannelDto> getChannelsForTeam(Integer teamId) throws Exception {
        Connection connection = null;
        List<ClaimChannelDto> claimChannels = null;
        try {
            connection = getJDBCConnection();
            return claimChannelTeamDao.getChannelsByTeamId(connection, teamId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<ChannelTeamGridDto> getTeamList() throws Exception {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimChannelTeamDao.getTeamList(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<UserDto> getMembersForTeam(Integer teamId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return userDao.getMembersForTeam(connection, teamId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<ListItemDto> fetchSpecialTeam() throws Exception {
        Connection connection = null;
        List<ListItemDto> activeUsers = new ArrayList<>();

        try {
            connection = getJDBCConnection();

            for (ListItemDto listItemDto : userDao.fetchSpecialTeam(connection)) {
                boolean isLeave = claimUserAllocationDao.checkIfLeave(
                        connection,
                        AppConstant.ACCESS_LEVEL_SPECIAL_TEAM,
                        AppConstant.CLAIM_HANDLER_NORAML_FUNCTION,
                        Utility.sysDateTime(),
                        (String) listItemDto.getValue()
                );

                if (!isLeave) {
                    activeUsers.add(listItemDto);
                }
            }

            return activeUsers;

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<ListItemDto> fetchApprovalUserList(Integer claimNo) throws Exception {
        Connection connection = null;
        List<ListItemDto> activeUsers = new ArrayList<>();

        try {
            connection = getJDBCConnection();
            for (ListItemDto listItemDto : userDao.fetchApprovalUserList(connection, claimNo)) {
                boolean isLeave = claimUserAllocationDao.checkIfLeave(
                        connection,
                        AppConstant.ACCESS_LEVEL_MOFA_TEAM,
                        AppConstant.CLAIM_HANDLER_NORAML_FUNCTION,
                        Utility.sysDateTime(),
                        (String) listItemDto.getValue()
                );

                if (!isLeave) {
                    activeUsers.add(listItemDto);
                }
            }
            return activeUsers;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public ChannelTeamDto getTeamInfo(Integer teamId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimChannelTeamDao.getChannelTeam(connection, teamId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<ClaimChannelDto> getChannelsNotInTeams() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimChannelTeamDao.getChannelsNotInTeams(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void saveNewTeam(String teamName, String channelDesc, List<Integer> ids) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            Integer teamId = claimChannelTeamDao.saveNewTeam(connection, teamName, channelDesc);
            if (ids != null) {
                claimChannelTeamDao.updateChannels(connection, teamId, ids);
            }
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<Integer> getSelectedList(String regex, String array) {
        List<Integer> list = null;
        if (!array.isEmpty()) {
            array = array.replaceAll(regex, "");
            list = Arrays.stream(array.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        } else {
            list = new ArrayList<>();
        }

        return list;
    }

    public List<String> getStringSelectedList(String regex, String array) {
        List<String> list = null;
        if (!array.isEmpty()) {
            array = array.replaceAll(regex, "");
            list = Arrays.stream(array.split(",")).map(String::toString).collect(Collectors.toList());
        } else {
            list = new ArrayList<>();
        }

        return list;
    }

    @Override
    public void addChannelsForTeam(Integer teamId, List<Integer> selectedList) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            claimChannelTeamDao.updateChannels(connection, teamId, selectedList);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<ChannelTeamDto> getAllTeamDetails() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimChannelTeamDao.getAllTeamDetails(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public boolean checkTeamName(String teamName) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimChannelTeamDao.checkTeamName(connection, teamName);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public boolean updateTeamDetails(ChannelTeamDto channelTeamDto) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimChannelTeamDao.updateTeamDetails(connection, channelTeamDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<BranchDetailDto> getAllActiveBranchDetails() {
        List<BranchDetailDto> branchDetailDtoList = new ArrayList<>();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            branchDetailDtoList = branchMstDao.searchAll(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return branchDetailDtoList;
    }

    @Override
    public BranchDetailDto getBranchDetail(String branchCode) {
        BranchDetailDto branchDetailDto = new BranchDetailDto();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            branchDetailDto = branchMstDao.getBranchDetailByBranchCode(connection, branchCode);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return branchDetailDto;
    }

    @Override
    public DataGridDto getBranchDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = branchMstDao.getDataGridDto(connection, parameterList, drawRandomId, start, length,
                    orderType, orderField);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public void updateBranchDetails(String branchCode, String branchCity, String branchName, UserDto user) throws Exception {
        BranchDetailDto branchDetailDto = new BranchDetailDto();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            branchDetailDto.setBranchCode(branchCode);
            branchDetailDto.setBranchName(branchName);
            branchDetailDto.setBranchCity(branchCity);
            branchDetailDto.setInputUser(user.getUserId());
            branchDetailDto.setInputDatetime(Utility.sysDateTime());
            branchDetailDto.setRecordStatus(AppConstant.ACTIVE_STATUS);
            branchMstDao.updateMaster(connection, branchDetailDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public boolean isAlreadySavedBranch(String branchCode) {
        boolean isAlreadySaved = false;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            isAlreadySaved = branchMstDao.isAlreadyHaveRecordByBranchCodeAndRecordStatus(connection, branchCode, AppConstant.ACTIVE_STATUS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return isAlreadySaved;
    }

    @Override
    public void saveBranchDetails(String branchCode, String branchName, String branchCity, UserDto user) throws Exception {
        BranchDetailDto branchDetailDto = new BranchDetailDto();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            branchDetailDto.setBranchCode(branchCode);
            branchDetailDto.setBranchName(branchName);
            branchDetailDto.setBranchCity(branchCity);
            branchDetailDto.setInputUser(user.getUserId());
            branchDetailDto.setInputDatetime(Utility.sysDateTime());
            branchDetailDto.setRecordStatus(AppConstant.ACTIVE_STATUS);
            branchMstDao.insertMaster(connection, branchDetailDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void deleteSelectedBranches(String selectedBranchesCode, UserDto user) throws Exception {
        BranchDetailDto branchDetailDto = new BranchDetailDto();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            List<String> branchList = getStringSelectedList("BRL-", selectedBranchesCode);
            if (null != branchList && !branchList.isEmpty()) {
                for (String branchCode : branchList) {
                    branchDetailDto = branchMstDao.getBranchDetailByBranchCode(connection, branchCode);
                    if (null != branchDetailDto) {
                        branchDetailDto.setInputUser(user.getUserId());
                        branchDetailDto.setInputDatetime(Utility.sysDateTime());
                        branchDetailDto.setRecordStatus(AppConstant.DELETE);
                        branchMstDao.updateMaster(connection, branchDetailDto);
                    } else {
                        throw new Exception();
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<CoverDetailMstDto> getBenefitCoverLoadingDetail(String code, String name) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return coverDetailMstDao.searchAllBenefitCoverLoadingDetail(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<CoverDetailMstDto> getCWEDetail(String code, String name) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return coverDetailMstDao.searchAllCWEDetail(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<CoverDetailMstDto> getChargesAndDiscountDetail(String code, String name) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return coverDetailMstDao.searchAllChargesAndDiscountDetail(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public ProductDetailListDto getProductDetailById() throws Exception {
        Connection connection = null;
        ProductDetailListDto productDetailListDto = new ProductDetailListDto();
        try {
            connection = getJDBCConnection();
            List<CoverDetailMstDto> coverDetailMstDtoList = coverDetailMstDao.searchAdminCoverDetail(connection);
            List<BenefitDetailDto> benefitDetailDtoList = benefitDetailDao.searchByProductId(connection);
            List<ConditionAndExclusionDto> conditionAndExclusionDtoList = conditionAndExclusionDetailDao.searchByProductId(connection);
            List<ServiceFactorDetailDto> serviceFactorDetailDtoList = serviceFactorDetailDao.searchByProductId(connection);
            List<SpecialPackagesDto> specialPackagesDtoList = specialPackageDao.searchAll(connection);
            List<SrcctcDetailDto> srcctcDetailDtoList = srcctcDetailDao.searchAll(connection);
            productDetailListDto.setBenefitData(benefitDetailDtoList);
            productDetailListDto.setCoverData(coverDetailMstDtoList);
            productDetailListDto.setServiceFactorData(serviceFactorDetailDtoList);
            productDetailListDto.setConditionData(conditionAndExclusionDtoList);
            productDetailListDto.setSpecialPackagesData(specialPackagesDtoList);
            productDetailListDto.setSrcctcDetailData(srcctcDetailDtoList);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return productDetailListDto;
    }

    @Override
    public ProductDetailListDto getProductDetailByName(String productName) throws Exception {
        Connection connection = null;
        ProductDetailListDto productDetailListDto = new ProductDetailListDto();
        try {
            connection = getJDBCConnection();
            List<CoverDetailMstDto> coverDetailMstDtoList = coverDetailMstDao.searchAll(connection);
            List<BenefitDetailDto> benefitDetailDtoList = benefitDetailDao.searchAll(connection);
            List<ConditionAndExclusionDto> conditionAndExclusionDtoList = conditionAndExclusionDetailDao.searchAll(connection);
            List<ServiceFactorDetailDto> serviceFactorDetailDtoList = serviceFactorDetailDao.searchAll(connection);
            productDetailListDto.setBenefitData(benefitDetailDtoList);
            productDetailListDto.setCoverData(coverDetailMstDtoList);
            productDetailListDto.setServiceFactorData(serviceFactorDetailDtoList);
            productDetailListDto.setConditionData(conditionAndExclusionDtoList);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return productDetailListDto;
    }

    @Override
    public void saveProductDetail(Map<String, Object> productDetailMap) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            saveProductDetailData(productDetailMap);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<CoverDetailMstDto> getSalientMasterDetail(String code, String name, String tableName) throws Exception {
        Connection connection = null;
        List<CoverDetailMstDto> list;
        try {
            connection = getJDBCConnection();
            switch (tableName) {
                case "chargesMasterTable":
                    list = coverDetailMstDao.searchAllChargesAndDiscountDetail(connection, code, name);
                    break;
                case "cweMasterTable":
                    list = coverDetailMstDao.searchAllCWEDetailMater(connection, code, name);
                    break;
                case "benefitCoverLoadingDetailTable":
                    list = coverDetailMstDao.searchAllBenefitCoverLoadingDetailMater(connection, code, name);
                    break;
                default:
                    throw new ErrorMsgException("ERROR", "Search failed, Error Occurred!");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public List<CoverDetailMstDto> getSrccTcDetail(String code, String name) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return coverDetailMstDao.searchAllSrccTcDetail(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private void saveProductDetailData(Map<String, Object> mapData) throws Exception {
        Connection connection = null;
        List<String> selectedCovers = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);

            coverDetailMstDao.EmptyAllCoverDetail(connection);
            coverDetailMstDao.EmptyAllConditionDetail(connection);
            coverDetailMstDao.EmptyAllSpecialPackageDetail(connection);
            coverDetailMstDao.EmptyAllBenefitDetail(connection);
            coverDetailMstDao.EmptyAllSpecialPackageDetail(connection);
            coverDetailMstDao.EmptyAllServiceFactorDetail(connection);

            coverDetailMstDao.updateStatusChargesAndDiscountDetail(connection, AppConstant.NO);
            coverDetailMstDao.updateStatusBefitCoverLoadingDetailMater(connection, AppConstant.NO);
            coverDetailMstDao.updateStatusCWEDetailMater(connection, AppConstant.NO);
            coverDetailMstDao.updateStatusSRCCTcDetailMater(connection, AppConstant.NO);

            for (Map.Entry<String, Object> entry : mapData.entrySet()) {
                String key = entry.getKey();
                ArrayList<?> value = (ArrayList<?>) entry.getValue();
                for (Object product : value) {
                    if (product instanceof LinkedHashMap) {
                        switch (key) {
                            case "serviceFactorData":
                                ServiceFactorDetailDto serviceFactorDetailDto = new ServiceFactorDetailDto();
                                serviceFactorDetailDto.setServiceFactorName(String.valueOf(((LinkedHashMap<?, ?>) product).get(AppConstant.STRING_PRODUCT_NAME)));
                                serviceFactorDetailDto.setCode(String.valueOf(((LinkedHashMap<?, ?>) product).get(AppConstant.STRING_CODE)));
                                if (null != serviceFactorDetailDto.getServiceFactorName() && null != serviceFactorDetailDto.getCode()) {
                                    serviceFactorDetailDao.insertMaster(connection, serviceFactorDetailDto);
                                    selectedCovers.add(serviceFactorDetailDto.getCode());
                                }
                                break;
                            case "benefitData":
                                BenefitDetailDto benefitDetailDto = new BenefitDetailDto();
                                benefitDetailDto.setBenefitName(String.valueOf(((LinkedHashMap<?, ?>) product).get(AppConstant.STRING_PRODUCT_NAME)));
                                benefitDetailDto.setCode(String.valueOf(((LinkedHashMap<?, ?>) product).get(AppConstant.STRING_CODE)));
                                if (null != benefitDetailDto.getBenefitName() && null != benefitDetailDto.getCode()) {
                                    benefitDetailDao.insertMaster(connection, benefitDetailDto);
                                    selectedCovers.add(benefitDetailDto.getCode());
                                }
                                break;
                            case "coverData":
                                CoverDetailMstDto coverDetailMstDto = new CoverDetailMstDto();
                                coverDetailMstDto.setCoverName(String.valueOf(((LinkedHashMap<?, ?>) product).get(AppConstant.STRING_PRODUCT_NAME)));
                                coverDetailMstDto.setCode(String.valueOf(((LinkedHashMap<?, ?>) product).get(AppConstant.STRING_CODE)));
                                if (null != coverDetailMstDto.getCoverName() && null != coverDetailMstDto.getCode()) {
                                    coverDetailMstDao.insertMaster(connection, coverDetailMstDto);
                                    selectedCovers.add(coverDetailMstDto.getCode());
                                }
                                break;
                            case "conditionData":
                                ConditionAndExclusionDto conditionAndExclusionDto = new ConditionAndExclusionDto();
                                conditionAndExclusionDto.setcAndEName(String.valueOf(((LinkedHashMap<?, ?>) product).get(AppConstant.STRING_PRODUCT_NAME)));
                                conditionAndExclusionDto.setCode(String.valueOf(((LinkedHashMap<?, ?>) product).get(AppConstant.STRING_CODE)));
                                if (null != conditionAndExclusionDto.getcAndEName() && null != conditionAndExclusionDto.getCode()) {
                                    conditionAndExclusionDetailDao.insertMaster(connection, conditionAndExclusionDto);
                                    selectedCovers.add(conditionAndExclusionDto.getCode());
                                }
                                break;
                            case "specialPackageData":
                                SpecialPackagesDto specialPackagesDto = new SpecialPackagesDto();
                                specialPackagesDto.setName(String.valueOf(((LinkedHashMap<?, ?>) product).get(AppConstant.STRING_PRODUCT_NAME)));
                                specialPackagesDto.setCode(String.valueOf(((LinkedHashMap<?, ?>) product).get(AppConstant.STRING_CODE)));
                                if (null != specialPackagesDto.getName() && null != specialPackagesDto.getCode()) {
                                    specialPackageDao.insertMaster(connection, specialPackagesDto);
                                    selectedCovers.add(specialPackagesDto.getCode());
                                }
                                break;
                            default:
                                throw new ErrorMsgException("ERROR", "Save failed, Error Occurred!");
                        }
                    }
                }
            }
            if (!selectedCovers.isEmpty()) {
                coverDetailMstDao.updateAllChargesAndDiscountDetailByListIn(connection, selectedCovers, AppConstant.YES);
                coverDetailMstDao.updateAllBenefitCoverLoadingDetailMaterByListIn(connection, selectedCovers, AppConstant.YES);
                coverDetailMstDao.updateAllCWEDetailMaterByListIn(connection, selectedCovers, AppConstant.YES);
                coverDetailMstDao.updateAllSRCCTcDetailMaterByListIn(connection, selectedCovers, AppConstant.YES);
            }
            commitTransaction(connection);
        } catch (ErrorMsgException e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new ErrorMsgException(e.getField(), e.getErrorMessage());
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }
}
