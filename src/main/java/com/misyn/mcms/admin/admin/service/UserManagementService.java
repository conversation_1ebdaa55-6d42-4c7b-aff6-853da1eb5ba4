package com.misyn.mcms.admin.admin.service;

import com.misyn.mcms.admin.admin.dto.*;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.ListItemDto;
import com.misyn.mcms.claim.dto.UserDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface UserManagementService {

    List<ClaimChannelDto> getChannelsForTeam(Integer teamId) throws Exception;

    List<ChannelTeamGridDto> getTeamList() throws Exception;

    List<UserDto> getMembersForTeam(Integer teamId) throws Exception;

    List<ListItemDto> fetchSpecialTeam() throws Exception;

    List<ListItemDto> fetchApprovalUserList(Integer claimNo) throws Exception;

    ChannelTeamDto getTeamInfo(Integer teamId) throws Exception;

    List<ClaimChannelDto> getChannelsNotInTeams() throws Exception;

    void saveNewTeam(String teamName, String channelDesc, List<Integer> ids) throws Exception;

    List<Integer> getSelectedList(String regex, String ids) throws Exception;

    void addChannelsForTeam(Integer teamId, List<Integer> selectedList) throws Exception;

    List<ChannelTeamDto> getAllTeamDetails() throws Exception;

    boolean checkTeamName(String teamName) throws Exception;

    boolean updateTeamDetails(ChannelTeamDto channelTeamDto) throws Exception;

    List<BranchDetailDto> getAllActiveBranchDetails();

    BranchDetailDto getBranchDetail(String branchCode);

    DataGridDto getBranchDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField);

    void updateBranchDetails(String branchCode, String branchCity, String branchName, UserDto user) throws Exception;

    boolean isAlreadySavedBranch(String branchCode);

    void saveBranchDetails(String branchCode, String branchName, String branchCity, UserDto user) throws Exception;

    void deleteSelectedBranches(String selectedBranchesCode, UserDto user) throws Exception;

    List<CoverDetailMstDto> getBenefitCoverLoadingDetail(String code, String name) throws Exception;

    List<CoverDetailMstDto> getCWEDetail(String code,String name) throws Exception;

    List<CoverDetailMstDto> getChargesAndDiscountDetail(String code,String name) throws Exception;

    ProductDetailListDto getProductDetailById() throws Exception;

    ProductDetailListDto getProductDetailByName(String productName) throws Exception;

    void saveProductDetail(Map<String,Object> productDetailListDto) throws Exception;

    List<CoverDetailMstDto> getSalientMasterDetail(String code,String name,String tableName) throws Exception;

    List <CoverDetailMstDto> getSrccTcDetail(String code,String name) throws Exception;
}
